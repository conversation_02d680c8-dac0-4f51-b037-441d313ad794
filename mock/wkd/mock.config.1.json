{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "68d8e458-3731-43ad-bc01-9330aa5bba9b", "ruleName": "寄件-快递员是否开启在线支付", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/v1/WeApp/isOpenOnlinePay", "filterId": "8440a40c-12fa-4a54-b4c5-379b04aa1679", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":0\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "81a7c770-17f6-45ff-ad71-476b3d7b5235", "ruleName": "寄件-提交优寄订单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Yj/Order/submit", "filterId": "5d8c3591-4cb0-4072-9fb1-427c94362b91", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"grab_id\":0,\n        \"order_id\":\"706153671709649\",\n        \"wx_after_pay\":1,\n        \"wx_after_pay_bind\":1\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e8fc1c98-9f0f-4787-908c-94aec1e4a43d", "ruleName": "寄件-报价单接口", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Yj/Order/quotation", "filterId": "8506d655-96c3-49e8-b78a-09d2ee0de976", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"quotation\":[\n            {\n                \"price\":7,\n                \"discount_price\":7,\n                \"discount_total_amount\":0,\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":7,\n                \"s_fee\":2,\n                \"s_total_fee\":0,\n                \"discount_list\":[\n\n                ],\n                \"brand\":\"yt\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"brand\":\"jd\",\n                \"available\":1,\n                \"unavailable_msg\":\"\",\n                \"tips\":\"该渠道默认产生1元保价费，请确认接受后下单\"\n            },\n            {\n                \"price\":8,\n                \"discount_price\":8,\n                \"discount_total_amount\":0,\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":8,\n                \"s_fee\":1.5,\n                \"s_total_fee\":0,\n                \"discount_list\":[\n                    {\n                        \"type\":\"spring\",\n                        \"desc\":\"寄件有机会领红包\"\n                    }\n                ],\n                \"brand\":\"sto\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":8,\n                \"discount_price\":8,\n                \"discount_total_amount\":0,\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":0,\n                \"s_fee\":0,\n                \"s_total_fee\":0,\n                \"discount_list\":[\n\n                ],\n                \"brand\":\"yd\",\n                \"available\":0,\n                \"unavailable_msg\":\"该地址不在韵达快递配送范围\"\n            },\n            {\n                \"price\":8,\n                \"discount_price\":8,\n                \"discount_total_amount\":0,\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":8,\n                \"s_fee\":1,\n                \"s_total_fee\":0,\n                \"discount_list\":[\n\n                ],\n                \"brand\":\"jt\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":9,\n                \"discount_price\":9,\n                \"discount_total_amount\":0,\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":9,\n                \"s_fee\":1.5,\n                \"s_total_fee\":0,\n                \"discount_list\":[\n\n                ],\n                \"brand\":\"dp\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":12,\n                \"discount_price\":12,\n                \"discount_total_amount\":0,\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":12,\n                \"s_fee\":2,\n                \"s_total_fee\":0,\n                \"discount_list\":[\n\n                ],\n                \"brand\":\"sf\"\n            }\n        ],\n        \"is_jd_sort\":1\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "00dc61ad-4e19-4675-bf56-5645b6c94889", "ruleName": "寄件-是否开启支付分", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_order_core/v2/mina/Payment/getWxPayServiceStatus", "filterId": "2ca476e1-eb84-4d84-bc52-e5bd41e2b2d0", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"serviceStatus\":true\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "bcc5d469-140a-45ed-adff-a0d0d6a86c3e", "ruleName": "寄件-是否关注微快递公众号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Account/isFocusWkdGzh", "filterId": "a489c8ce-cb01-4dd5-a1e3-e1db7441c154", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"is_focus\":false\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8a25fdf2-eb89-4ffe-b0ce-d66c4d630531", "ruleName": "寄件-是否关注微快递会员号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Account/platformUserSubscribeStatus", "filterId": "4159a6f0-1785-4915-8fd3-164cb3142dae", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"wt_weixin_162\":\"1\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}