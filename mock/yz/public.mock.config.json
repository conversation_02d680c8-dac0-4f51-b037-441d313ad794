{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "9d554478-53ca-4865-9b72-3a90735cf9ee", "ruleName": "公共-关注公众号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/followStatus", "filterId": "e165ab54-e26a-4410-9517-d754c92edbb1", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\n\"code\": 0,\n\n\"msg\": \"成功\",\n\n\"data\": {\n\n \"follow_status\": \"0\",\n\n \"gzh_name\": \"快宝驿站华东\",\n\n \"follow_url\": \"https:\\/\\/mp.weixin.qq.com\\/s\\/23wtSmXyLF3wP5ojqLftlA\"\n\n}\n\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "0ad972fd-cebc-48b1-a432-eb36e130c282", "ruleName": "公共-添加下单关系", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_mp/api/weixin/mini/minpost/relation/addRelation", "filterId": "eb2fc47d-040e-4d28-b834-7a7184de5746", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{},\n    \"code\":1,\n    \"msg\":\"错误的下单对象，请重试\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "bc5263e2-7270-4702-8308-6e233c74fea0", "ruleName": "公共-流量主广告", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/Ad/showFlowAd", "filterId": "bf79c55a-613f-4ed4-8a9f-626615c1f3f7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n \"code\": 0,\n \"msg\": \"成功\",\n \"data\": {\n  \"check_piece_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"logistics_details_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"subscribe_get_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"order_submit_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"stage_home_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"order_details_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\"\n  }\n }\n\n\t},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "dd049588-9b8f-46ae-ac75-4e78e8346161", "ruleName": "公共-驿站信息", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/record/dakInfo", "filterId": "a42baeea-c406-4803-a71f-4449279e7ce8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\t\"code\": 10,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"id\": \"125873\",\n\t\t\"cm_id\": \"1870621\",\n\t\t\"kb_id\": \"27994298\",\n\t\t\"avator_url\": \"\",\n\t\t\"inn_logo\": \"\",\n\t\t\"inn_name\": \"sunny123测试\",\n\t\t\"concat_name\": \"束霞\",\n\t\t\"realname\": \"\",\n\t\t\"concat_phone\": \"18505650937\",\n\t\t\"concat_area\": \"上海市 上海市 长宁区\",\n\t\t\"concat_street\": \"\",\n\t\t\"concat_location\": \"通协路269号\",\n\t\t\"workday\": \"周一至周日\",\n\t\t\"start_time\": \"09:00\",\n\t\t\"end_time\": \"18:00\",\n\t\t\"openid\": \"\",\n\t\t\"unionid\": null,\n\t\t\"create_at\": \"2019-04-16 16:14:40\",\n\t\t\"update_at\": \"2022-01-18 17:51:19\",\n\t\t\"last_login_time\": \"2022-01-18 17:51:19\",\n\t\t\"nickname\": \"\",\n\t\t\"gender\": \"0\",\n\t\t\"city\": \"上海市\",\n\t\t\"province\": \"上海市\",\n\t\t\"country\": \"\",\n\t\t\"is_disable\": \"0\",\n\t\t\"is_auth\": \"0\",\n\t\t\"is_slave\": \"0\",\n\t\t\"app_openid\": \"\",\n\t\t\"phone\": \"18505650937\",\n\t\t\"ali_openid\": \"\",\n\t\t\"ali_open\": \"0\",\n\t\t\"longitude\": \"121.356\",\n\t\t\"latitude\": \"31.2259\",\n\t\t\"is_pass\": \"0\",\n\t\t\"channel\": \"\",\n\t\t\"province_code\": \"310000000000\",\n\t\t\"city_code\": \"310100000000\",\n\t\t\"district_code\": \"310105000000\",\n\t\t\"town_code\": \"310105102000\",\n\t\t\"district\": \"长宁区\",\n\t\t\"town\": \"新泾镇\",\n\t\t\"waybill_time\": null,\n\t\t\"area_type\": \"办公区\",\n\t\t\"league_id\": \"0\",\n\t\t\"is_special\": \"0\",\n\t\t\"station_code\": \"2001809\",\n\t\t\"remark\": \"\",\n\t\t\"dak_auth_status\": 1,\n\t\t\"is_vip\": 0\n\t}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "59d0e184-8fcc-4d26-9bdc-a7e663059328", "ruleName": "公共-解析驿站二维码", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/handle/QrCode/innInfoByCnt", "filterId": "1578fc83-978b-4070-be43-6a580ac3564d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n      \"code\":1,\n      \"data\":{\n          \"cmId\":2225571\n      },\n      \"msg\":\"错误信息。弹出来给我瞧一瞧啊！！\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6bcdacb7-2e60-4302-9020-be3cb17b1140", "ruleName": "公共-广告", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/Ad/getAdBannerList", "filterId": "93615915-1878-4928-8787-8069ae721fc4", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":[{\"app_id\":null,\"original_id\":\"\",\"imgUrl\":null,\"adUrl\":null},{\"app_id\":null,\"original_id\":\"\",\"imgUrl\":null,\"adUrl\":null}]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ef6a0e3c-197f-415c-aad0-5991b9265901", "ruleName": "公共-关注", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/followStatus", "filterId": "9746f161-1278-4fbf-a232-b031c7311a2f", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":\n    {\n      \"follow_status\":0,\n      \"gzh_name\":\"快递驿站\",\n      \"fllow_url\":\"https://kd1.cn/gz1-4\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "107dd858-0692-486f-a609-1cf42acf7f44", "ruleName": "公共-实名信息", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/realname/auth/realNameInfo", "filterId": "0174b816-0881-4cec-b789-6b7624a93fd4", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0 ,\n  \"data\":{\n    \"realname_status\":1,\n    \"name\":\"李大钊\",\n    \"no\":360622199702187716,\n    \"pSrc\":true,\n    \"nSrc\":false,\n    \"status\":\"已认证\"\n  }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ec6e0a11-e999-4626-a534-b4a9ed98186e", "ruleName": "公共-获取手机号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Config/userCenter", "filterId": "8244fe77-5487-409e-9595-fe84e830b180", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"mobile\":\"\",\"points\":0,\"pay_status\":\"0\",\"coupon_count\":2,\"show_vip\":0,\"show_box\":0,\"nickname\":\"W\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "513aa25b-7faf-46ab-a6c4-92c3eb0a0cb8", "ruleName": "公共-首页广告位", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/getAds", "filterId": "8be1b048-418a-4a17-90d1-2fed91fab4b6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n      \"count\": 4,\n      \"list\": [\n        {\n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"微掌柜app新版上线\"\n        },\n        { \n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"微掌柜网页版\"\n        },\n        {\n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"电商品台一键导入\"\n        },\n        {\n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"天天打卡领现金\"\n        }\n      ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ea784a0c-b4de-44c1-bca7-b3c4f57492e8", "ruleName": "公共-授权手机号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/bindMobileByAlipayMini", "filterId": "c6291e21-ae11-4a45-883e-e154bdd0e827", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\": 0,\n    \"data\": {\n      \"mobile\": \"\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d3f684f3-4872-409b-b381-90d471600b9f", "ruleName": "公共-登录", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "weixin/mini/login/index", "filterId": "40e19ea1-9f87-4d4d-853e-ec3ffd7f9c24", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n   \"data\": {\n    \"code\":0,\n    \"data\":{\n      \"sessionid\": \"b997a9559a14f789df8f61ee354abc97\"\n     }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}