{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "4d8cf7a0-b2dc-4690-8f8d-bac36b0b0ec5", "ruleName": "寄件-在线支付", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/supportPay", "filterId": "94766b6e-8532-456d-883c-e13b8d80e94b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":true},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code|1\":[0,1003],\"msg\":\"成功\",\"data\":true\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "39ff4c5c-cca0-489b-9887-dbe5e310c1eb", "ruleName": "寄件-驿站增值服务", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/order/getDakValueaddConfig", "filterId": "1f7ffc3c-ba73-40ae-9d2b-6ad0591512e0", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n\t\t\"courierId\":\"1957205\",\n    \"cost\":\"0.01\",\n\t\t\"proPriceStart\":\"101.00\",\n\t\t\"proPriceEnd\":\"510.11\",\n\t\t\"isDelivery\":\"1\",\n    \"isDesPay\":\"1\",\n\t\t\"keepPrice\":0,\n    \"isDecVal\":\"1\",\n\t\t\"isProPrice\":\"0\",\n    \"isToDoor\":\"1\",\n\t\t\"allowReserveTime\":\"1\",\n\t\t\"isFresh\":\"1\",\n    \"isDeclared\":\"1\",\n    \"declaration\":\"浪里个浪\",\n    \"reserve_start_time\":\"11:00\",\n    \"reserve_end_time\":\"18:00\"\n    }},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n     \"code|1\":[0,1003],\n    \"msg\":\"成功\",\n    \"data\":{\n\t\t\"courierId\":\"1957205\",\n    \"cost\":\"0.01\",\n\t\t\"proPriceStart\":\"101.00\",\n\t\t\"proPriceEnd\":\"510.11\",\n\t\t\"isDelivery\":\"1\",\n    \"isDesPay\":\"1\",\n\t\t\"keepPrice\":0,\n    \"isDecVal\":\"1\",\n\t\t\"isProPrice\":\"0\",\n    \"isToDoor\":\"1\",\n\t\t\"allowReserveTime\":\"1\",\n\t\t\"isFresh\":\"1\",\n    \"isDeclared\":\"1\",\n    \"declaration\":\"浪里个浪\",\n    \"reserve_start_time\":\"11:00\",\n    \"reserve_end_time\":\"18:00\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b2f73f9e-6cc3-4942-9d27-e75040083c6d", "ruleName": "寄件-获取打赏记录", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/Reward/rewardRecord", "filterId": "4b07520c-5fc4-4f73-b7c5-e9e8ac6d7b73", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"count\":\"3\",\n        \"currentPageNum\":\"1\",\n        \"totalPageNum\":1,\n        \"pageSize\":\"10\",\n        \"list\":[\n            {\n                \"money\":\"0.10\",\n                \"success_time\":\"2021-11-19 17:48:46\",\n                \"donate_desc\":\"打赏给dyw快递员\"\n            },\n            {\n                \"money\":\"0.10\",\n                \"success_time\":\"2021-11-19 17:45:58\",\n                \"donate_desc\":\"打赏给dyw快递员\"\n            },\n            {\n                \"money\":\"0.10\",\n                \"success_time\":\"2021-11-19 17:40:49\",\n                \"donate_desc\":\"打赏给驿站\"\n            }\n        ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\n      \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"count\":\"3\",\n        \"currentPageNum\":\"1\",\n        \"totalPageNum\":1,\n        \"pageSize\":\"10\",\n        \"list|4\":[\n            {\n                \"money|1-100\":1,\n                \"success_time\":\"@datetime()\",\n                \"donate_desc\":\"@csentence()\"\n            },\n            \n        ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "310e9f2c-ea17-4a92-980f-0d305b85025e", "ruleName": "寄件-快递员主页", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/relation/courierCardInfo", "filterId": "18a23b87-b180-4eb5-86a2-b8b26a9781a7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"未获得快递员的信息\",\"data\":{\n    \"focusStatus\":1,\n    \"payStatus\":1,\n    \"phone\":157071040823, \"shop\":12, \"name\":\"测试\",\n    \"service_count_sum\":12,\n    \"service_count\":23,\n    \"courier_id\":123,\n    \"service_length\":23\n  }},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"未获得快递员的信息\",\"data\":{\n    \"focusStatus\":1,\n    \"payStatus\":1,\n    \"phone\":157071040823, \"shop\":12, \"name\":\"测试\",\n    \"service_count_sum\":12,\n    \"service_count\":23,\n    \"courier_id\":123,\n    \"service_length\":23\n  }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "99514fe2-e9b1-47b1-b10b-5be5789814e9", "ruleName": "寄件-打印", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/newOrderPrint", "filterId": "b572d94a-2aac-4d63-b422-7a16b03e8942", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code|1\":[0,1003],\n    \"msg\":\"成功\",\n    \"data\":{}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "907cece5-85e3-41ea-b3f0-f91ff9afce71", "ruleName": "寄件-优惠券列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/CourierEquityCard/userEquityCardList", "filterId": "1a32fcd8-7b66-4536-b84a-89f6d31ff285", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\":{\"code\":0,\"msg\":\"成功\",\"data\":{\"count\":8,\"page\":\"1\",\"pageSize\":\"30\",\n  \"list\":[{\"record_id\":\"167\",\n  \"card_name\":\"sunny\",\n  \"card_type\":\"coupon\",\n  \"start_time\":\"2020-03-15 12:42\",\n  \"discount_fee\":0.02,\n  \"regional_restriction\":[\n    \"上海市\",\"浙江省\",\"江苏省\",\"山东省\",\n    \"安徽省\",\"福建省\",\"江西省\",\"湖南省\",\n    \"湖北省\",\"河南省\"]\n    ,\"invalid_time\":\"2022-03-10 21:13:57\",\n    \"residue_num\":\"12\",\n    \"customer_id\":\"0\",\n    \"s_id\":\"67891787\",\n    \"s_type\":\"yz\",\n    \"dakName\":\"sunny123测试\",\n    \"dak_id\":\"2225144\"\n    }]\n  }\n  },\n\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}\n\n\n"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":\n  {\n  \"code\":0,\n  \"msg\":\"成功\",\n  \"data\":{\n    \"count\":8,\n    \"page\":\"1\",\n    \"pageSize\":\"30\",\n    \"list|10\":[\n      {\n      \"record_id\":\"167\",\n      \"card_name\":\"sunny\",\n      \"card_type\":\"coupon\",\n      \"start_time\":\"2020-03-15 12:42\",\n      \"discount_fee\":0.02,\n      \"regional_restriction|5-10\":[\"@province\"]\n      ,\"invalid_time\":\"@datetime\",\n      \"residue_num|1-100\":1,\n      \"customer_id\":\"@id\",\n      \"s_id\":\"@id\",\n      \"s_type\":\"yz\",\n      \"dakName\":\"@name\",\n      \"dak_id\":\"@id\"\n      }\n    ]}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a8c435a6-cfe4-404d-b393-a3303bdd6c95", "ruleName": "寄件-提交订单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/weixin/mini/minpost/order/add", "filterId": "1d04aada-7033-45c4-bbd1-7436e044605c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"order_id\":\"702095766707693\",\"third_order_id\":\"702095766707693\",\"third_platform\":\"dak\",\"order_platform\":\"minpost\",\"send_mobile\":\"18715276649\",\"send_tel\":\"\",\"send_province\":\"上海市\",\"collect_code\":\"28602636\",\"brand\":\"zt\",\"brand_name\":\"中通快递\",\"waybill\":\"23521234321523\",\"collection\":\"\",\"orderPrice\":0,\"courier_name\":\"\",\"courier_mobile\":\"\",\"courier_id\":0,\"dak_id\":2225571,\"realNameCity\":\"上海市\",\"agent_id\":\"\",\"dak_name\":\"测试123\",\"dak_mobile\":\"17596123053\",\"warrant_price\":0,\"pro_price\":0,\"open_pay_type\":\"public_pay\",\"weight\":0,\"support_pay\":true,\"support_print\":2,\"is_custom\":false,\"closeAd\":0}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\"msg\":\"成功\",\n    \"data\":{\n      \"order_id\":\"702095766707693\",\n      \"third_order_id\":\"702095766707693\",\n      \"third_platform\":\"dak\",\n      \"order_platform\":\"minpost\",\n      \"send_mobile\":\"18715276649\",\n      \"send_tel\":\"\",\"send_province\":\"上海市\"\n      ,\"collect_code\":\"28602636\",\n      \"brand\":\"zt\",\"brand_name\":\"中通快递\",\n      \"waybill\":\"23521234321523\",\"collection\":\"\",\n      \"orderPrice\":0,\"courier_name\":\"\",\n      \"courier_mobile\":\"\",\"courier_id\":0,\n      \"dak_id\":2225571,\"realNameCity\":\"上海市\",\n      \"agent_id\":\"\",\"dak_name\":\"测试123\",\n      \"dak_mobile\":\"17596123053\",\n      \"warrant_price\":0,\"pro_price\":0,\n      \"open_pay_type\":\"public_pay\",\n      \"weight\":0,\"support_pay\":true,\n      \"support_print\":2,\n      \"is_custom\":false,\n      \"closeAd\":0}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "fdd63bfb-08ce-4933-ac10-e94228376dbc", "ruleName": "寄件-快递员卡片", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/relation/courierCardInfo", "filterId": "943da701-119b-47de-a760-02d7fa86e945", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\n       \"service_count\":95,\n        \"service_count_sum\":191,\n        \"nearest_distance\":\"\",\n        \"last_login_time\":[\n\n        ],\n        \"latest_messages\":[\n\n        ],\n        \"is_nearby\":true,\n        \"service_length\":754,\n        \"courier_id\":\"1614786\",\n        \"phone\":\"***********\",\n        \"name\":\"丁*成\",\n        \"account_company\":\"sto\",\n        \"account_shop\":\"甘肃临夏公司\",\n        \"location\":\"甘肃省-临夏回族自治州-东乡族自治县\",\n        \"index_shop_id\":\"200\",\n        \"is_focused\":true,\n        \"type\":\"courier\",\n        \"focusStatus\":\"1\",\n        \"payStatus\":\"1\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n     \"code\":0,\n    \"data\":{\n       \"service_count\":95,\n        \"service_count_sum\":191,\n        \"nearest_distance\":\"\",\n        \"last_login_time\":[\n\n        ],\n        \"latest_messages\":[\n\n        ],\n        \"is_nearby\":true,\n        \"service_length\":754,\n        \"courier_id\":\"1614786\",\n        \"phone\":\"***********\",\n        \"name\":\"丁*成\",\n        \"account_company\":\"sto\",\n        \"account_shop\":\"甘肃临夏公司\",\n        \"location\":\"甘肃省-临夏回族自治州-东乡族自治县\",\n        \"index_shop_id\":\"200\",\n        \"is_focused\":true,\n        \"type\":\"courier\",\n        \"focusStatus\":\"1\",\n        \"payStatus\":\"1\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "44e728ec-592e-41c2-9926-eceb5d32e8c9", "ruleName": "寄件-解析身份证", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/v1/WeApp/parseRealName", "filterId": "668eb1a3-b79e-4842-bd2e-7f1d79453234", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\n      \"name\":\"213\",\n      \"end\":\"********\",\n      \"start\":\"********\",\n      \"police\":\"随便来一个\",\n            \"pSrc\":\"https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/share-waybill.png\",\n      \"nSrc\":\"https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/share-waybill.png\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\n    \"code\":0,\n    \"data\":{\n      \"name\":\"213\",\n      \"end\":\"********\",\n      \"start\":\"********\",\n      \"police\":\"随便来一个\",\n            \"pSrc\":\"https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/share-waybill.png\",\n      \"nSrc\":\"https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/share-waybill.png\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8f2fa632-893b-4075-801c-443989460966", "ruleName": "寄件-驿站可用积分", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/cloudPrint/order/v1/pay/NewVipPoints/userVipPoints", "filterId": "abb819ea-0dac-4c15-bd2f-d6cb0dded890", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\n\t\"data\": {\n\t\t\"inn\": {\n\t\t\t\"id\": \"49\",\n\t\t\t\"points\": 10,\n\t\t\t\"money\": \"0\",\n\t\t\t\"integral_name\": \"测试123\"\n\t\t},\n\t\t\"city_shop\": {\n\t\t\t\"id\": \"24\",\n\t\t\t\"platform\": \"wt_mini_55\",\n\t\t\t\"points\": 0,\n\t\t\t\"money\": \"0\",\n\t\t\t\"integral_name\": \"\"\n\t\t},\n\t\t\"max_points\": 30\n},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n\n\t\"data\": {\n\t\t\"inn\": {\n\t\t\t\"id\": \"49\",\n\t\t\t\"points\": 10,\n\t\t\t\"money\": \"0\",\n\t\t\t\"integral_name\": \"测试123\"\n\t\t},\n\t\t\"city_shop\": {\n\t\t\t\"id\": \"24\",\n\t\t\t\"platform\": \"wt_mini_55\",\n\t\t\t\"points\": 0,\n\t\t\t\"money\": \"0\",\n\t\t\t\"integral_name\": \"\"\n\t\t},\n\t\t\"max_points\": 30\n},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "9f4c2469-05be-421f-84bd-617a044b45b6", "ruleName": "寄件-积分明细列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/cloudPrint/order/v1/pay/NewVipPoints/userVipPointsDetailList", "filterId": "3205b914-84fc-48a4-8d5a-ee004b074ae7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\"page\":1,\"pageSize\":30,\"list\":[\n    {\"create_time\":\"2020-12-23\",\"id\":2314231,\"s_kb_id\":214,\"points\":231,\"flow_sign\":\"+\",\"flow_desc\":\"测试1\"},\n    {\"create_time\":\"2020-12-13\",\"id\":231431,\"s_kb_id\":214,\"points\":231,\"flow_sign\":\"+\",\"flow_desc\":\"测试2\"},\n    {\"create_time\":\"2020-12-03\",\"id\":234231,\"s_kb_id\":2141,\"points\":31,\"flow_sign\":\"-\",\"flow_desc\":\"测试3\"}]}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\"page\":1,\"pageSize\":30,\"list|1-10\":[\n    {\n      \"create_time\":\"@date()\",\n      \"id\":\"@id\",\n      \"s_kb_id|1-100\":1,\n      \"points|1-200\":1,\n      \"flow_sign|1\":[\"+\",\"-\"],\n      \"flow_desc\":\"测试1\"},\n    ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "21d3176e-9f24-4464-9910-186a5d685ba1", "ruleName": "寄件-积分列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/cloudPrint/order/v1/pay/NewVipPoints/userVipPointsList", "filterId": "fe4f61b7-05ce-4c33-93b3-22444b333648", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"不支持该平台\",\n    \"data\":{\n      \"inn\":[{\"points\":21314,\"sKbId\":2341,\"integral_name\":\"测试积分\"}],\n      \"city_shop\":[{\"points\":2134,\"sKbId\":2341}]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"不支持该平台\",\n    \"data\":{\n      \"inn\":[{\"inn_name\":\"测试1\",\"dak_id\":2225293,\"points\":4},{\"inn_name\":\"测试2\",\"dak_id\":2225293,\"points\":234},{\"inn_name\":\"测试3\",\"dak_id\":2225293,\"points\":34},{\"inn_name\":\"测试4\",\"dak_id\":160033,\"points\":23}],\n      \"city_shop\":[{\"points\":2134,\"sKbid\":2341}]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\n    \"code\":0,\n    \"msg\":\"不支持该平台\",\n    \"data\":{\n      \"inn|5\":[{\"points\":\"@id\",\"sKbId\":\"@id\",\"integral_name\":\"@ctitle(5)\"}],\n      \"city_shop|1-10\":[{\"points|1-100\":1,\"sKbId\":\"@id\"}]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a92812c0-8d53-4857-b09d-7d8711eadfe4", "ruleName": "寄件-扫码寄", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/handle/QrCode/scan", "filterId": "fa13ebef-0dd5-4130-a9eb-c754d289fe72", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\":0,\"msg\":\"成功\",\n  \"data\":{\n    \"service\":\"ship_code\",\n    \"order_id\":\"\",\"waybill\":\"\",\n    \"brand\":\"jt\",\n    \"relation_info\":\n    {\"type\":\"dak\",\"brand\":\"\",\"dak_id\":2225571},\n    \"collect_code\":\"K174000426\",\n    \"courier_id\":0,\"customer_id\":0,\n    \"dak_id\":\"2225571\",\n    \"t\":\"\",\n    \"is_order_user\":1}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\"msg\":\"成功\",\n  \"data\":{\n    \"service\":\"ship_code\",\n    \"order_id\":\"\",\"waybill\":\"\",\n    \"brand\":\"jt\",\n    \"relation_info\":\n    {\"type\":\"dak\",\"brand\":\"\",\"dak_id\":2225571},\n    \"collect_code\":\"K174000426\",\n    \"courier_id\":0,\"customer_id\":0,\n    \"dak_id\":\"2225571\",\n    \"t\":\"\",\n    \"is_order_user\":1}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "275366a0-de15-4e42-bfc3-601d13d7d416", "ruleName": "寄件-默认下单关系", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/relation/getDefaultRelation", "filterId": "2f166c75-d224-4f61-9991-b35b37d7b14d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": null\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\n    \"code|1\": [\"0\",\"1003\"],\n    \"msg\": \"成功\",\n    \"data\": null\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "898647e9-1c56-4c66-b8bd-33f52b931764", "ruleName": "寄件-品牌", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/relation/supportBrand", "filterId": "db61abae-e230-4b0b-be78-eeac467072be", "matchType": "regExp"}, {"propName": "", "propRegString": "", "filterId": "99c07da9-38bd-41b8-85d7-dcc16994301a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"data\":[\n\n                    ]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"data\":[]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1a3e4401-2984-41d1-acc5-c06e152873be", "ruleName": "寄件-报价单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/getQuotationPrice", "filterId": "c0161462-6270-4c09-b7af-6909caed90ad", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"data\":{\"price\":11,\"f_weight\":1,\"s_weight\":2,\"f_kg\":1,\"s_kg\":2},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"data\":{\"price|1-100\":1,\"f_weight|1-20\":1,\"s_weight|1-20\":1,\"f_kg|1-20\":1,\"s_kg|1-20\":1},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "561c8218-4e03-4767-a7b0-84937c2e448b", "ruleName": "寄件-权益次卡-选择", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/CourierEquityCard/userEquityCardList", "filterId": "32f8af1c-8bdc-46fb-a27d-c458678cc688", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"count\":3,\n        \"page\":\"1\",\n        \"pageSize\":\"5\",\n        \"list\":[\n          {\n            \"card_id\":\"2959736\",\n            \"card_name\":\"优惠券\",\n            \"card_type\":\"discount_card\",\n            \"discount_fee\":2.20,\n            \"regional_restriction\":[\"all\"],\n            \"invalid_time\":\"2021-06-13 09:58:22\",\n            \"residue_num\":\"1\",\n            \"s_id\":\"1094099\",\n            \"s_type\":\"yz\",\n           \"dakName\":\"丹水快递驿站\"}\n          ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"count\":3,\n        \"page\":\"1\",\n        \"pageSize\":\"5\",\n        \"list｜1-10\":[\n          {\n            \"card_id\":\"@id\",\n            \"card_name\":\"优惠券\",\n            \"card_type|1\":[\"discount_card\",\"coupon\"],\n            \"discount_fee|1-10\":1,\n            \"regional_restriction\":[\"all\"],\n            \"invalid_time\":\"@datetime()\",\n            \"residue_num|1-10\":\"1\",\n            \"s_id\":\"@id\",\n            \"s_type\":\"yz\",\n           \"dakName\":\"@name\"}\n          ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}