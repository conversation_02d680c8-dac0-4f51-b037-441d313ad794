{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "e2468ce1-46c4-48b4-a7ab-0e92ea8db2f2", "ruleName": "我的-我的信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Config/userCenter", "filterId": "3cfab28e-4060-418b-b681-964623fc9aa8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"mobile\":\"157****8042\",\"points\":0,\"pay_status\":\"0\",\"coupon_count\":0,\"show_vip\":0,\"show_box\":0,\"nickname\":\"\",\"avatar_url\":\"https:\\/\\/thirdwx.qlogo.cn\\/mmopen\\/vi_32\\/SIJxEcpKygGrib6IQu36RiagicgwGXPKibvFNCUlzWyMQ7pqOq5ck3jia51Es0CW19v2sR1WFyrbkicnRawnahGZbiakA\\/132\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"mobile\":\"157****8042\",\"points\":0,\"pay_status\":\"0\",\"coupon_count\":0,\"show_vip\":0,\"show_box\":0,\"nickname\":\"\",\"avatar_url\":\"https:\\/\\/thirdwx.qlogo.cn\\/mmopen\\/vi_32\\/SIJxEcpKygGrib6IQu36RiagicgwGXPKibvFNCUlzWyMQ7pqOq5ck3jia51Es0CW19v2sR1WFyrbkicnRawnahGZbiakA\\/132\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "3f8181eb-1e88-4ebf-be74-ec3dda3b073c", "ruleName": "我的-更新手机号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/upBindMobileByMini", "filterId": "8500e271-80b8-4e4e-87cb-ac5083c84dad", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\"mobile\":15707018042}\n\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "519d248a-9d3b-4030-a01d-1def991b32bd", "ruleName": "我的-联系客服", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/ContactCustom/weixin", "filterId": "e58cf47e-f9e6-4e24-acea-c092a63b0020", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"url\":\"\",\"services_phone\":[15707018042,16606083322]}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "5d3af39d-476a-4174-9698-b2569f118a02", "ruleName": "我的-平台驿站列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/City/puisnePostList", "filterId": "96da9784-e242-4b8e-9d6c-4ac7cca5f4b6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":[{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":15707018042},{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":157807018042},{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":157807018042},{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":157807018042}],\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8a95f2cd-8bdb-49b7-b583-e967a40bbf5e", "ruleName": "我的-积分配置", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/v1/pay/NewVipPoints/getPointsConfig", "filterId": "0ab34227-4dc1-476c-82d0-c9bc59ce1957", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\n      \"new_user\":{\n        \"point\":13,\n        \"status\": \"1\"\n      },\n       \"online_pay\":{\n        \"point\":11,\n        \"status\": \"1\"\n      },\n       \"bind_phone\":{\n        \"point\":12,\n        \"status\": \"1\"\n      }\n    },\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "452f9e5a-7b40-41d7-9a44-cd45f0b0245a", "ruleName": "我的-是否存在新券", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/CourierEquityCard/getIsHasCoupon", "filterId": "88cc33a2-ad85-4bb8-8384-71231f8a34d1", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":\"0\",\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}