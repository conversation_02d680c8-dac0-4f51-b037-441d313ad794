{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "bcbe7135-abf1-4d0f-be10-e92b7575b6aa", "ruleName": "订单-取消订单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/order/cancel", "filterId": "0136e0d2-1222-430e-8022-321f690f540a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f524be95-0835-441d-b739-24a7990e0f8e", "ruleName": "订单-填写订单到付金额", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/setOrderArrivePrice", "filterId": "335fdf6c-20b2-453d-88f6-78fefc281573", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"无权操作该订单\",\"data\":{}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\"code｜1\":[0,1000],\"msg\":\"无权操作该订单\",\"data\":{}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d0dd2263-48c4-4516-9784-81e20ea6ca2d", "ruleName": "订单-我收到的快递订单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/MiniDak/receiverOrderList", "filterId": "4ac6edc3-72d8-47d4-b257-5a29e0c76790", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"ok\",\n  \"data|5-20\":[\n    {\n      \"id\":\"609085938100746\",\n      \"order_id\":\"609085938100746\",\n      \"waybill\":\"\",\n      \"brand\":\"zt\",\n      \"send_name\":\"哈哈\",\n      \"send_province\":\"上海市\",\n      \"send_city\":\"上海市\",\n      \"receive_name\":\"fds\",\n      \"receive_province\":\"上海市\",\n      \"receive_city\":\"上海市\",\n      \"goods_name\":\"日用品\",\n      \"order_status\":1,\n      \"print_status\":0,\n      \"collect_code\":\"28602635\",\n      \"create_time\":\"2021-09-08 16:29:41\",\n      \"third_order_id\":\"609085938100746\",\n      \"status\":\"已下单\",\n      \"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\n      \"orderPrice\":\"0.01\",\n      \"source\":\"inn-mini\",\n      \"channel\":\"minpost\",\n      \"collect_object_type\":\"dak\",\n      \"collect_courier_id\":\"2225571\",\n      \"collect_courier_mobile\":\"17596123053\",\n      \"collect_courier_name\":\"测试123\",\n      \"placeOrderConfig\":null,\n      \"sourceText\":\"来自驿站小程序订单\"\n      }\n    ]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":  {\n    \"code\":0,\n    \"msg\":\"ok\",\n  \"data|5-20\":[\n    {\n      \"id\":\"@id\",\n      \"order_id\":\"@id\",\n      \"waybill\":\"@id\",\n      \"brand|1\":[\"zt\",\"sto\",\"sf\",\"jd\",\"dp\",\"yd\",\"sfky\",\"yzd\",\"jt\"],\n      \"send_name\":\"@name\",\n      \"send_province\":\"@province\",\n      \"send_city\":\"@city\",\n      \"receive_name\":\"@name\",\n      \"receive_province\":\"@province\",\n      \"receive_city\":\"@city\",\n      \"goods_name\":\"@name\",\n      \"order_status|1\":[1,0,2],\n      \"print_status|1\":[0,1],\n      \"collect_code\":\"@id\",\n      \"create_time\":\"@datetime\",\n      \"third_order_id\":\"@id\",\n      \"status|1\":[\"已下单\",\"已退款\",\"已送达\"],\n      \"last_logistics\":\"@csentence\",\n      \"orderPrice|1-10\":1,\n      \"source\":\"inn-mini\",\n      \"channel\":\"minpost\",\n      \"collect_object_type\":\"dak\",\n      \"collect_courier_id\":\"2225571\",\n      \"collect_courier_mobile\":\"\",\n      \"collect_courier_name\":\"测试123\",\n      \"placeOrderConfig\":null,\n      \"sourceText\":\"来自驿站小程序订单\"\n      }\n    ]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "bc763184-74f9-4f66-9316-635829b82799", "ruleName": "订单-订单详情", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/MiniDak/orderInfo", "filterId": "3b92139b-096d-40e2-a84e-e32c5ca82b8b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":{\n        \"id\":\"@id\",\n        \"order_id\":\"@id\",\n        \"waybill\":\"@id\",\n        \"print_uid\":null,\n        \"print_platform\":null,\n        \"agent_guid\":null,\n        \"brand\":\"zt\",\n        \"kb_id\":\"115187280\",\n        \"send_name\":\"黄灯辉\",\n        \"send_mobile\":\"18573062808\",\n        \"send_tel\":null,\n        \"send_province\":\"湖南省\",\n        \"send_city\":\"岳阳市\",\n        \"send_district\":\"平江县\",\n        \"send_address\":\"三阳乡大众安置区\",\n        \"receive_name\":\"哆啦A梦\",\n        \"receive_mobile\":\"15521290447\",\n        \"receive_tel\":\"\",\n        \"receive_province\":\"广东省\",\n        \"receive_city\":\"广州市\",\n        \"receive_district\":\"白云区\",\n        \"receive_address\":\"大源街道田新路17号一楼103(哆啦A梦①号仓)\",\n        \"goods_name\":\"日用品\",\n        \"goods_remark\":\"\",\n        \"goods_weight\":null,\n        \"source\":\"ship_code\",\n        \"proof_image\":\"\",\n        \"collect_code\":\"64E02179\",\n        \"print_time\":null,\n        \"create_time\":\"2021-12-08 15:04:56\",\n        \"dak_id\":\"1325554\",\n        \"courier_id\":null,\n        \"agent_id\":null,\n        \"order_status\":1,\n        \"print_status\":0,\n        \"real_name_status\":null,\n        \"third_order_id\":\"612085429618342\",\n        \"third_platform\":null,\n        \"fresh\":1,\n        \"status\":\"已下单\",\n        \"dak_pickup_code\":\"\",\n        \"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\n        \"orderPrice\":\"12.00\",\n        \"pay\":\"12.00\",\n        \"pay_status\":\"1\",\n        \"equity_card_id\":\"0\",\n        \"back_equity_card\":false,\n        \"deposit\":null,\n        \"pay_method\":null,\n        \"receiptsUnderCustody\":\"12.00\",\n        \"pro_price\":\"\",\n        \"dec_val\":\"\",\n        \"refund_status\":\"\",\n        \"refund_order_id\":\"\",\n        \"collect_object_type\":\"dak\",\n        \"collect_courier_id\":\"1325554\",\n        \"collect_courier_mobile\":\"13974005168\",\n        \"collect_courier_name\":\"传说中的青哥\",\n        \"placeOrderConfig\":null,\n        \"supportPrint\":true,\n        \"supportGetWaybill\":false,\n        \"shipper_zipcode_del\":\"\",\n        \"reserve_start_time\":null,\n        \"reserve_end_time\":null,\n        \"canOperate\":0,\n        \"collection\":12,\n        \"arrive_pay\":23,\n        \"f_weight\":\"12.0\",\n        \"s_weight\":\"6.0\",\n        \"f_kg\":\"1.0\",\n        \"s_kg\":\"1.0\",\n        \"support_pay\":true,\n        \"product_type\":\"\",\n        \"points_count\":0,\n        \"points\":0,\n        \"coupon\":0,\n        \"price\":\"12.00\",\n        \"package_images\":[\n\n        ],\n        \"pay_type\":\"1\",\n        \"is_arrive_pay\":null\n    }\n  }}\n\n"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":{\n        \"id\":\"@id\",\n        \"order_id\":\"@id\",\n        \"waybill\":\"@id\",\n        \"print_uid\":null,\n        \"print_platform\":null,\n        \"agent_guid\":null,\n        \"brand\":\"zt\",\n        \"kb_id\":\"@id\",\n        \"send_name\":\"@cname\",\n        \"send_mobile\":\"18573062808\",\n        \"send_tel\":null,\n        \"send_province\":\"@province\",\n        \"send_city\":\"@city\",\n        \"send_district\":\"@county\",\n        \"send_address\":\"@county\",\n        \"receive_name\":\"@name\",\n        \"receive_mobile\":\"15521290447\",\n        \"receive_tel\":\"\",\n        \"receive_province\":\"@province\",\n        \"receive_city\":\"@city\",\n        \"receive_district\":\"@county\",\n        \"receive_address\":\"大源街道田新路17号一楼103(哆啦A梦①号仓)\",\n        \"goods_name\":\"@name\",\n        \"goods_remark\":\"\",\n        \"goods_weight\":null,\n        \"source\":\"ship_code\",\n        \"proof_image\":\"\",\n        \"collect_code\":\"64E02179\",\n        \"print_time\":null,\n        \"create_time\":\"2021-12-08 15:04:56\",\n        \"dak_id\":\"1325554\",\n        \"courier_id\":null,\n        \"agent_id\":null,\n        \"order_status\":1,\n        \"print_status\":0,\n        \"real_name_status\":null,\n        \"third_order_id\":\"612085429618342\",\n        \"third_platform\":null,\n        \"fresh\":1,\n        \"status\":\"已下单\",\n        \"dak_pickup_code\":\"\",\n        \"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\n        \"orderPrice\":\"12.00\",\n        \"pay\":\"12.00\",\n        \"pay_status\":\"1\",\n        \"equity_card_id\":\"0\",\n        \"back_equity_card\":false,\n        \"deposit\":null,\n        \"pay_method\":null,\n        \"receiptsUnderCustody\":\"12.00\",\n        \"pro_price\":\"\",\n        \"dec_val\":\"\",\n        \"refund_status\":\"\",\n        \"refund_order_id\":\"\",\n        \"collect_object_type\":\"dak\",\n        \"collect_courier_id\":\"1325554\",\n        \"collect_courier_mobile\":\"13974005168\",\n        \"collect_courier_name\":\"传说中的青哥\",\n        \"placeOrderConfig\":null,\n        \"supportPrint\":true,\n        \"supportGetWaybill\":false,\n        \"shipper_zipcode_del\":\"\",\n        \"reserve_start_time\":null,\n        \"reserve_end_time\":null,\n        \"canOperate\":0,\n        \"collection\":12,\n        \"arrive_pay\":23,\n        \"f_weight\":\"12.0\",\n        \"s_weight\":\"6.0\",\n        \"f_kg\":\"1.0\",\n        \"s_kg\":\"1.0\",\n        \"support_pay\":true,\n        \"product_type\":\"\",\n        \"points_count\":0,\n        \"points\":0,\n        \"coupon\":0,\n        \"price\":\"12.00\",\n        \"package_images\":[\n\n        ],\n        \"pay_type\":\"1\",\n        \"is_arrive_pay\":null\n    }\n    },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}