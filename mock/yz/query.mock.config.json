{"useApiMock": false, "apiMockConfig": {"globalOpen": false, "rules": [{"ruleId": "955c2578-8ca7-44a9-bde9-90a05d1bf898", "ruleName": "取件-预约上门取件", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/logistic/signExpress", "filterId": "61e7d518-233d-46d4-bdcf-aea9fd933ba4", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\n      \"batch_no\":\"\"\n    },\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\n      \"batch_no\":\"@natural\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "5d83a665-074d-48cb-8a27-c31cb0235bd1", "ruleName": "取件-获取运单号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/order/getWaybillCode", "filterId": "cb213778-6134-4bc8-92ca-42a852129506", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\n  \"data\":{\n      \"702145070706667\":\n      {\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\"data\":{\"waybill\":\"\"}},\n      \"702145070706669\":{\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\n      \"data\":{\"waybill\":\"\"}},\n      \"702145070806671\":\n      {\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\n      \"data\":{\"waybill\":\"\"}},\n      \"702145070806673\":{\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\n      \"data\":{\"waybill\":\"\"}}}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "3d672002-554f-47e5-8cbb-064e9b138d0d", "ruleName": "取件-大屏预约取件扫码", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/record/scanScreenPickupPopUp", "filterId": "478169da-3532-421d-a442-f71b998b4d1c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\"dak_auth_status\":1},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d1c9d6d8-a8ce-42d9-9845-1168d0015641", "ruleName": "取件-扫码查件", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/handle/QrCode/scan", "filterId": "c6d36d3b-7782-4204-a24f-d1e52138040d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\n      \"service\":\"ship_code\",\n      \"relation_info\":{\n        \"type\":\"courier\",\n        \"courier_id\":\"1957333\",\n        \"relation_id\":\"14820950\"\n \n      },\n      \"waybill\":2314321321,\n      \"brand\":\"post\",\n      \"order_id\":22222222\n    },\n    \"msg\":\"成功\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "003afe50-6c2b-4c9d-8893-62b728d04ef5", "ruleName": "取件-物流列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/waybill/record/expressList", "filterId": "2cf50dd9-41f2-4b01-9ea9-b36bc012b600", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":[\n      {\"waybill\":\"654254332696\",\"brand\":\"ems\",\n      \"brandName\":\"EMS\",\"status\":\"退回件\",\"note\":\"\",\n      \"pickupCode\":\"25\",\"expressType\":\"yz\",\n      \"lastLogistics\":\"\",\"mobileType\":0,\"mobileNote\":\"\",\n      \"dakId\":\"1957205\",\"expressStatus\":\"1\",\"mobile\":\"166****7416\",\n      \"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-10-19 14:27:05\",\n      \"createTime\":\"2021-10-19 14:27:05\",\"expressOrderMsg\":\"浙江周树人大学\",\n      \"recordNote\":\"\",\"isMark\":0,\"order_no\":\"\",\"markMsg\":\"未预约\",\n      \"applyStatus\":0,\"applyMsg\":\"\",\"retreatReason\":\"测试退回理由问题的一个\",\n      \"specialMark\":\"测试\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"浙江周树人大学\"},{\"waybill\":\"35585665555555\",\"brand\":\"yt\",\"brandName\":\"圆通\",\"status\":\"待取件\",\"note\":\"\",\"pickupCode\":\"29\",\"expressType\":\"yz\",\"lastLogistics\":\"\",\"mobileType\":0,\"mobileNote\":\"\",\"dakId\":\"1957205\",\"expressStatus\":\"1\",\"mobile\":\"166****7416\",\"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-10-19 14:27:04\",\"createTime\":\"2021-10-19 14:27:04\",\"expressOrderMsg\":\"浙江周树人大学\",\"recordNote\":\"\",\"isMark\":0,\"order_no\":\"\",\"markMsg\":\"未预约\",\"applyStatus\":0,\"applyMsg\":\"\",\"retreatReason\":\"\",\"specialMark\":\"\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"浙江周树人大学\"},\n      {\"waybill\":\"737766738939\",\"brand\":\"zykd\",\"brandName\":\"众邮\",\"status\":\"待取件\",\"note\":\"\",\"pickupCode\":\"15\",\"expressType\":\"yz\",\"lastLogistics\":\"\",\"mobileType\":0,\"mobileNote\":\"\",\"dakId\":\"2225571\",\"expressStatus\":\"1\",\"mobile\":\"166****7416\",\"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-10-18 16:18:20\",\"createTime\":\"2021-10-18 16:18:20\",\"expressOrderMsg\":\"测试123\",\"recordNote\":\"\",\"isMark\":0,\"order_no\":\"\",\"markMsg\":\"未预约\",\"applyStatus\":0,\"applyMsg\":\"\",\"retreatReason\":\"\",\"specialMark\":\"\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"测试123\"},{\"waybill\":\"9775999906093\",\"brand\":\"postx\",\"brandName\":\"邮政快包\",\"status\":\"暂无物流\",\"note\":\"\",\"pickupCode\":\"\",\"expressType\":\"\",\"lastLogistics\":\"\",\"mobileType\":\"\",\"mobileNote\":\"\",\"dakId\":0,\"expressStatus\":\"0\",\"mobile\":\"166****7416\",\"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-11-29 16:19:55\",\"createTime\":\"2021-11-29 14:45:33\",\"expressOrderMsg\":\"由【孙靖宇】寄出的包裹\",\"recordNote\":\"\",\"isMark\":\"\",\"order_no\":\"211129376400000029\",\"markMsg\":\"\",\"applyStatus\":\"\",\"applyMsg\":\"\",\"retreatReason\":\"\",\"specialMark\":\"\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"测试123\"}]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ad931538-15ca-41e4-8bf3-9eea70a24315", "ruleName": "取件-预约取件列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/Dak<PERSON>ini/Record/uncollectedPackageByDak", "filterId": "aa970737-58ad-4b86-9953-9cdec580e0a8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\"code\":0,\"msg\":\"成功\",\"data\":\n  [\n    {\"expressStatus\":11,\n    \"waybill\":\"776594643464643\",\n    \"remark\":\"为哦互粉i的撒发的\",\n    \"brand\":\"sto\",\"brandName\":\"申通\",\"pickupCode\":\"44\",\n    \"lastTime\":\"2022-01-13 13:19:37\",\"createTime\":\"2022-01-13 13:19:37\",\n    \"isLike\":0,\"isMark\":0,\"markMsg\":\"未预约\",\"applyStatus\":0,\n    \"applyMsg\":\"\",\"lastLogistics\":\"包裹已入库, 请及时取件\",\n    \"isSelfMobile\":true,\"mobile\":\"166****7416\",\"mobileType\":1,\n    \"mobileNote\":\"\",\"complaintDesc\":\"\",\"dealDesc\":\"哦了\",\n    \"complaintStatus\":1,\"complaintType\":\"\",\"img_url\":\"\",\n    \"in_time\":\"2022-01-13 13:19:37\",\"out_time\":\"\",\"inn_name\":\"发天箱底\",\n    \"specialMark\":\"\",\n    \"owner_type\":1},\n     {\"expressStatus\":1,\n    \"waybill\":\"776594643464646\",\n    \"brand\":\"sto\",\"brandName\":\"申通\",\"pickupCode\":\"44\",\n    \"lastTime\":\"2022-01-13 13:19:37\",\"createTime\":\"2022-01-13 13:19:37\",\n    \"isLike\":0,\"isMark\":0,\"markMsg\":\"未预约\",\"applyStatus\":0,\n    \"applyMsg\":\"\",\"lastLogistics\":\"包裹已入库, 请及时取件\",\n    \"isSelfMobile\":true,\"mobile\":\"166****7416\",\"mobileType\":1,\n    \"mobileNote\":\"\",\"complaintDesc\":\"\",\"dealDesc\":\"哦了\",\n    \"complaintStatus\":1,\"complaintType\":\"\",\"img_url\":\"\",\n    \"in_time\":\"2022-01-13 13:19:37\",\"out_time\":\"\",\"inn_name\":\"发天箱底\",\n    \"specialMark\":\"\",\n    \"owner_type\":1}\n    ]},\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "38fd45a0-1029-4883-b1c1-ca2302e75265", "ruleName": "取件-包裹操作记录", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/dak/ShareDakDetail/getDakWaybillDetail", "filterId": "2561e6c0-b3bf-41a0-8596-e73bee82a7c6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"不成功\",\n    \"data\":{\n        \"id\":\"34488785\",\n        \"waybill_no\":\"464634553454\",\n        \"status\":\"out\",\n        \"status_cn\":\"已出库\",\n        \"brand\":\"zt\",\n        \"brand_cn\":\"中通\",\n        \"phone\":\"\",\n        \"pickup_code\":\"A01\",\n        \"goods_type\":\"\",\n        \"note\":\"\",\n        \"name\":\"\",\n        \"name_tag\":0,\n        \"back_reason\":\"\",\n        \"inn_name\":\"远大佳兆业\",\n        \"sms_detail\":[\n\n        ],\n        \"ivr_detail\":[\n\n        ],\n        \"button\":{\n            \"back\":{\n                \"cn\":\"退回件出库\",\n                \"is_show\":0\n            },\n            \"pickup\":{\n                \"cn\":\"\",\n                \"is_show\":0\n            },\n            \"error\":{\n                \"cn\":\"信息报错\",\n                \"is_show\":0\n            }\n        },   \n         \"operate_record\":[{\n           \"date\":\"2010-10-20 11:30\",\n                \"desc\":\"取件出库\",\n                \"desc_en\":\"\",\n                \"operate\":\"\",\n                \"reason\":\"\",\n                \"upload\":\"\"\n            },{\n              \"date\":\"2010-10-20 11:30\",\n                \"desc\":\"扫描入库\",\n                \"desc_en\":\"\",\n                \"operate\":\"\",\n                \"reason\":\"\",\n                \"upload\":\"\"\n            }]\n            \n        \n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "654113b9-76dc-4e0a-ad95-0cbef97bb196", "ruleName": "取件-物流详情", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/logistic/index", "filterId": "8a26c2f8-5957-45dd-802d-b683292a8c92", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"未查到物流信息，请确保单号正确\",\"data\":{\"package_images\":\"http://upload.kuaidihelp.com/yz/icon/2021_07/09/160e80a7004a80716542777.jpg\",\"retreatReason\":\"风刀霜剑爱粉红色哒\",\"specialMark\":\"侧泳\",\"waybill\":\"9768989832893\",\"brand_name\":\"邮政快包\",\"order_id\":\"211028729100000007\",\"brand\":\"post\",\"status\":\"暂无物流\",\"express_type\":\"查\",\"remarks\":\"\",\"tel\":\"11185\",\"url\":\"http:\\/\\/yjcx.chinapost.com.cn\",\"platform\":\"minipost_1030\",\"inn_info\":{\"express_status\":0,\"inn_name\":\"测试驿站\",\"inn_id\":234},\"needLast4Num\":0,\"recordMarkStatus\":0,\"recordApplyMsg\":\"\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ddc1d90c-1bde-440f-a613-0847f4bdec90", "ruleName": "取件-扫码优惠券", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/Coupon/createCustomCoupon", "filterId": "75984df6-8bcc-4d8b-be2c-c389016057e9", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\n      \"cost\":1\n    },\n  \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "73da25b4-57f3-4fa0-bacf-1c0d371f60d7", "ruleName": "取件-预约取件", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/logistic/signExpress", "filterId": "d7121b0e-31a4-4a36-bf8d-5be41af2e79f", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "30ee7900-ab38-4b09-ba3b-1b8c48d4c5e1", "ruleName": "取件-投诉列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/DakMini/Record/innRecordComplaintList", "filterId": "aaff623f-5da7-4044-85e1-f51f24157f80", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":[\n        {\n            \"waybill\":\"549457648585545\",\n            \"brand\":\"hw\",\n            \"brandName\":\"汇文\",\n            \"expressStatus\":\"0\",\n            \"expressMsg\":\"包裹本人已签收\",\n            \"pickupCode\":\"42\",\n            \"lastLogistics\":\"快宝驿站【测试环境】出库\",\n            \"mobile\":\"176****4094\",\n            \"mobileType\":1,\n            \"mobileNote\":\"\",\n            \"lastTime\":\"2020-09-04 16:01:05\",\n            \"complaintStatus\":1,\n            \"complaintDesc\":\"ddd\"\n        },\n        {\n            \"waybill\":\"9628577228882\",\n            \"brand\":\"ems\",\n            \"brandName\":\"EMS\",\n            \"expressStatus\":\"0\",\n            \"expressMsg\":\"包裹本人已签收\",\n            \"pickupCode\":\"4\",\n            \"lastLogistics\":\"快宝驿站【旺角驿站测试】出库\",\n            \"mobile\":\"176****4094\",\n            \"mobileType\":1,\n            \"mobileNote\":\"\",\n            \"lastTime\":\"2020-08-25 13:53:04\",\n            \"complaintStatus\":0,\n            \"complaintDesc\":\"\"\n        },\n        {\n            \"waybill\":\"728228866886\",\n            \"brand\":\"zt\",\n            \"brandName\":\"中通\",\n            \"expressStatus\":\"0\",\n            \"expressMsg\":\"包裹本人已签收\",\n            \"pickupCode\":\"3\",\n            \"lastLogistics\":\"快宝驿站【旺角驿站测试】出库\",\n            \"mobile\":\"176****4094\",\n            \"mobileType\":1,\n            \"mobileNote\":\"\",\n            \"lastTime\":\"2020-08-25 13:53:04\",\n            \"complaintStatus\":0,\n            \"complaintDesc\":\"\",\n            \"dakId\":\"123\"\n        },\n        {\n            \"waybill\":\"2872288282\",\n            \"brand\":\"yd\",\n            \"brandName\":\"韵达\",\n            \"expressStatus\":\"0\",\n            \"expressMsg\":\"包裹本人已签收\",\n            \"pickupCode\":\"11\",\n            \"lastLogistics\":\"快宝驿站【旺角驿站测试】出库\",\n            \"mobile\":\"176****4094\",\n            \"mobileType\":1,\n            \"mobileNote\":\"\",\n            \"lastTime\":\"2020-08-25 13:32:06\",\n            \"complaintStatus\":0,\n            \"complaintDesc\":\"\"\n        },\n        {\n            \"waybill\":\"6494659465659\",\n            \"brand\":\"ht\",\n            \"brandName\":\"百世\",\n            \"expressStatus\":\"1\",\n            \"expressMsg\":\"包裹已到达测试环境代收点\",\n            \"pickupCode\":\"38\",\n            \"lastLogistics\":\"快宝驿站【测试环境】入库\",\n            \"mobile\":\"176****4094\",\n            \"mobileType\":1,\n            \"mobileNote\":\"\",\n            \"lastTime\":\"2020-08-17 16:50:05\",\n            \"complaintStatus\":1,\n            \"complaintDesc\":\"\"\n        },\n        {\n            \"waybill\":\"6464356464855\",\n            \"brand\":\"ht\",\n            \"brandName\":\"百世\",\n            \"expressStatus\":\"0\",\n            \"expressMsg\":\"包裹本人已签收\",\n            \"pickupCode\":\"36\",\n            \"lastLogistics\":\"快宝驿站【测试环境】出库\",\n            \"mobile\":\"176****4094\",\n            \"mobileType\":1,\n            \"mobileNote\":\"\",\n            \"lastTime\":\"2020-08-17 16:22:05\",\n            \"complaintStatus\":0,\n            \"complaintDesc\":\"\"\n        },\n        {\n            \"waybill\":\"64646464644665\",\n            \"brand\":\"ht\",\n            \"brandName\":\"百世\",\n            \"expressStatus\":\"1\",\n            \"expressMsg\":\"包裹已到达测试环境代收点\",\n            \"pickupCode\":\"34\",\n            \"lastLogistics\":\"快宝驿站【测试环境】入库\",\n            \"mobile\":\"176****4094\",\n            \"mobileType\":1,\n            \"mobileNote\":\"\",\n            \"lastTime\":\"2020-08-17 16:11:05\",\n            \"complaintStatus\":0,\n            \"complaintDesc\":\"\"\n        }\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "04a97bb9-be3f-46c4-9f78-a64b31e72982", "ruleName": "取件-投诉信息", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/logistic/getComplaint", "filterId": "6850e9f7-9fcf-46cb-8123-6fd6520d48c5", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"id\":\"\",\n        \"cm_id\":\"1957300\",\n        \"waybill_no\":\"373748585885858\",\n        \"brand\":\"zt\",\n        \"express_phone\":\"17601614094\",\n        \"third_user_id\":\"\",\n        \"complaint_type\":\"0\",\n        \"complaint_desc\":\"测试投诉功能\",\n        \"deal_desc\":\"\",\n        \"is_deal\":\"1\",\n        \"create_at\":\"2020-03-10 09:28:26\",\n        \"update_at\":\"2020-03-10 09:40:02\",\n        \"operate_at\":\"2020-03-10 09:40:02\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6b6a53b6-fb26-46e1-8fbf-a8347a490162", "ruleName": "取件-快递员语音", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "https://m.kuaidihelp.com/WkdGzh/openDakParam", "filterId": "af97eb73-bb96-40a2-94a5-47dd43a28566", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":\n  {\"id\":\"99070\",\"cm_id\":\"1659429\",\"avator_url\":\"http://upload.kuaidihelp.com/yz/icon/2019_02/27/15c76314f1c62f805917162.jpg\",\n  \"inn_logo\":\"\",\"inn_name\":\"测试环境\",\n  \"concat_name\":\"王现勇\",\"realname\":\"\",\n  \"concat_phone\":\"17596123053\",\n  \"concat_area\":\"福建省 福州市 鼓楼区\",\n  \"concat_street\":\"\",\n  \"concat_location\":\"建滔广场建滔商业广场\",\n  \"workday\":\"周一至周日\",\n  \"start_time\":\"08:00\",\n  \"end_time\":\"20:00\",\n  \"openid\":\"\",\"unionid\":null,\n  \"create_at\":\"2019-02-27 10:30:31\",\n  \"update_at\":\"2020-06-19 10:23:23\",\"last_login_time\":null,\n  \"nickname\":\"\",\"gender\":\"0\",\"city\":\"福州市\",\"province\":\"福建省\",\n  \"country\":\"\",\"is_disable\":\"0\",\"is_auth\":\"0\",\"is_slave\":\"0\",\n  \"app_openid\":\"\",\"phone\":\"17596123053\",\"ali_openid\":\"\",\n  \"ali_open\":\"0\",\"longitude\":\"119.304\",\"latitude\":\"26.082\",\n  \"is_pass\":\"1\",\"district\":\"鼓楼区\",\"town\":\"鼓东街道\",\n  \"channel\":null,\"province_code\":\"350000000000\",\n  \"city_code\":\"350100000000\",\"district_code\":\"350102000000\",\n  \"town_code\":\"350102001000\",\"area_type\":\"\",\"league_id\":\"0\",\n  \"thirdId\":0,\n  \"subscribe_qrcode_url\":\"http://img.kuaidihelp.com/open_qrcode/weixin_shop_36/258.jpg\",\n  \"bind_mobile\":\"17596123053\",\"dak_auth_status\":1,\"source\":null,\n  \"sms_mobile\":null,\"waybill\":null,\"brand\":null,\"pic\":\"\",\n  \"content\":\"亲，949464646464已到建滔广场建滔商业广场，提货码46，请速来取，有问题联系17596123053\",\n  \"isInform\":1,\"voice_url\":\"voice_url\",\n  \"voice_text\":\"亲，949464646464已到建滔广场建滔商业广场，提货码46，请速来取，有问题联系17596123053\",\"isIvr\":1}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e534320c-59b8-4ad4-acfb-87489257b479", "ruleName": "取件-附近的驿站", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/record/nearDakList", "filterId": "16620770-09d6-4007-b22e-34ad0b770e41", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":[{\n            \"cm_id\":\"1748871\",\n            \"province\":\"福建省\",\n            \"city\":\"福州市\",\n            \"district\":\"台江区\",\n            \"town\":\"后洲街道\",\n            \"concat_area\":\"福建省 福州市 台江区\",\n            \"inn_name\":\"风之谷\",\n            \"concat_phone\":\"02188656789\",\n            \"phone\":\"13661964640\",\n            \"start_time\":\"08:06\",\n            \"end_time\":\"21:12\",\n            \"longitude\":\"121.356\",\n            \"latitude\":\"31.2266\",\n            \"concat_location\":\"福建省福州市台江区后洲街道\",\n            \"distance\":\"12.7921\"\n        },\n        {\n            \"cm_id\":\"2128349\",\n            \"province\":\"上海市\",\n            \"city\":\"上海市\",\n            \"district\":\"长宁区\",\n            \"town\":\"新泾镇\",\n            \"concat_area\":\"上海市 上海市 长宁区\",\n            \"inn_name\":\"旅途咯哦\",\n            \"concat_phone\":\"18361212558\",\n            \"phone\":\"18361212558\",\n            \"start_time\":\"08:00\",\n            \"end_time\":\"20:00\",\n            \"longitude\":\"121.355\",\n            \"latitude\":\"31.226\",\n            \"concat_location\":\"上海市上海市长宁区新泾镇\",\n            \"distance\":\"12.8989\"\n        },\n        {\n            \"cm_id\":\"2225143\",\n            \"province\":\"上海市\",\n            \"city\":\"上海市\",\n            \"district\":\"徐汇区\",\n            \"town\":\"斜土路街道\",\n            \"concat_area\":\"上海市 上海市 徐汇区\",\n            \"inn_name\":\"嘿嘿啦啦\",\n            \"concat_phone\":\"13661993517\",\n            \"phone\":\"13661993517\",\n            \"start_time\":\"08:00\",\n            \"end_time\":\"20:00\",\n            \"longitude\":\"121.351\",\n            \"latitude\":\"31.228\",\n            \"concat_location\":\"上海市上海市徐汇区斜土路街道\",\n            \"distance\":\"13.2305\"\n        }]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"data|10\" : [\n        {\n         \"cm_id\":\"1748871|2128349|2225143\",\n              \"province\":\"福建省\",\n              \"city\":\"福州市\",\n              \"district\":\"台江区\",\n              \"town\":\"后洲街道\",\n              \"concat_area\":\"福建省 福州市 台江区\",\n              \"inn_name\":\"风之谷\",\n              \"concat_phone\":\"02188656789\",\n              \"phone\":\"13661964640\",\n              \"start_time\":\"08:06\",\n              \"end_time\":\"21:12\",\n              \"longitude\":\"121.356\",\n              \"latitude\":\"31.2266\",\n              \"concat_location\":\"福建省福州市台江区后洲街道\",\n              \"distance\":\"12.7921\"\n        }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b3571931-9a83-4cfc-a1b4-5c5060a45e88", "ruleName": "取件-取件列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/DakMini/Record/uncollectedPackage", "filterId": "3757c9ea-a578-4e24-8ed9-67a68c11fdc1", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"data\" : [\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        },\n        {\n          \"expressStatus\": 1,\n\t\t\"waybill\": \"875676797979\",\n\t\t\"brand\": \"sto\",\n\t\t\"pickupCode\": \"9\",\n\t\t\"lastTime\": \"2020-02-13 13:47:05\",\n\t\t\"isLike\": 0,\n\t\t\"isMark\": 0,\n\t\t\"markMsg\": \"未预约\",\n\t\t\"applyStatus\": 0,\n\t\t\"applyMsg\": \"\",\n\t\t\"lastLogistics\": \"包裹已到达旺角驿站测试快宝驿站代收点\",\n\t\t\"mobile\": \"188****0094\",\n\t\t\"mobileType\": 0,\n\t\t\"mobileNote\": \"左同事\"\n        }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\":\"驿站已经被注销\",\n    \"data|2\" : [\n        {\n          \"expressStatus\": 0,\n          \"waybill\": /\\d{8,19}/,\n          \"brand\": \"sto\",\n          \"pickupCode\": 1,\n          \"lastTime\": \"@date(yyyy-MM-dd HH:mm:ss)\",\n          \"isLike\": 0,\n          \"isMark\": 0,\n          \"markMsg\": \"未预约\",\n          \"applyStatus\": 0,\n          \"applyMsg\": \"\",\n          \"lastLogistics\": \"@cparagraph\",\n          \"mobile\": /1\\d{2}\\*{4}\\d{4}/,\n          \"mobileType|1\": [0,1],\n          \"mobileNote\": \"@cname\",\n          \"img_url\":\"https://attachments.tower.im/tower/abf5461d216cf445cf52852e38aeaf67?version=auto\"\n        }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e8385493-6bbd-4ccf-8e60-1f45db5a5040", "ruleName": "取件-匹配单号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/logistic/getCompany", "filterId": "1c50a966-10db-4c1e-87ae-d467a0f81f78", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\t\"code\": 0,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"data\": \"*********\",\n\t\t\"response\": {\n\t\t\t\"dp\": {\n\t\t\t\t\"name\": \"德邦快递\"\n\t\t\t},\n\t\t\t\"qf\": {\n\t\t\t\t\"name\": \"全峰快递\"\n\t\t\t}\n\t\t}\n\t}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}