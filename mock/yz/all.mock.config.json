{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "cd6a2ebb-ad74-4a1e-8f4f-1d7cf2e89323", "ruleName": "我的-我的信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Config/userCenter", "filterId": "3cfab28e-4060-418b-b681-964623fc9aa8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"mobile\":\"157****8042\",\"points\":0,\"pay_status\":\"0\",\"coupon_count\":0,\"show_vip\":0,\"show_box\":0,\"nickname\":\"\",\"avatar_url\":\"https:\\/\\/thirdwx.qlogo.cn\\/mmopen\\/vi_32\\/SIJxEcpKygGrib6IQu36RiagicgwGXPKibvFNCUlzWyMQ7pqOq5ck3jia51Es0CW19v2sR1WFyrbkicnRawnahGZbiakA\\/132\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"mobile\":\"157****8042\",\"points\":0,\"pay_status\":\"0\",\"coupon_count\":0,\"show_vip\":0,\"show_box\":0,\"nickname\":\"\",\"avatar_url\":\"https:\\/\\/thirdwx.qlogo.cn\\/mmopen\\/vi_32\\/SIJxEcpKygGrib6IQu36RiagicgwGXPKibvFNCUlzWyMQ7pqOq5ck3jia51Es0CW19v2sR1WFyrbkicnRawnahGZbiakA\\/132\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "9d554478-53ca-4865-9b72-3a90735cf9ee", "ruleName": "公共-关注公众号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/followStatus", "filterId": "e165ab54-e26a-4410-9517-d754c92edbb1", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\n\"code\": 0,\n\n\"msg\": \"成功\",\n\n\"data\": {\n\n \"follow_status\": \"0\",\n\n \"gzh_name\": \"快宝驿站华东\",\n\n \"follow_url\": \"https:\\/\\/mp.weixin.qq.com\\/s\\/23wtSmXyLF3wP5ojqLftlA\"\n\n}\n\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "0ad972fd-cebc-48b1-a432-eb36e130c282", "ruleName": "公共-添加下单关系", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_mp/api/weixin/mini/minpost/relation/addRelation", "filterId": "eb2fc47d-040e-4d28-b834-7a7184de5746", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{},\n    \"code\":1,\n    \"msg\":\"错误的下单对象，请重试\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "91318994-2ec1-4cc6-821d-2d5ad266beac", "ruleName": "订单-取消订单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/order/cancel", "filterId": "0136e0d2-1222-430e-8022-321f690f540a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "bc5263e2-7270-4702-8308-6e233c74fea0", "ruleName": "公共-流量主广告", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/Ad/showFlowAd", "filterId": "bf79c55a-613f-4ed4-8a9f-626615c1f3f7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n \"code\": 0,\n \"msg\": \"成功\",\n \"data\": {\n  \"check_piece_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"logistics_details_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"subscribe_get_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"order_submit_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"stage_home_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\",\n   \"banner\": \"adunit-65122222bd2d85b6\"\n  },\n  \"order_details_page\": {\n   \"pop_up\": \"adunit-0b6279a459673f34\"\n  }\n }\n\n\t},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "dd049588-9b8f-46ae-ac75-4e78e8346161", "ruleName": "公共-驿站信息", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/record/dakInfo", "filterId": "a42baeea-c406-4803-a71f-4449279e7ce8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\t\"code\": 10,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"id\": \"125873\",\n\t\t\"cm_id\": \"1870621\",\n\t\t\"kb_id\": \"27994298\",\n\t\t\"avator_url\": \"\",\n\t\t\"inn_logo\": \"\",\n\t\t\"inn_name\": \"sunny123测试\",\n\t\t\"concat_name\": \"束霞\",\n\t\t\"realname\": \"\",\n\t\t\"concat_phone\": \"18505650937\",\n\t\t\"concat_area\": \"上海市 上海市 长宁区\",\n\t\t\"concat_street\": \"\",\n\t\t\"concat_location\": \"通协路269号\",\n\t\t\"workday\": \"周一至周日\",\n\t\t\"start_time\": \"09:00\",\n\t\t\"end_time\": \"18:00\",\n\t\t\"openid\": \"\",\n\t\t\"unionid\": null,\n\t\t\"create_at\": \"2019-04-16 16:14:40\",\n\t\t\"update_at\": \"2022-01-18 17:51:19\",\n\t\t\"last_login_time\": \"2022-01-18 17:51:19\",\n\t\t\"nickname\": \"\",\n\t\t\"gender\": \"0\",\n\t\t\"city\": \"上海市\",\n\t\t\"province\": \"上海市\",\n\t\t\"country\": \"\",\n\t\t\"is_disable\": \"0\",\n\t\t\"is_auth\": \"0\",\n\t\t\"is_slave\": \"0\",\n\t\t\"app_openid\": \"\",\n\t\t\"phone\": \"18505650937\",\n\t\t\"ali_openid\": \"\",\n\t\t\"ali_open\": \"0\",\n\t\t\"longitude\": \"121.356\",\n\t\t\"latitude\": \"31.2259\",\n\t\t\"is_pass\": \"0\",\n\t\t\"channel\": \"\",\n\t\t\"province_code\": \"310000000000\",\n\t\t\"city_code\": \"310100000000\",\n\t\t\"district_code\": \"310105000000\",\n\t\t\"town_code\": \"310105102000\",\n\t\t\"district\": \"长宁区\",\n\t\t\"town\": \"新泾镇\",\n\t\t\"waybill_time\": null,\n\t\t\"area_type\": \"办公区\",\n\t\t\"league_id\": \"0\",\n\t\t\"is_special\": \"0\",\n\t\t\"station_code\": \"2001809\",\n\t\t\"remark\": \"\",\n\t\t\"dak_auth_status\": 1,\n\t\t\"is_vip\": 0\n\t}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "79504f8c-8f2b-4b14-b8a9-b5c939d8d7e3", "ruleName": "寄件-在线支付", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/supportPay", "filterId": "94766b6e-8532-456d-883c-e13b8d80e94b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":true},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e8620ad0-784f-4842-845f-8899c5c10ba3", "ruleName": "寄件-驿站增值服务", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/order/getDakValueaddConfig", "filterId": "1f7ffc3c-ba73-40ae-9d2b-6ad0591512e0", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\n\t\t\"courierId\":\"1957205\",\"cost\":\"0.01\",\n\t\t\"proPriceStart\":\"101.00\",\n\t\t\"proPriceEnd\":\"510.11\",\n\t\t\"isDelivery\":\"1\",\"isDesPay\":\"1\",\n\t\t\"keepPrice\":0,\"isDecVal\":\"1\",\n\t\t\"isProPrice\":\"0\",\"isToDoor\":\"1\",\n\t\t\"allowReserveTime\":\"1\",\n\t\t\"isFresh\":\"1\",\"isDeclared\":\"1\",\"declaration\":\"浪里个浪\",\"reserve_start_time\":\"11:00\",\"reserve_end_time\":\"18:00\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a633e7da-c06f-47ef-bc3f-92a520c378fd", "ruleName": "取件-获取运单号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/order/getWaybillCode", "filterId": "cb213778-6134-4bc8-92ca-42a852129506", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\n  \"data\":{\n      \"702145070706667\":\n      {\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\"data\":{\"waybill\":\"\"}},\n      \"702145070706669\":{\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\n      \"data\":{\"waybill\":\"\"}},\n      \"702145070806671\":\n      {\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\n      \"data\":{\"waybill\":\"\"}},\n      \"702145070806673\":{\"code\":0,\"msg\":\"当前驿站扫码打印功能有误，请重新设置4\",\n      \"data\":{\"waybill\":\"\"}}}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "2a6b32ce-818a-48f4-9ecb-0cd131e50103", "ruleName": "取件-大屏预约取件扫码", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/record/scanScreenPickupPopUp", "filterId": "478169da-3532-421d-a442-f71b998b4d1c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\"dak_auth_status\":1},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "71393082-9afd-47a0-b0b7-c4cce662a4ff", "ruleName": "我的-更新手机号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/upBindMobileByMini", "filterId": "8500e271-80b8-4e4e-87cb-ac5083c84dad", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\"mobile\":15707018042}\n\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b7381705-71c0-43fa-b689-266f06461764", "ruleName": "取件-扫码查件", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/handle/QrCode/scan", "filterId": "c6d36d3b-7782-4204-a24f-d1e52138040d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\n      \"service\":\"ship_code\",\n      \"relation_info\":{\n        \"type\":\"courier\",\n        \"courier_id\":\"1957333\",\n        \"relation_id\":\"14820950\"\n \n      },\n      \"waybill\":2314321321,\n      \"brand\":\"post\",\n      \"order_id\":22222222\n    },\n    \"msg\":\"成功\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "50fa95b2-df9b-4e9a-8754-8cda525bb339", "ruleName": "我的-联系客服", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/ContactCustom/weixin", "filterId": "e58cf47e-f9e6-4e24-acea-c092a63b0020", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"url\":\"\",\"services_phone\":[15707018042,16606083322]}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "504f5f41-3553-48f7-bae5-a551363d7849", "ruleName": "取件-物流列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/waybill/record/expressList", "filterId": "2cf50dd9-41f2-4b01-9ea9-b36bc012b600", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":[\n      {\"waybill\":\"654254332696\",\"brand\":\"ems\",\n      \"brandName\":\"EMS\",\"status\":\"退回件\",\"note\":\"\",\n      \"pickupCode\":\"25\",\"expressType\":\"yz\",\n      \"lastLogistics\":\"\",\"mobileType\":0,\"mobileNote\":\"\",\n      \"dakId\":\"1957205\",\"expressStatus\":\"1\",\"mobile\":\"166****7416\",\n      \"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-10-19 14:27:05\",\n      \"createTime\":\"2021-10-19 14:27:05\",\"expressOrderMsg\":\"浙江周树人大学\",\n      \"recordNote\":\"\",\"isMark\":0,\"order_no\":\"\",\"markMsg\":\"未预约\",\n      \"applyStatus\":0,\"applyMsg\":\"\",\"retreatReason\":\"测试退回理由问题的一个\",\n      \"specialMark\":\"测试\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"浙江周树人大学\"},{\"waybill\":\"35585665555555\",\"brand\":\"yt\",\"brandName\":\"圆通\",\"status\":\"待取件\",\"note\":\"\",\"pickupCode\":\"29\",\"expressType\":\"yz\",\"lastLogistics\":\"\",\"mobileType\":0,\"mobileNote\":\"\",\"dakId\":\"1957205\",\"expressStatus\":\"1\",\"mobile\":\"166****7416\",\"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-10-19 14:27:04\",\"createTime\":\"2021-10-19 14:27:04\",\"expressOrderMsg\":\"浙江周树人大学\",\"recordNote\":\"\",\"isMark\":0,\"order_no\":\"\",\"markMsg\":\"未预约\",\"applyStatus\":0,\"applyMsg\":\"\",\"retreatReason\":\"\",\"specialMark\":\"\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"浙江周树人大学\"},\n      {\"waybill\":\"737766738939\",\"brand\":\"zykd\",\"brandName\":\"众邮\",\"status\":\"待取件\",\"note\":\"\",\"pickupCode\":\"15\",\"expressType\":\"yz\",\"lastLogistics\":\"\",\"mobileType\":0,\"mobileNote\":\"\",\"dakId\":\"2225571\",\"expressStatus\":\"1\",\"mobile\":\"166****7416\",\"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-10-18 16:18:20\",\"createTime\":\"2021-10-18 16:18:20\",\"expressOrderMsg\":\"测试123\",\"recordNote\":\"\",\"isMark\":0,\"order_no\":\"\",\"markMsg\":\"未预约\",\"applyStatus\":0,\"applyMsg\":\"\",\"retreatReason\":\"\",\"specialMark\":\"\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"测试123\"},{\"waybill\":\"9775999906093\",\"brand\":\"postx\",\"brandName\":\"邮政快包\",\"status\":\"暂无物流\",\"note\":\"\",\"pickupCode\":\"\",\"expressType\":\"\",\"lastLogistics\":\"\",\"mobileType\":\"\",\"mobileNote\":\"\",\"dakId\":0,\"expressStatus\":\"0\",\"mobile\":\"166****7416\",\"isSelfMobile\":true,\"notRelationMobile\":1,\"lastTime\":\"2021-11-29 16:19:55\",\"createTime\":\"2021-11-29 14:45:33\",\"expressOrderMsg\":\"由【孙靖宇】寄出的包裹\",\"recordNote\":\"\",\"isMark\":\"\",\"order_no\":\"211129376400000029\",\"markMsg\":\"\",\"applyStatus\":\"\",\"applyMsg\":\"\",\"retreatReason\":\"\",\"specialMark\":\"\",\"in_time\":null,\"out_time\":null,\"inn_name\":\"测试123\"}]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "58ce9775-2602-48b7-b73a-738950986dc3", "ruleName": "寄件-获取打赏记录", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/Reward/rewardRecord", "filterId": "4b07520c-5fc4-4f73-b7c5-e9e8ac6d7b73", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"count\":\"3\",\n        \"currentPageNum\":\"1\",\n        \"totalPageNum\":1,\n        \"pageSize\":\"10\",\n        \"list\":[\n            {\n                \"money\":\"0.10\",\n                \"success_time\":\"2021-11-19 17:48:46\",\n                \"donate_desc\":\"打赏给dyw快递员\"\n            },\n            {\n                \"money\":\"0.10\",\n                \"success_time\":\"2021-11-19 17:45:58\",\n                \"donate_desc\":\"打赏给dyw快递员\"\n            },\n            {\n                \"money\":\"0.10\",\n                \"success_time\":\"2021-11-19 17:40:49\",\n                \"donate_desc\":\"打赏给驿站\"\n            }\n        ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "60118493-01d5-4caa-b490-ffa301ba4667", "ruleName": "取件-预约取件列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/Dak<PERSON>ini/Record/uncollectedPackageByDak", "filterId": "aa970737-58ad-4b86-9953-9cdec580e0a8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\"code\":0,\"msg\":\"成功\",\"data\":\n  [\n    {\"expressStatus\":11,\n    \"waybill\":\"776594643464643\",\n    \"remark\":\"为哦互粉i的撒发的\",\n    \"brand\":\"sto\",\"brandName\":\"申通\",\"pickupCode\":\"44\",\n    \"lastTime\":\"2022-01-13 13:19:37\",\"createTime\":\"2022-01-13 13:19:37\",\n    \"isLike\":0,\"isMark\":0,\"markMsg\":\"未预约\",\"applyStatus\":0,\n    \"applyMsg\":\"\",\"lastLogistics\":\"包裹已入库, 请及时取件\",\n    \"isSelfMobile\":true,\"mobile\":\"166****7416\",\"mobileType\":1,\n    \"mobileNote\":\"\",\"complaintDesc\":\"\",\"dealDesc\":\"哦了\",\n    \"complaintStatus\":1,\"complaintType\":\"\",\"img_url\":\"\",\n    \"in_time\":\"2022-01-13 13:19:37\",\"out_time\":\"\",\"inn_name\":\"发天箱底\",\n    \"specialMark\":\"\",\n    \"owner_type\":1},\n     {\"expressStatus\":1,\n    \"waybill\":\"776594643464646\",\n    \"brand\":\"sto\",\"brandName\":\"申通\",\"pickupCode\":\"44\",\n    \"lastTime\":\"2022-01-13 13:19:37\",\"createTime\":\"2022-01-13 13:19:37\",\n    \"isLike\":0,\"isMark\":0,\"markMsg\":\"未预约\",\"applyStatus\":0,\n    \"applyMsg\":\"\",\"lastLogistics\":\"包裹已入库, 请及时取件\",\n    \"isSelfMobile\":true,\"mobile\":\"166****7416\",\"mobileType\":1,\n    \"mobileNote\":\"\",\"complaintDesc\":\"\",\"dealDesc\":\"哦了\",\n    \"complaintStatus\":1,\"complaintType\":\"\",\"img_url\":\"\",\n    \"in_time\":\"2022-01-13 13:19:37\",\"out_time\":\"\",\"inn_name\":\"发天箱底\",\n    \"specialMark\":\"\",\n    \"owner_type\":1}\n    ]},\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "59d0e184-8fcc-4d26-9bdc-a7e663059328", "ruleName": "公共-解析驿站二维码", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/handle/QrCode/innInfoByCnt", "filterId": "1578fc83-978b-4070-be43-6a580ac3564d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n      \"code\":1,\n      \"data\":{\n          \"cmId\":2225571\n      },\n      \"msg\":\"错误信息。弹出来给我瞧一瞧啊！！\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "48f804f8-8f92-4191-a474-14a23cbbc61e", "ruleName": "取件-包裹操作记录", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/dak/ShareDakDetail/getDakWaybillDetail", "filterId": "2561e6c0-b3bf-41a0-8596-e73bee82a7c6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"不成功\",\n    \"data\":{\n        \"id\":\"34488785\",\n        \"waybill_no\":\"464634553454\",\n        \"status\":\"out\",\n        \"status_cn\":\"已出库\",\n        \"brand\":\"zt\",\n        \"brand_cn\":\"中通\",\n        \"phone\":\"\",\n        \"pickup_code\":\"A01\",\n        \"goods_type\":\"\",\n        \"note\":\"\",\n        \"name\":\"\",\n        \"name_tag\":0,\n        \"back_reason\":\"\",\n        \"inn_name\":\"远大佳兆业\",\n        \"sms_detail\":[\n\n        ],\n        \"ivr_detail\":[\n\n        ],\n        \"button\":{\n            \"back\":{\n                \"cn\":\"退回件出库\",\n                \"is_show\":0\n            },\n            \"pickup\":{\n                \"cn\":\"\",\n                \"is_show\":0\n            },\n            \"error\":{\n                \"cn\":\"信息报错\",\n                \"is_show\":0\n            }\n        },   \n         \"operate_record\":[{\n           \"date\":\"2010-10-20 11:30\",\n                \"desc\":\"取件出库\",\n                \"desc_en\":\"\",\n                \"operate\":\"\",\n                \"reason\":\"\",\n                \"upload\":\"\"\n            },{\n              \"date\":\"2010-10-20 11:30\",\n                \"desc\":\"扫描入库\",\n                \"desc_en\":\"\",\n                \"operate\":\"\",\n                \"reason\":\"\",\n                \"upload\":\"\"\n            }]\n            \n        \n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6322fbb7-7316-4a32-828e-b14291b747a6", "ruleName": "寄件-快递员主页", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/relation/courierCardInfo", "filterId": "18a23b87-b180-4eb5-86a2-b8b26a9781a7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"未获得快递员的信息\",\"data\":{\n    \"focusStatus\":1,\n    \"payStatus\":1,\n    \"phone\":157071040823, \"shop\":12, \"name\":\"测试\",\n    \"service_count_sum\":12,\n    \"service_count\":23,\n    \"courier_id\":123,\n    \"service_length\":23\n  }},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1ec64556-99fc-4d54-85bb-6d26256b3920", "ruleName": "寄件-打印", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/newOrderPrint", "filterId": "b572d94a-2aac-4d63-b422-7a16b03e8942", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b224a153-dd5f-4bb8-891f-bfc9a4fc0720", "ruleName": "订单-填写订单到付金额", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/setOrderArrivePrice", "filterId": "335fdf6c-20b2-453d-88f6-78fefc281573", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"无权操作该订单\",\"data\":{}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b2ffbffe-8734-4a9f-b9b9-f562632ba042", "ruleName": "取件-物流详情", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/waybill/logistic/index", "filterId": "8a26c2f8-5957-45dd-802d-b683292a8c92", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"未查到物流信息，请确保单号正确\",\"data\":{\"package_images\":\"http://upload.kuaidihelp.com/yz/icon/2021_07/09/160e80a7004a80716542777.jpg\",\"retreatReason\":\"风刀霜剑爱粉红色哒\",\"specialMark\":\"侧泳\",\"waybill\":\"9768989832893\",\"brand_name\":\"邮政快包\",\"order_id\":\"211028729100000007\",\"brand\":\"post\",\"status\":\"暂无物流\",\"express_type\":\"查\",\"remarks\":\"\",\"tel\":\"11185\",\"url\":\"http:\\/\\/yjcx.chinapost.com.cn\",\"platform\":\"minipost_1030\",\"inn_info\":{\"express_status\":0,\"inn_name\":\"测试驿站\",\"inn_id\":234},\"needLast4Num\":0,\"recordMarkStatus\":0,\"recordApplyMsg\":\"\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6bcdacb7-2e60-4302-9020-be3cb17b1140", "ruleName": "公共-广告", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/Ad/getAdBannerList", "filterId": "93615915-1878-4928-8787-8069ae721fc4", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":[{\"app_id\":null,\"original_id\":\"\",\"imgUrl\":null,\"adUrl\":null},{\"app_id\":null,\"original_id\":\"\",\"imgUrl\":null,\"adUrl\":null}]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ef6a0e3c-197f-415c-aad0-5991b9265901", "ruleName": "公共-关注", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/followStatus", "filterId": "9746f161-1278-4fbf-a232-b031c7311a2f", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":\n    {\n      \"follow_status\":0,\n      \"gzh_name\":\"快递驿站\",\n      \"fllow_url\":\"https://kd1.cn/gz1-4\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "66b0109a-f579-4cdf-839a-f079ae5aaf5d", "ruleName": "取件-扫码优惠券", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/Coupon/createCustomCoupon", "filterId": "75984df6-8bcc-4d8b-be2c-c389016057e9", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\n      \"cost\":1\n    },\n  \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "62b43d37-f1ae-4737-97ce-e7a7354f47b5", "ruleName": "寄件-优惠券列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/CourierEquityCard/userEquityCardList", "filterId": "1a32fcd8-7b66-4536-b84a-89f6d31ff285", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\"code\":0,\"msg\":\"成功\",\"data\":{\"count\":8,\"page\":\"1\",\"pageSize\":\"30\",\n  \"list\":[{\"record_id\":\"167\",\n  \"card_name\":\"sunny\",\n  \"card_type\":\"coupon\",\n  \"start_time\":\"2020-03-15 12:42\",\n  \"discount_fee\":0.02,\"regional_restriction\":[\"上海市\",\"浙江省\",\"江苏省\",\"山东省\",\"安徽省\",\"福建省\",\"江西省\",\"湖南省\",\"湖北省\",\"河南省\"],\"invalid_time\":\"2022-03-10 21:13:57\",\"residue_num\":\"12\",\"customer_id\":\"0\",\"s_id\":\"67891787\",\"s_type\":\"yz\",\"dakName\":\"sunny123测试\",\"dak_id\":\"2225144\"},\n  {\"record_id\":\"164\",\"card_name\":\"sunny\",\"card_type\":\"discount_card\",\"discount_fee\":0.01,\"regional_restriction\":[\"上海市\",\"浙江省\",\"江苏省\",\"山东省\",\"安徽省\",\"福建省\",\"江西省\",\"湖南省\",\"湖北省\",\"河南省\"],\"invalid_time\":\"2022-03-10 20:40:30\",\"residue_num\":\"48\",\"customer_id\":\"0\",\"s_id\":\"67891787\",\"s_type\":\"yz\",\"dakName\":\"sunny123测试\",\"dak_id\":\"2225144\"},\n  {\"record_id\":\"163\",\"card_name\":\"sunny\",\"card_type\":\"discount_card\",\"discount_fee\":0.01,\"regional_restriction\":[\"上海市\",\"浙江省\",\"江苏省\",\"山东省\",\"安徽省\",\"福建省\",\"江西省\",\"湖南省\",\"湖北省\",\"河南省\"],\"invalid_time\":\"2022-03-10 20:32:03\",\"residue_num\":\"47\",\"customer_id\":\"0\",\"s_id\":\"67891787\",\"s_type\":\"yz\",\"dakName\":\"sunny123测试\",\"dak_id\":\"2225144\"},{\"card_id\":\"1149970\",\"card_name\":\"在线支付运费赠送券\",\"card_type\":\"coupon\",\"type\":\"2\",\"discount_fee\":0.99,\"regional_restriction\":[\"all\"],\"invalid_time\":\"2021-11-20 16:22:38\",\"residue_num\":\"1\",\"s_id\":\"160033\",\"s_type\":\"yz\",\"scene_name\":\"在线支付运费赠送券\",\"price_condition\":{\"min\":0,\"max\":0},\"dakName\":\"浙江周树人大学\",\"dak_id\":\"1957205\"},{\"card_id\":\"1149972\",\"card_name\":\"新用户寄件优惠券\",\"card_type\":\"coupon\",\"type\":\"1\",\"discount_fee\":2.41,\"regional_restriction\":[\"all\"],\"invalid_time\":\"2021-11-20 20:01:52\",\"residue_num\":\"1\",\"s_id\":\"93617786\",\"s_type\":\"yz\",\"scene_name\":\"新用户寄件优惠券\",\"price_condition\":{\"min\":0.1,\"max\":0.1},\"dakName\":\"徐肖磊测试驿站\",\"dak_id\":\"2225710\"},{\"card_id\":\"1149999\",\"card_name\":\"在线支付运费赠送券\",\"card_type\":\"coupon\",\"type\":\"2\",\"discount_fee\":0.01,\"regional_restriction\":[\"all\"],\"invalid_time\":\"2021-11-27 14:52:20\",\"residue_num\":\"1\",\"s_id\":\"93617786\",\"s_type\":\"yz\",\"scene_name\":\"在线支付运费赠送券\",\"price_condition\":{\"min\":0.1,\"max\":0.1},\"dakName\":\"徐肖磊测试驿站\",\"dak_id\":\"2225710\"},{\"card_id\":\"1150014\",\"card_name\":\"在线支付运费赠送券\",\"card_type\":\"coupon\",\"type\":\"2\",\"discount_fee\":0.35,\"regional_restriction\":[\"all\"],\"invalid_time\":\"2021-11-27 19:33:58\",\"residue_num\":\"1\",\"s_id\":\"93617389\",\"s_type\":\"yz\",\"scene_name\":\"在线支付运费赠送券\",\"price_condition\":{\"min\":0,\"max\":0},\"dakName\":\"测试123\",\"dak_id\":\"2225571\"},{\"card_id\":\"1150015\",\"card_name\":\"在线支付运费赠送券\",\"card_type\":\"coupon\",\"type\":\"2\",\"discount_fee\":5.27,\"regional_restriction\":[\"all\"],\"invalid_time\":\"2021-11-27 19:37:30\",\"residue_num\":\"1\",\"s_id\":\"93617786\",\"s_type\":\"yz\",\"scene_name\":\"在线支付运费赠送券\",\"price_condition\":{\"min\":0.01,\"max\":0.01},\"dakName\":\"徐肖磊测试驿站\",\"dak_id\":\"2225710\"}]}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}\n\n\n"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "993678bb-8278-43fe-81c1-f637660a5e77", "ruleName": "我的-平台驿站列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/City/puisnePostList", "filterId": "96da9784-e242-4b8e-9d6c-4ac7cca5f4b6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":[{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":15707018042},{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":157807018042},{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":157807018042},{\"name\":\"test1\",\"address\":\"这是测试地址1\",\"phone\":157807018042}],\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "81eb44a5-e63a-40c7-b0d1-a9addcaf3ca3", "ruleName": "我的-积分配置", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/v1/pay/NewVipPoints/getPointsConfig", "filterId": "0ab34227-4dc1-476c-82d0-c9bc59ce1957", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\n      \"new_user\":{\n        \"point\":13,\n        \"status\": \"1\"\n      },\n       \"online_pay\":{\n        \"point\":11,\n        \"status\": \"1\"\n      },\n       \"bind_phone\":{\n        \"point\":12,\n        \"status\": \"1\"\n      }\n    },\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8287dd16-7990-436a-8c65-45588ed109e8", "ruleName": "寄件-提交订单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/weixin/mini/minpost/order/add", "filterId": "1d04aada-7033-45c4-bbd1-7436e044605c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"order_id\":\"702095766707693\",\"third_order_id\":\"702095766707693\",\"third_platform\":\"dak\",\"order_platform\":\"minpost\",\"send_mobile\":\"18715276649\",\"send_tel\":\"\",\"send_province\":\"上海市\",\"collect_code\":\"28602636\",\"brand\":\"zt\",\"brand_name\":\"中通快递\",\"waybill\":\"23521234321523\",\"collection\":\"\",\"orderPrice\":0,\"courier_name\":\"\",\"courier_mobile\":\"\",\"courier_id\":0,\"dak_id\":2225571,\"realNameCity\":\"上海市\",\"agent_id\":\"\",\"dak_name\":\"测试123\",\"dak_mobile\":\"17596123053\",\"warrant_price\":0,\"pro_price\":0,\"open_pay_type\":\"public_pay\",\"weight\":0,\"support_pay\":true,\"support_print\":2,\"is_custom\":false,\"closeAd\":0}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "36558f41-eb60-4048-a88b-f14cd98afcac", "ruleName": "订单-我收到的快递订单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/MiniDak/receiverOrderList", "filterId": "4ac6edc3-72d8-47d4-b257-5a29e0c76790", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"ok\",\"data\":[{\"id\":\"609085938100746\",\"order_id\":\"609085938100746\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602635\",\"create_time\":\"2021-09-08 16:29:41\",\"third_order_id\":\"609085938100746\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.01\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609085917300744\",\"order_id\":\"609085917300744\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602634\",\"create_time\":\"2021-09-08 16:26:13\",\"third_order_id\":\"609085917300744\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.01\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609085736706554\",\"order_id\":\"609085736706554\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602633\",\"create_time\":\"2021-09-08 15:56:07\",\"third_order_id\":\"609085736706554\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609085701507835\",\"order_id\":\"609085701507835\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602632\",\"create_time\":\"2021-09-08 15:50:15\",\"third_order_id\":\"609085701507835\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609085688900717\",\"order_id\":\"609085688900717\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602631\",\"create_time\":\"2021-09-08 15:48:09\",\"third_order_id\":\"609085688900717\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609085676600715\",\"order_id\":\"609085676600715\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602630\",\"create_time\":\"2021-09-08 15:46:06\",\"third_order_id\":\"609085676600715\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609085667109556\",\"order_id\":\"609085667109556\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602629\",\"create_time\":\"2021-09-08 15:44:31\",\"third_order_id\":\"609085667109556\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075842702257\",\"order_id\":\"609075842702257\",\"waybill\":\"75804679887670\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":2,\"print_status\":1,\"collect_code\":\"28602595\",\"create_time\":\"2021-09-07 16:13:47\",\"third_order_id\":\"609075842702257\",\"status\":\"已打印\",\"last_logistics\":\"您的包裹面单已打印\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075842702255\",\"order_id\":\"609075842702255\",\"waybill\":\"75804679887151\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"亲领悟\",\"receive_province\":\"安徽省\",\"receive_city\":\"芜湖市\",\"goods_name\":\"日用品\",\"order_status\":2,\"print_status\":1,\"collect_code\":\"28602594\",\"create_time\":\"2021-09-07 16:13:47\",\"third_order_id\":\"609075842702255\",\"status\":\"已打印\",\"last_logistics\":\"您的包裹面单已打印\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075842702253\",\"order_id\":\"609075842702253\",\"waybill\":\"75804680192927\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":2,\"print_status\":1,\"collect_code\":\"28602593\",\"create_time\":\"2021-09-07 16:13:47\",\"third_order_id\":\"609075842702253\",\"status\":\"已打印\",\"last_logistics\":\"您的包裹面单已打印\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075823802250\",\"order_id\":\"609075823802250\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"亲领悟\",\"receive_province\":\"安徽省\",\"receive_city\":\"芜湖市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602592\",\"create_time\":\"2021-09-07 16:10:38\",\"third_order_id\":\"609075823802250\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075823802248\",\"order_id\":\"609075823802248\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602591\",\"create_time\":\"2021-09-07 16:10:38\",\"third_order_id\":\"609075823802248\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075801001411\",\"order_id\":\"609075801001411\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"亲领悟\",\"receive_province\":\"安徽省\",\"receive_city\":\"芜湖市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602590\",\"create_time\":\"2021-09-07 16:06:50\",\"third_order_id\":\"609075801001411\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075801001409\",\"order_id\":\"609075801001409\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602589\",\"create_time\":\"2021-09-07 16:06:50\",\"third_order_id\":\"609075801001409\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075694503945\",\"order_id\":\"609075694503945\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"亲领悟\",\"receive_province\":\"安徽省\",\"receive_city\":\"芜湖市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602588\",\"create_time\":\"2021-09-07 15:49:05\",\"third_order_id\":\"609075694503945\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075694403943\",\"order_id\":\"609075694403943\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602587\",\"create_time\":\"2021-09-07 15:49:04\",\"third_order_id\":\"609075694403943\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075694303941\",\"order_id\":\"609075694303941\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602586\",\"create_time\":\"2021-09-07 15:49:03\",\"third_order_id\":\"609075694303941\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075684503939\",\"order_id\":\"609075684503939\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602585\",\"create_time\":\"2021-09-07 15:47:25\",\"third_order_id\":\"609075684503939\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075684403937\",\"order_id\":\"609075684403937\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602584\",\"create_time\":\"2021-09-07 15:47:24\",\"third_order_id\":\"609075684403937\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075684403935\",\"order_id\":\"609075684403935\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602583\",\"create_time\":\"2021-09-07 15:47:24\",\"third_order_id\":\"609075684403935\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075662204173\",\"order_id\":\"609075662204173\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602582\",\"create_time\":\"2021-09-07 15:43:42\",\"third_order_id\":\"609075662204173\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075662204171\",\"order_id\":\"609075662204171\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602581\",\"create_time\":\"2021-09-07 15:43:42\",\"third_order_id\":\"609075662204171\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075583606819\",\"order_id\":\"609075583606819\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602580\",\"create_time\":\"2021-09-07 15:30:36\",\"third_order_id\":\"609075583606819\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075583606817\",\"order_id\":\"609075583606817\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602579\",\"create_time\":\"2021-09-07 15:30:36\",\"third_order_id\":\"609075583606817\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"},{\"id\":\"609075538500603\",\"order_id\":\"609075538500603\",\"waybill\":\"\",\"brand\":\"zt\",\"send_name\":\"哈哈\",\"send_province\":\"上海市\",\"send_city\":\"上海市\",\"receive_name\":\"fds\",\"receive_province\":\"上海市\",\"receive_city\":\"上海市\",\"goods_name\":\"日用品\",\"order_status\":1,\"print_status\":0,\"collect_code\":\"28602578\",\"create_time\":\"2021-09-07 15:23:05\",\"third_order_id\":\"609075538500603\",\"status\":\"已下单\",\"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\"orderPrice\":\"0.00\",\"source\":\"inn-mini\",\"channel\":\"minpost\",\"collect_object_type\":\"dak\",\"collect_courier_id\":\"2225571\",\"collect_courier_mobile\":\"17596123053\",\"collect_courier_name\":\"测试123\",\"placeOrderConfig\":null,\"sourceText\":\"来自驿站小程序订单\"}]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1bc1c45a-d6da-40d6-bb0e-cf9cf12e8ef2", "ruleName": "我的-是否存在新券", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/CourierEquityCard/getIsHasCoupon", "filterId": "88cc33a2-ad85-4bb8-8384-71231f8a34d1", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":\"0\",\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "dedaeb37-5dfe-4d06-93dd-8d12abd92cc4", "ruleName": "寄件-快递员卡片", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/weixin/mini/minpost/relation/courierCardInfo", "filterId": "943da701-119b-47de-a760-02d7fa86e945", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\n       \"service_count\":95,\n        \"service_count_sum\":191,\n        \"nearest_distance\":\"\",\n        \"last_login_time\":[\n\n        ],\n        \"latest_messages\":[\n\n        ],\n        \"is_nearby\":true,\n        \"service_length\":754,\n        \"courier_id\":\"1614786\",\n        \"phone\":\"***********\",\n        \"name\":\"丁*成\",\n        \"account_company\":\"sto\",\n        \"account_shop\":\"甘肃临夏公司\",\n        \"location\":\"甘肃省-临夏回族自治州-东乡族自治县\",\n        \"index_shop_id\":\"200\",\n        \"is_focused\":true,\n        \"type\":\"courier\",\n        \"focusStatus\":\"1\",\n        \"payStatus\":\"1\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8dbb2888-dfc2-4641-b6a1-02f1ce166f20", "ruleName": "寄件-增值服务", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/getDakValueaddConfig", "filterId": "9d860c3a-1ff5-416b-a2db-02303e2b2448", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{   \"courierId\": \"2225571\",\n\t\t\"cost\": \"1.00\",\n\t\t\"proPriceStart\": \"100.00\",\n\t\t\"proPriceEnd\": \"10000.00\",\n\t\t\"isDelivery\": \"1\",\n\t\t\"isDesPay\": \"1\",\n\t\t\"keepPrice\": 0,\n\t\t\"isDecVal\": \"0\",\n\t\t\"isProPrice\": \"1\",\n\t\t\"isToDoor\": \"1\",\n\t\t\"allowReserveTime\": \"1\",\n\t\t\"isFresh\": \"0\",\n\t\t\"declaration\": \"李一鸣了搜题不拿没安全！我上次问了最高\",\n\t\t\"reserve_start_time\": \"08:00\",\n\t\t\"reserve_end_time\": \"20:00\",\n\t\t\"isDeclared\":\"1\"},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "107dd858-0692-486f-a609-1cf42acf7f44", "ruleName": "公共-实名信息", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/realname/auth/realNameInfo", "filterId": "0174b816-0881-4cec-b789-6b7624a93fd4", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0 ,\n  \"data\":{\n    \"realname_status\":1,\n    \"name\":\"李大钊\",\n    \"no\":360622199702187716,\n    \"pSrc\":true,\n    \"nSrc\":false,\n    \"status\":\"已认证\"\n  }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f820817b-174b-4952-ac25-44f21992e0d2", "ruleName": "寄件-解析身份证", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/v1/WeApp/parseRealName", "filterId": "668eb1a3-b79e-4842-bd2e-7f1d79453234", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\n      \"name\":\"213\",\n      \"end\":\"20221010\",\n      \"start\":\"20200211\",\n      \"police\":\"随便来一个\",\n            \"pSrc\":\"https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/share-waybill.png\",\n      \"nSrc\":\"https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/share-waybill.png\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "caada3f0-0dc2-4088-ba00-cb04043aeecd", "ruleName": "订单-订单详情", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/MiniDak/orderInfo", "filterId": "3b92139b-096d-40e2-a84e-e32c5ca82b8b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":{\n        \"id\":\"612085429618342\",\n        \"order_id\":\"612085429618342\",\n        \"waybill\":\"\",\n        \"print_uid\":null,\n        \"print_platform\":null,\n        \"agent_guid\":null,\n        \"brand\":\"zt\",\n        \"kb_id\":\"115187280\",\n        \"send_name\":\"黄灯辉\",\n        \"send_mobile\":\"18573062808\",\n        \"send_tel\":null,\n        \"send_province\":\"湖南省\",\n        \"send_city\":\"岳阳市\",\n        \"send_district\":\"平江县\",\n        \"send_address\":\"三阳乡大众安置区\",\n        \"receive_name\":\"哆啦A梦\",\n        \"receive_mobile\":\"15521290447\",\n        \"receive_tel\":\"\",\n        \"receive_province\":\"广东省\",\n        \"receive_city\":\"广州市\",\n        \"receive_district\":\"白云区\",\n        \"receive_address\":\"大源街道田新路17号一楼103(哆啦A梦①号仓)\",\n        \"goods_name\":\"日用品\",\n        \"goods_remark\":\"\",\n        \"goods_weight\":null,\n        \"source\":\"ship_code\",\n        \"proof_image\":\"\",\n        \"collect_code\":\"64E02179\",\n        \"print_time\":null,\n        \"create_time\":\"2021-12-08 15:04:56\",\n        \"dak_id\":\"1325554\",\n        \"courier_id\":null,\n        \"agent_id\":null,\n        \"order_status\":1,\n        \"print_status\":0,\n        \"real_name_status\":null,\n        \"third_order_id\":\"612085429618342\",\n        \"third_platform\":null,\n        \"fresh\":1,\n        \"status\":\"已下单\",\n        \"dak_pickup_code\":\"\",\n        \"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\n        \"orderPrice\":\"12.00\",\n        \"pay\":\"12.00\",\n        \"pay_status\":\"1\",\n        \"equity_card_id\":\"0\",\n        \"back_equity_card\":false,\n        \"deposit\":null,\n        \"pay_method\":null,\n        \"receiptsUnderCustody\":\"12.00\",\n        \"pro_price\":\"\",\n        \"dec_val\":\"\",\n        \"refund_status\":\"\",\n        \"refund_order_id\":\"\",\n        \"collect_object_type\":\"dak\",\n        \"collect_courier_id\":\"1325554\",\n        \"collect_courier_mobile\":\"13974005168\",\n        \"collect_courier_name\":\"传说中的青哥\",\n        \"placeOrderConfig\":null,\n        \"supportPrint\":true,\n        \"supportGetWaybill\":false,\n        \"shipper_zipcode_del\":\"\",\n        \"reserve_start_time\":null,\n        \"reserve_end_time\":null,\n        \"canOperate\":0,\n        \"collection\":12,\n        \"arrive_pay\":23,\n        \"f_weight\":\"12.0\",\n        \"s_weight\":\"6.0\",\n        \"f_kg\":\"1.0\",\n        \"s_kg\":\"1.0\",\n        \"support_pay\":true,\n        \"product_type\":\"\",\n        \"points_count\":0,\n        \"points\":0,\n        \"coupon\":0,\n        \"price\":\"12.00\",\n        \"package_images\":[\n\n        ],\n        \"pay_type\":\"1\",\n        \"is_arrive_pay\":null\n    }\n  }}\n\n"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d797107b-c84f-4487-8c98-08b922b280c8", "ruleName": "寄件-驿站可用积分", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/cloudPrint/order/v1/pay/NewVipPoints/userVipPoints", "filterId": "abb819ea-0dac-4c15-bd2f-d6cb0dded890", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\n\t\"data\": {\n\t\t\"inn\": {\n\t\t\t\"id\": \"49\",\n\t\t\t\"points\": 10,\n\t\t\t\"money\": \"0\",\n\t\t\t\"integral_name\": \"测试123\"\n\t\t},\n\t\t\"city_shop\": {\n\t\t\t\"id\": \"24\",\n\t\t\t\"platform\": \"wt_mini_55\",\n\t\t\t\"points\": 0,\n\t\t\t\"money\": \"0\",\n\t\t\t\"integral_name\": \"\"\n\t\t},\n\t\t\"max_points\": 30\n},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "40645c30-0210-4e31-854f-bb9e16b60c7b", "ruleName": "寄件-积分明细列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/cloudPrint/order/v1/pay/NewVipPoints/userVipPointsDetailList", "filterId": "3205b914-84fc-48a4-8d5a-ee004b074ae7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":{\"page\":1,\"pageSize\":30,\"list\":[\n    {\"create_time\":\"2020-12-23\",\"id\":2314231,\"s_kb_id\":214,\"points\":231,\"flow_sign\":\"+\",\"flow_desc\":\"测试1\"},\n    {\"create_time\":\"2020-12-13\",\"id\":231431,\"s_kb_id\":214,\"points\":231,\"flow_sign\":\"+\",\"flow_desc\":\"测试2\"},\n    {\"create_time\":\"2020-12-03\",\"id\":234231,\"s_kb_id\":2141,\"points\":31,\"flow_sign\":\"-\",\"flow_desc\":\"测试3\"}]}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a0234023-2530-4232-89cb-95d64d2174db", "ruleName": "寄件-积分列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/cloudPrint/order/v1/pay/NewVipPoints/userVipPointsList", "filterId": "fe4f61b7-05ce-4c33-93b3-22444b333648", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"不支持该平台\",\n    \"data\":{\n      \"inn\":[{\"points\":21314,\"sKbId\":2341,\"integral_name\":\"测试积分\"}],\n      \"city_shop\":[{\"points\":2134,\"sKbId\":2341}]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"不支持该平台\",\n    \"data\":{\n      \"inn\":[{\"inn_name\":\"测试1\",\"dak_id\":2225293,\"points\":4},{\"inn_name\":\"测试2\",\"dak_id\":2225293,\"points\":234},{\"inn_name\":\"测试3\",\"dak_id\":2225293,\"points\":34},{\"inn_name\":\"测试4\",\"dak_id\":160033,\"points\":23}],\n      \"city_shop\":[{\"points\":2134,\"sKbid\":2341}]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "4f7ca050-7f8d-4020-9192-2005acd3bb6d", "ruleName": "寄件-扫码寄", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/handle/QrCode/scan", "filterId": "fa13ebef-0dd5-4130-a9eb-c754d289fe72", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\":0,\"msg\":\"成功\",\n  \"data\":{\n    \"service\":\"ship_code\",\n    \"order_id\":\"\",\"waybill\":\"\",\n    \"brand\":\"jt\",\n    \"relation_info\":\n    {\"type\":\"dak\",\"brand\":\"\",\"dak_id\":2225571},\n    \"collect_code\":\"K174000426\",\n    \"courier_id\":0,\"customer_id\":0,\n    \"dak_id\":\"2225571\",\n    \"t\":\"\",\n    \"is_order_user\":1}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "875ca720-70b0-40af-9b3d-af038da5d98d", "ruleName": "寄件-默认下单关系", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/relation/getDefaultRelation", "filterId": "2f166c75-d224-4f61-9991-b35b37d7b14d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": null\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1a834063-864e-49fa-9522-e221ba6b6a36", "ruleName": "订单-订单详情", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/MiniDak/orderInfo", "filterId": "b9d42986-4079-43b6-bcc8-3ffe721b42f8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":{\n        \"id\":\"704034612504893\",\n        \"order_id\":\"704034612504893\",\n        \"waybill\":\"\",\n        \"print_uid\":null,\n        \"print_platform\":null,\n        \"agent_guid\":null,\n        \"brand\":\"sto\",\n        \"kb_id\":\"158709592\",\n        \"send_name\":\"沈雪雪\",\n        \"send_mobile\":\"15293430506\",\n        \"send_tel\":\"\",\n        \"send_province\":\"甘肃省\",\n        \"send_city\":\"庆阳市\",\n        \"send_district\":\"环县\",\n        \"send_address\":\"曲子镇\",\n        \"receive_name\":\"曹斌星[9942]\",\n        \"receive_mobile\":\"18400684008\",\n        \"receive_tel\":\"\",\n        \"receive_province\":\"江苏省\",\n        \"receive_city\":\"苏州\",\n        \"receive_district\":\"吴江区\",\n        \"receive_address\":\"江陵西路逸品阁繁华里1栋706[9942]\",\n        \"goods_name\":\"日用品\",\n        \"goods_remark\":\"\",\n        \"goods_weight\":\"0.85\",\n        \"source\":\"\",\n        \"proof_image\":\"\",\n        \"collect_code\":\"1910643\",\n        \"print_time\":null,\n        \"create_time\":\"2022-04-03 12:48:45\",\n        \"dak_id\":null,\n        \"courier_id\":\"386659\",\n        \"agent_id\":null,\n        \"order_status\":1,\n        \"print_status\":0,\n        \"real_name_status\":1,\n        \"third_order_id\":\"704034612504893\",\n        \"third_platform\":null,\n        \"fresh\":0,\n        \"status\":\"已下单\",\n        \"dak_pickup_code\":\"\",\n        \"last_logistics\":\"已通知驿站，请前往驿站将包裹交由工作人员处理\",\n        \"orderPrice\":0,\n        \"pay\":0,\n        \"pay_status\":0,\n        \"equity_card_id\":0,\n        \"back_equity_card\":false,\n        \"deposit\":null,\n        \"pay_method\":null,\n        \"receiptsUnderCustody\":\"0.00\",\n        \"pro_price\":\"\",\n        \"dec_val\":\"\",\n        \"refund_status\":\"\",\n        \"refund_order_id\":\"\",\n        \"collect_object_type\":\"courier\",\n        \"collect_courier_id\":\"386659\",\n        \"collect_courier_mobile\":\"18919263729\",\n        \"collect_courier_name\":\"甘肃环县曲子营业部\",\n        \"placeOrderConfig\":null,\n        \"supportPrint\":false,\n        \"supportGetWaybill\":false,\n        \"shipper_zipcode_del\":\"0\",\n        \"reserve_start_time\":null,\n        \"reserve_end_time\":null,\n        \"canOperate\":1,\n        \"f_weight\":null,\n        \"s_weight\":null,\n        \"f_kg\":null,\n        \"s_kg\":null,\n        \"support_pay\":true,\n        \"product_type\":\"\",\n        \"collection\":null,\n        \"arrive_pay\":null,\n        \"points_count\":0,\n        \"points\":null,\n        \"coupon\":null,\n        \"price\":0,\n        \"package_images\":[\n\n        ],\n        \"pay_type\":\"0\",\n        \"is_arrive_pay\":null\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c31b821a-3232-4287-98a7-7279246cc8ef", "ruleName": "寄件-品牌", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/relation/supportBrand", "filterId": "db61abae-e230-4b0b-be78-eeac467072be", "matchType": "regExp"}, {"propName": "", "propRegString": "", "filterId": "99c07da9-38bd-41b8-85d7-dcc16994301a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"data\":[\n\n                    ]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ec6e0a11-e999-4626-a534-b4a9ed98186e", "ruleName": "公共-获取手机号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Config/userCenter", "filterId": "8244fe77-5487-409e-9595-fe84e830b180", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"mobile\":\"\",\"points\":0,\"pay_status\":\"0\",\"coupon_count\":2,\"show_vip\":0,\"show_box\":0,\"nickname\":\"W\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "68a1910d-5e80-4671-8eb3-c4029d86d577", "ruleName": "寄件-报价单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/order/getQuotationPrice", "filterId": "c0161462-6270-4c09-b7af-6909caed90ad", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"data\":{\"price\":11,\"f_weight\":1,\"s_weight\":2,\"f_kg\":1,\"s_kg\":2},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1a57d11a-671e-45b8-8570-827ee86e1709", "ruleName": "我的-绑定手机号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/bindUserMobile", "filterId": "ce6e190d-5bb3-4983-89da-148192a0e1c8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\"code\":0,\"data\":{}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c115ebc8-dde9-4a60-98e3-df470a1d9bc2", "ruleName": "寄件-权益次卡-选择", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/minpost/CourierEquityCard/userEquityCardList", "filterId": "32f8af1c-8bdc-46fb-a27d-c458678cc688", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"count\":3,\n        \"page\":\"1\",\n        \"pageSize\":\"5\",\n        \"list\":[{\"card_id\":\"2959736\",\"card_name\":\"优惠券\",\"card_type\":\"discount_card\",\"discount_fee\":2.20,\"regional_restriction\":[\"all\"],\"invalid_time\":\"2021-06-13 09:58:22\",\"residue_num\":\"1\",\"s_id\":\"1094099\",\"s_type\":\"yz\",\"dakName\":\"丹水快递驿站\"}]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "513aa25b-7faf-46ab-a6c4-92c3eb0a0cb8", "ruleName": "公共-首页广告位", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/getAds", "filterId": "8be1b048-418a-4a17-90d1-2fed91fab4b6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n      \"count\": 4,\n      \"list\": [\n        {\n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"微掌柜app新版上线\"\n        },\n        { \n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"微掌柜网页版\"\n        },\n        {\n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"电商品台一键导入\"\n        },\n        {\n          \"img_url\":\"//cdn-img.kuaidihelp.com/official/home/<USER>\",\n          \"link\":\"\",\n          \"title\":\"天天打卡领现金\"\n        }\n      ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ea784a0c-b4de-44c1-bca7-b3c4f57492e8", "ruleName": "公共-授权手机号", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/weixin/mini/user/Bind/bindMobileByAlipayMini", "filterId": "c6291e21-ae11-4a45-883e-e154bdd0e827", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\": 0,\n    \"data\": {\n      \"mobile\": \"\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d3f684f3-4872-409b-b381-90d471600b9f", "ruleName": "公共-登录", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "weixin/mini/login/index", "filterId": "40e19ea1-9f87-4d4d-853e-ec3ffd7f9c24", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n   \"data\": {\n    \"code\":0,\n    \"data\":{\n      \"sessionid\": \"b997a9559a14f789df8f61ee354abc97\"\n     }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}