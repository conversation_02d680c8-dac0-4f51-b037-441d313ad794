{"miniprogramRoot": "dist/weapp.dev", "projectname": "%E9%A9%BF%E7%AB%99%E5%8A%9F%E8%83%BD%E7%89%88", "appid": "wx1cc70b43e2af1811", "setting": {"urlCheck": false, "es6": false, "enhance": false, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyJS": true, "minifyWXSS": false, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "condition": false}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "libVersion": "2.20.1", "compileType": "miniprogram", "srcMiniprogramRoot": "dist/weapp.wkd.dev/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "condition": {"miniprogram": {"list": [{"name": "寄快递", "pathName": "pages/order/edit/index", "query": "", "scene": null}, {"name": "寄快递 - 扫码寄 - 已寄", "pathName": "pages/order/edit/index", "query": "q=https%3A%2F%2Fkbydy.cn%2Fs%3Fi%3D35376856%26k%3Dca37b1f4", "scene": null}, {"name": "寄快递 - 扫码寄", "pathName": "pages/order/edit/index", "query": "q=https%3A%2F%2Fkbydy.cn%2Fs%3Fi%3D35377221%26k%3Dae85c965", "scene": null}, {"name": "寄快递 - 权益次卡", "pathName": "pages/order/card/index", "query": "", "scene": null}, {"name": "寄快递 - 权益次卡-驿站", "pathName": "pages/order/card/index", "query": "dakId=2127890", "scene": null}, {"name": "寄快递 - 积分中心", "pathName": "pages/order/integral/index", "query": "", "scene": null}, {"name": "寄快递 - 积分明细", "pathName": "pages/order/integral/detail/index", "query": "", "scene": null}, {"name": "寄快递 - 选择下单关系", "pathName": "pages/order/relation/index", "query": "", "scene": null}, {"name": "寄快递 - 附近的驿站", "pathName": "pages/order/shop/index", "query": "", "scene": null}, {"name": "寄快递 - 结果页", "pathName": "pages/order/result/index", "query": "order_id=201216165900000036&total=1&brand=&status=submit", "scene": null}, {"name": "寄快递 - 增值服务", "pathName": "pages/order/service/index", "query": "type=pj-mark", "scene": null}, {"name": "查快递", "pathName": "pages/query/index", "query": "", "scene": null}, {"name": "查快递 - 扫码寄 - 查件", "pathName": "pages/query/detail/index", "query": "q=https%3A%2F%2Fkbydy.cn%2Fs%3Fi%3D35376856%26k%3Dca37b1f4", "scene": null}, {"name": "查快递 - 扫码寄 - 未寄件", "pathName": "pages/query/detail/index", "query": "q=https%3A%2F%2Fkbydy.cn%2Fs%3Fi%3D35377221%26k%3Dae85c965", "scene": null}, {"name": "查快递 - 物流详情", "pathName": "pages/query/detail/index", "query": "waybill=547673228102&brand=zt", "scene": null}, {"name": "查快递 - 物流详情 - 暂无", "pathName": "pages/query/detail/index", "query": "waybill=1111111111&brand=zt", "scene": null}, {"name": "查快递 - 匹配", "pathName": "pages/query/match/index", "query": "no=776001646931794", "scene": null}, {"name": "查快递 - 预约取件", "pathName": "pages/query/appointment/index", "query": "dakId=2292841", "scene": null}, {"name": "查快递 - 预约取件 - 带语音消息", "pathName": "pages/query/appointment/index", "query": "dakId=1957205&inform_id=489670805&inform_vcode=d922da2bb6cbbad61d3ee073f2cd71b4&ivr_vcode=732b5786e5954ae92b6c81c8e5134395&ivr_id=58516196&push_type=sync&ivr_msg_id=687027&sms_msg_id=687026", "scene": null}, {"name": "查快递 - 预约取件 - 带语音文字", "pathName": "pages/query/appointment/index", "query": "dakId=1957205&inform_id=489670808&inform_vcode=e14b00be0009055cfbbd9e7aac5d6152&ivr_vcode=c14a1067ecf0476c8636b92ed02a18ca&ivr_id=58516199&push_type=sync&ivr_msg_id=687033&sms_msg_id=687032", "scene": null}, {"name": "查快递 - 投诉反馈", "pathName": "pages/query/feedback/index", "query": "", "scene": null}, {"name": "查快递 - 投诉反馈 - 原因", "pathName": "pages/query/feedback/reason/index", "query": "brand=zt&waybill=547673228102&dakId=1659429", "scene": null}, {"name": "选择快递品牌", "pathName": "pages/brand/index", "query": "", "scene": null}, {"name": "查看订单", "pathName": "pages/order/index", "query": "", "scene": null}, {"name": "查看订单 - 详情", "pathName": "pages/order/detail/index", "query": "order_id=201210680600000025", "scene": null}, {"name": "查看订单 - 详情-支付", "pathName": "pages/order/pay/index", "query": "order_id=201210680600000025", "scene": null}, {"name": "查看订单 - 批量打印", "pathName": "pages/order/select/index", "query": "", "scene": null}, {"name": "地址薄", "pathName": "pages/address/index", "query": "org=send", "scene": null}, {"name": "地址薄 - 批量选择收件人", "pathName": "pages/address/index", "query": "action=select&org=receive&multiple=1", "scene": null}, {"name": "地址薄 - 编辑", "pathName": "pages/address/edit/index", "query": "org=send", "scene": null}, {"name": "地址薄 - 批量下单", "pathName": "pages/address/batch/index", "query": "", "scene": null}, {"name": "地址薄 - 导入", "pathName": "pages/address/import/index", "query": "", "scene": null}, {"name": "认证实名信息", "pathName": "pages/realname/index", "query": "", "scene": null}, {"name": "我的", "pathName": "pages/user/index", "query": "", "scene": null}, {"name": "我的 - 登录", "pathName": "pages/user/login/index", "query": "", "scene": null}, {"name": "我的 - 关联小程序", "pathName": "pages/help/relation/index", "query": "", "scene": null}, {"name": "关联手机号", "pathName": "pages/user/relation/index", "query": "", "scene": null}, {"name": "关联手机号 - 管理", "pathName": "pages/user/relation/edit/index", "query": "type=family&mobile=15155414101&tag=老婆", "scene": null}, {"name": "关联手机号 - 绑定", "pathName": "pages/user/relation/edit/index", "query": "action=modify&type=self", "scene": null}, {"name": "身份码", "pathName": "pages/IDcode/index", "query": "", "scene": null}, {"name": "pages/query/detail/index", "pathName": "pages/query/detail/index", "query": "waybill=123456789&brand=dp&__key_=16152711241151", "scene": null}, {"name": "pages/order/shop/index", "pathName": "pages/order/shop/index", "query": "", "scene": null}, {"name": "pages/order/edit/index", "pathName": "pages/order/edit/index", "query": "", "scene": null}, {"name": "权益次卡", "pathName": "pages/order/card/index", "query": "q=https%3A%2F%2Fkbydy.cn%2Fc%3FdakId%3D1748861%26cardId%3D113", "scene": 1011}, {"name": "app分享支付", "pathName": "pages-2/pages/order/detail/index", "query": "order_id=703015066208552&fromShare=1&order_random=5DjgSzJk0bY48onP&channel=sharePay", "scene": 1011}, {"name": "app分享实名", "pathName": "pages-2/pages/order/detail/index", "query": "order_id=703035944104975&mode=realname&fromShare=1&order_random=T6BwJ6h1q6b6458k", "scene": 1011}, {"name": "驿站投诉原因列表", "pathName": "pages-0/pages/query/feedback/reason/index", "query": "waybill=800900564664646464&brand=yt&dakId=1614783&__key_=16463777217753", "scene": 1011}, {"name": "疫情查询", "pathName": "pages-3/pages/user/yiqing/index", "query": "", "scene": 1011}]}}, "projectArchitecture": "miniprogram"}