/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { setRelationInfo, setRelationVip, setServiceConfigInfo } from '@/actions/relationInfo';
import { getServicesConfig } from '@/components/_pages/order/extra-info/_utils';
import {
  checkCourierIsVip,
  formatRelationInfo,
} from '@/components/_pages/order/_utils/courier.detail';
import { getRelationConfig } from '@/components/_pages/order/_utils/order.relation';
import {
  checkCustomerAuthStatus,
  checkCustomerIsAdmin,
  createRelation,
  fixRelationInfo,
  getBrandInfo,
  getCourierFromStorage,
  getLastUseRelation,
  getStoreInfo,
  getWkdStoreInfo,
  triggerUpdateInfo,
} from '@/components/_pages/store-card/_utils';
import { relationStorageKey } from '@/utils/config';
import { useUpdate } from '@base/hooks/page';
import logger from '@base/utils/logger';
import { getPage, noop, setStorage } from '@base/utils/utils';
import { useDispatch, useSelector } from '@tarojs/redux';
import Taro, { useEffect } from '@tarojs/taro';
import isEmpty from 'lodash/isEmpty';
import useAuthUserInfo from './auth-user';
import { scanAction } from '~/utils/scan';

/**
 *
 * @description 如果扫码进入首页，且有驿站信息，则屏蔽全局更新逻辑
 */
export async function checkNeedStopGlobalUpdateRelationInfo() {
  if (process.env.MODE_ENV === 'yz') {
    try {
      const page = getPage();
      const { path } = page.$router;
      const scanActionInPages = ['/pages/query/index', '/pages/query/appointment/index']; // 这些页面内触发了scanAction
      if (scanActionInPages.includes(path)) return true;
      const res = await scanAction();
      const { url, dakId: dak_id } = res || {};
      if (url === 'index' && dak_id) {
        return true;
      }
    } catch (error) {}
  }
  return false;
}

/**
 *
 * @description 根据扫描信息，绑定下单关系
 */

export async function createRelationByScanAction(relationInfo) {
  if (process.env.MODE_ENV === 'yz') {
    try {
      const res = await scanAction();
      const { url, dakId: dak_id, ...restData } = res || {};
      if (url === 'index' && dak_id) {
        const { dakId: oldDakId } = relationInfo || {};
        // 扫到的驿站id和上次一样
        if (`${oldDakId}` === `${dak_id}`) {
          return true;
        }
        await createRelation({ dak_id, ...restData });
        return true;
      }
    } catch (error) {}
  }
  return false;
}

/**
 * @description 此方法在@base/components/page组件使用，不允许其他组件使用
 */

Taro.kbRelationInfo = { data: {}, isVip: false };
export const useUpdateRelationInfo = (callback = noop) => {
  const dispatch = useDispatch();
  const triggerUpdateServiceConfig = (relationInfo) => {
    const disabledServiceList = Taro.kbGetGlobalDataOnce('disabledServiceList');
    if (disabledServiceList) return;
    getServicesConfig(relationInfo)
      .then(({ data }) => {
        dispatch(setServiceConfigInfo(data));
      })
      .catch(() => {
        dispatch(setServiceConfigInfo({}));
      });
  };
  // 设置vip快递员
  const triggerUpdateVip = (isVip) => {
    Taro.kbRelationInfo.isVip = isVip;
    dispatch(setRelationVip(isVip));
  };

  // 检查是否vip快递员
  const triggerCheckCourierIsVip = (relationInfo = {}, action = 'normal') => {
    return new Promise((resolve) => {
      const checkCallBackFn = (isVip) => {
        action != 'check' && triggerUpdateVip(isVip);
        resolve(isVip);
      };
      if (process.env.MODE_ENV === 'wkd') {
        // 检查快递员，更新vip状态
        const { type, courier_id } = relationInfo || {};
        if (type === 'courier' && courier_id) {
          checkCourierIsVip(courier_id)
            .then(({ isVip }) => checkCallBackFn(isVip))
            .catch(() => {
              checkCallBackFn(false);
            });
        } else {
          checkCallBackFn(false);
        }
      } else {
        const { is_vip = false } = relationInfo || {};
        triggerUpdateVip(!!is_vip);
        resolve(!!is_vip);
      }
    });
  };

  // 触发更新缓存对象
  const triggerStorageRelation = (relationInfo, isVip = Taro.kbRelationInfo.isVip) => {
    /**
     * @description 缓存下单对象
     * @param isVip 当前下单对象是否为vip快递员
     * storageWay order下单/scan扫码/default默认
     */
    const { type, storageWay } = relationInfo || {};
    // 触发缓存下单对象
    const storageRelationFn = () => {
      let relationInfoData = { ...relationInfo };
      if (relationInfoData.smjData) {
        delete relationInfoData.smjData;
      }
      // 去掉dynamicForms缓存
      relationInfoData.dynamicForms = {};
      setStorage({
        key: relationStorageKey,
        data: relationInfoData,
      });
    };
    const canStorageWays = ['order', 'scan', 'default'];
    // 除同城急送外设置缓存
    if (type !== 'tcjs') {
      getCourierFromStorage().then((storageData) => {
        triggerCheckCourierIsVip(storageData, 'check').then((storageIsVip) => {
          if (storageWay === 'reset') {
            storageRelationFn();
          } else if (storageIsVip) {
            storageWay == 'scan' && isVip && storageRelationFn();
          } else if (!storageIsVip) {
            canStorageWays.includes(storageWay) && storageRelationFn();
          }
        });
      });
    }
  };

  Taro.kbTriggerStorageRelation = triggerStorageRelation;

  // 设置下单关系
  const triggerDispatch = (opts, isDefault = false) => {
    if (!opts || isEmpty(opts) || interceptDefaultSet(isDefault)) return;
    const { disabled = false } = Taro.kbRelationInfo.data;
    const relationInfo = { disabled, ...opts, isDefault };
    logger.info('设置下单关系-triggerDispatch', relationInfo.type);
    Taro.kbRelationInfo.data = { ...relationInfo };
    dispatch(setRelationInfo(relationInfo));
    callback(relationInfo);
    //更新缓存，同时也会触发检查设置vip快递员
    triggerCheckCourierIsVip(relationInfo).then(() => {
      triggerStorageRelation(relationInfo);
    });
    // 更新增值服务内容
    triggerUpdateServiceConfig(relationInfo);
  };
  // 非空状态拦截默认下单关系更新
  const interceptDefaultSet = (isDefault = false) => {
    const { data: { brand, platform, type } = {} } = Taro.kbRelationInfo || {};
    const notEmpty = !isEmpty(Taro.kbRelationInfo.data);
    const notYjkdBrand = !(type == 'brand' && brand == 'yjkd' && !platform);
    return isDefault && notEmpty && notYjkdBrand;
  };

  // 修正大客户
  const fixCustomerInfo = (relationInfo, isDefault) => {
    const { customer } = relationInfo;
    const { id: customer_id } = customer || {};
    if (customer_id) {
      checkCustomerIsAdmin({ customer_id })
        .then((isCanAdmin) => {
          relationInfo.customer.isCanAdmin = !!isCanAdmin;
          checkCustomerAuthStatus(customer_id)
            .then((customerAuthStatus) => {
              relationInfo.customer.customerAuthStatus = customerAuthStatus;
              if (customerAuthStatus != 0) triggerDispatch(relationInfo, isDefault);
            })
            .catch((err) => {
              console.log('err', err);
            });
        })
        .catch((err) => {
          console.log('err', err);
        });
      return true;
    }
    return false;
  };

  // 修正快递员
  const fixCourierInfo = (relationInfo, isDefault) => {
    const { account_phone, courier_id } = relationInfo;
    if (!courier_id && account_phone) {
      getWkdStoreInfo({ phone: account_phone }).then((data) => {
        data && triggerDispatch(formatRelationInfo(data), isDefault);
      });
      return true;
    }
    return false;
  };

  // 修正快递品牌
  const fixBrandInfo = (relationInfo, isDefault) => {
    const { type, brand, describe } = relationInfo;
    if (type === 'brand' && !describe) {
      getBrandInfo(brand)
        .then((data) => {
          const { message } = data;
          triggerDispatch({ ...relationInfo, describe: message }, isDefault);
        })
        .catch(() => triggerDispatch(relationInfo, isDefault));
      return true;
    }
    return false;
  };

  // 修正驿站信息
  const fixDakInfo = (relationInfo, isDefault) => {
    const { type, dakId, dak_id = dakId, inn_name } = relationInfo;
    if (type === 'dak' && !inn_name) {
      getStoreInfo(dak_id)
        .then((data) => {
          let dakData = formatRelationInfo({ type: 'dak', ...data });
          triggerDispatch({ ...relationInfo, ...dakData }, isDefault);
        })
        .catch(() => triggerDispatch(relationInfo, isDefault));
      return true;
    }
    return false;
  };

  useEffect(() => {
    Taro.kbUpdateRelationInfo = (data, isDefault) => {
      return new Promise((resolve, reject) => {
        const { limitUpdate = noop, ...restData } = data;
        if (limitUpdate(Taro.kbRelationInfo.data, restData, isDefault))
          return reject({ code: 1000, msg: '外部阻止的切换动作' });
        // 默认设置下单关系时，如果已经有下单关系则跳过；
        if (interceptDefaultSet(isDefault))
          return reject({ code: 1000, msg: '默认下单关系不能顶替切换的下单关系' });
        // 格式化下单对象
        const relationInfo =
          process.env.MODE_ENV === 'wkd' ? formatRelationInfo(restData) : restData;
        if (process.env.MODE_ENV === 'wkd') {
          if (isDefault) {
            logger.info('更新下单关系-默认', data && data.type, isEmpty(Taro.kbRelationInfo.data));
          }

          const { limitUpdate = noop, ...restData } = data;

          // 格式化下单对象
          let relationInfo = formatRelationInfo(restData);
          getRelationConfig(relationInfo).then((relationInfo) => {
            if (limitUpdate(Taro.kbRelationInfo.data, restData, isDefault)) return reject();
            // 默认设置下单关系时，如果已经有下单关系则跳过；
            if (interceptDefaultSet(isDefault)) return reject();

            // 修正：快递品牌无描述信息时请求获取描述
            if (fixBrandInfo(relationInfo, isDefault)) return reject();

            // 修正：驿站无驿站名字信息时请求获取描述
            if (fixDakInfo(relationInfo, isDefault)) return reject();

            // 修正：大客户追加isCanAdmin
            if (fixCustomerInfo(relationInfo, isDefault)) return reject();

            // 修正：快递员信息缺少courier_id
            if (fixCourierInfo(relationInfo, isDefault)) return reject();
            resolve(relationInfo);
            triggerDispatch(relationInfo, isDefault);
          }, reject);
        } else {
          fixRelationInfo({ ...relationInfo }).then((relation) => {
            triggerUpdateInfo(relation).then((data) => {
              triggerDispatch({ ...data }, isDefault);
            }, reject);
          }, reject);
        }
      });
    };
  }, []);

  useUpdate(async (loginData) => {
    // 空下单关系从缓存获取
    if (!loginData.logined || !isEmpty(Taro.kbRelationInfo.data)) return;

    const isStop = await checkNeedStopGlobalUpdateRelationInfo();

    if (isStop) return;

    getLastUseRelation()
      .then((data) => {
        data.storageWay = 'default';
        Taro.kbUpdateRelationInfo(data, true);
      })
      .catch((err) => console.log(err));
  });
};

/**
 *
 * @description 获取下单关系
 * @returns
 */
export const useGetRelationInfo = () => {
  const info = useSelector(({ global }) => global.relationInfo);
  return info;
};

/**
 * @description pageEffect
 */
export function usePageEffect() {
  useUpdateRelationInfo();
  useAuthUserInfo();
}
