/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { capsuleBounding, pagePaths } from '@base/config';
import { checkIsTabPage } from '@base/utils/navigator';
import { getPage, getSystemInfoSync, isAvailableValue } from '@base/utils/utils';
import Taro, { useMemo } from '@tarojs/taro';

export const dealPageNavBack = (opts) => {
  const { delta = 1 } = opts || {};
  const total = Taro.getCurrentPages().length;
  if (total > delta) {
    // 总页数大于返回页数
    return Taro.navigateBack({
      delta,
    });
  }
  Taro.navigator({
    url: pagePaths.homePage,
    target: 'tab',
  });
  return Promise.resolve();
};

/**
 * @description 导航关键数据
 */
export function useGetMenuButtonBoundingClientRect() {
  const bounding = useMemo(() => {
    try {
      return Taro.getMenuButtonBoundingClientRect();
    } catch (error) {
      const { statusBarHeight = 24, platform = '' } = getSystemInfoSync();
      const b = capsuleBounding[platform] || capsuleBounding.android;
      b.top = statusBarHeight + b.top;
      return b;
    }
  }, []);
  return bounding;
}

/**
 *
 * @description 导航关键数据
 */
export function useNavBarCriticalData(props) {
  const data = useMemo(() => {
    let { statusBarHeight = 24, titleBarHeight = 0, platform = '' } = getSystemInfoSync();
    const boundingClientRect = Taro.getMenuButtonBoundingClientRect();
    const isPc = platform === 'window'; // pc端兼容
    if (!titleBarHeight) {
      // 注意如果getMenuButtonBoundingClientRect获取有异常，考虑还是用capsuleBounding配置
      let { top, height } = Taro.getMenuButtonBoundingClientRect();
      let topSpace = top - statusBarHeight;
      // 兼容微信的标题高度
      if (!top || !height) {
        const { top: t, height: h } = capsuleBounding[platform] || capsuleBounding.android;
        topSpace = t;
        height = h;
      }
      titleBarHeight = height + 2 * topSpace;
    }
    if (isPc) {
      statusBarHeight = 0;
    }

    // 当前页信息
    const currentPage = getPage();
    const prePage = getPage(-2);
    const {
      $router: { path } = {},
      config: {
        navigationStyle = '',
        navigationBarTitleText = '',
        navigationBarTitleTextAlipay,
      } = {},
    } = currentPage;
    const { title = navigationBarTitleTextAlipay || navigationBarTitleText } = props;
    // 是否显示自定义导航组件
    const isOpened = navigationStyle === 'custom' && isAvailableValue(title);
    // 是否有上一页
    const hasPrePage = path && prePage !== currentPage;
    // 是否是tab页面
    const isTabPage = checkIsTabPage(path);
    return {
      isPc,
      title,
      isOpened,
      hasPrePage,
      isTabPage,
      currentPage,
      titleBarHeight,
      statusBarHeight,
      boundingClientRect,
    };
  }, []);

  return data;
}
