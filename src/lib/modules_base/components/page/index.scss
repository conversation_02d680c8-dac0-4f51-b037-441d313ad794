/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 上下分布布局
.kb-page {
  /*  #ifdef alipay  */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: $color-black-1;
  /*  #endif  */

  &-cover {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow-x: hidden;
  }

  &-cover &__body {
    position: relative;
    flex-grow: 1;

    &-view {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    &-scroll {
      height: 100%;
    }
  }

  &-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    background-color: $color-grey-8;
  }

  &__footer {
    &-view {
      padding-bottom: 0;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}
