/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isEqual from 'lodash/isEqual';
import isNull from 'lodash/isNull';
import CallbacksPool from '~base/utils/callbacksPool';
import request from '.';
import { getPage, noop } from '../utils';

// 页面卸载，重置当前页面上的缓存
function pageUnload(res) {
  const responseCacheMap = RequestOnceManager.getResponseCache();
  const urls = Object.keys(responseCacheMap).filter((url) => {
    const catchGroup = responseCacheMap[url];
    if (catchGroup && catchGroup.some((item) => item.path === `/${res.path}`)) {
      return true;
    }
    return false;
  });
  urls.forEach((url) => {
    RequestOnceManager.reset(url);
  });
}

const ResponseCallbacksManager = new CallbacksPool();

class RequestOnce {
  constructor() {
    this.responseCacheMap = {};
    this.responseLockMap = {};
  }

  // 锁定请求
  lockRequest(url, req) {
    this.responseLockMap[url] = req;
  }

  // 解锁请求
  unlockRequest(url) {
    this.responseLockMap[url] = null;
  }

  // 仅当参数完全相等时锁定生效
  checkIsLocked(url, req) {
    const reqCache = this.responseLockMap[url];
    return reqCache && isEqual(req, reqCache);
  }

  // 缓存响应结果
  setResponseCache(url, res, req) {
    if (isNull(res)) {
      this.responseCacheMap[url] = null;
      return;
    }
    const { $router: { path } = {} } = getPage();
    const resGroups = this.responseCacheMap[url] || [];
    const index = resGroups.findIndex((item) => isEqual(item.req, req));
    const catchItem = { res, req, path };
    if (index >= 0) {
      resGroups[index] = catchItem;
    } else {
      resGroups.push(catchItem);
    }
    this.responseCacheMap[url] = resGroups;
  }

  // 获取响应缓存
  getResponseCache(url, req) {
    if(!url) return this.responseCacheMap;
    const resGroups = this.responseCacheMap[url] || [];
    const index = resGroups.findIndex((item) => isEqual(item.req, req));
    if (index >= 0) {
      return resGroups[index].res;
    }
    return null;
  }

  // 触发回调
  onThen({ url, res, req, onThen }) {
    // 记录缓存
    if (`${res.code}` === '0') {
      this.setResponseCache(url, res, req);
    } else {
      this.unlockRequest(url);
    }
    ResponseCallbacksManager.trigger({ res, req });

    onThen(res, req);
  }

  // 请求拦截
  onIntercept({ url, req, force, onThen, onIntercept }) {
    const isLocked = this.checkIsLocked(url, req);
    const cacheRes = this.getResponseCache(url, req);

    if (cacheRes && !force) {
      Taro.offBeforePageUnload(pageUnload);
      Taro.onBeforePageUnload(pageUnload);
      onThen(cacheRes, req);
      return true;
    }

    if (isLocked) {
      ResponseCallbacksManager.push(({ res, req }) => onThen(res, req));
      return true;
    }

    // 锁定请求
    this.lockRequest(url, req);

    return onIntercept(req);
  }

  // 重置状态
  reset(url) {
    this.unlockRequest(url);
    this.setResponseCache(url, null);
  }
}

export const RequestOnceManager = new RequestOnce();

// 声明周期内，仅请求一次
export function requestGetOnce(opts, force) {
  const { url, data = {}, onIntercept = noop, onThen = noop, ...restOpts } = opts;

  return request({
    ...restOpts,
    url,
    data,
    onIntercept: (req) => RequestOnceManager.onIntercept({ url, req, force, onThen, onIntercept }),
    onThen: (res, req) => RequestOnceManager.onThen({ url, req, res, onThen }),
  });
}
