/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 颜色
.kb-color {
  &__black {
    color: $color-black-1 !important;
  }

  &__greyer {
    color: $color-grey-1 !important;
  }

  &__grey {
    color: $color-grey-2 !important;
  }

  &__grey-3 {
    color: $color-grey-3 !important;
  }

  &__grey-4 {
    color: $color-grey-4 !important;
  }
  &__grey-ddd {
    color: $color-grey-ddd !important;
  }

  &__brand {
    color: $color-brand !important;
  }

  &__brand-light {
    color: $color-brand-light !important;
  }

  &__brand-lighter {
    color: $color-brand-lighter !important;
  }

  &__brand-lightest {
    color: $color-brand-lightest !important;
  }

  &__blue {
    color: $color-blue !important;
  }

  &__red {
    color: $color-red !important;
  }

  &__white {
    color: $color-white !important;
  }

  &__orange {
    color: $color-orange !important;
  }

  &__yellow {
    color: $color-yellow !important;
  }

  &__green {
    color: $color-green !important;
  }

  &__green1 {
    color: $color-green-1 !important;
  }

  &__green-yz {
    color: $color-green-yz !important;
  }
}

// 字体大小
.kb-size__bold {
  font-weight: bold;
}

.kb-size {
  &__xs {
    font-size: $font-size-xs !important;
  }

  &__sm {
    font-size: $font-size-sm !important;
  }

  &__base {
    font-size: $font-size-base !important;
  }

  &__base2 {
    font-size: $font-size-base2 !important;
  }

  &__lg {
    font-size: $font-size-lg !important;
  }

  &__xl {
    font-size: $font-size-xl !important;
  }

  &__xxl {
    font-size: $font-size-xxl !important;
  }
}
