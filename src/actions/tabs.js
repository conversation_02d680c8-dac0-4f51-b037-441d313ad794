/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { save } from '@base/actions/global';

// 更新选中的tabIndex
export const updateSelectedTabIndex = (selectedTabIndex = -1) => {
  return (dispatch) =>
    dispatch(
      save({
        selectedTabIndex,
      }),
    );
};

// 更新tabBar红点与徽标
export const updateBadgeMap = (badgeMap = {}) => {
  return (dispatch) =>
    dispatch(
      save({
        badgeMap,
      }),
    );
};

// tabBar显示隐藏
export const updateTabBarShow = (showTabBar) => {
  return (dispatch) =>
    dispatch(
      save({
        showTabBar,
      }),
    );
};

// 处理某个tab显示隐藏
export const updateTabBarMap = (tabBarMap) => {
  return (dispatch) =>
    dispatch(
      save({
        tabBarMap,
      }),
    );
};
