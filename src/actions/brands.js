/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { STORAGE_BRANDS } from '@/constants/business';
import { save } from '@base/actions/global';
import { apis } from '@base/config';
import CallbacksPool from '@base/utils/callbacksPool';
import extraBrands from '@base/utils/extra-brands';
import request from '@base/utils/request';
import { debounce, getStorage, setStorage } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';

const filter = (keys) => {
  const brands = Taro.brands;
  if (!keys) return brands;
  const data = {};
  if (isArray(keys)) {
    keys.map((key) => {
      data[key] = brands[key];
    });
    return data;
  } else {
    return brands[keys];
  }
};

const callbackIns = new CallbacksPool();

/**
 *
 * @description 补充缓存获取逻辑
 * @returns
 */
function getBrandsFromCache() {
  return new Promise((resolve) => {
    if (Taro.brands) {
      resolve();
      return;
    }
    getStorage({
      key: STORAGE_BRANDS,
    })
      .then((res) => {
        const { data: { data } = {} } = res || {};
        if (data) {
          Taro.brands = data;
        }
        resolve();
      })
      .catch(resolve);
  });
}

/**
 *
 * @description 后台拉取品牌列表
 * @returns
 */
let lock = false;
function getBrandsFromRemote() {
  return new Promise((resolve) => {
    if (lock) return;
    lock = true;
    request({
      mastLogin: false,
      toastSuccess: false,
      toastLoading: false,
      toastError: false,
      url: apis['getBrandAll'],
      onThen: (data) => {
        lock = false;
        const mergeData = {
          ...extraBrands,
          ...data,
        };
        delete mergeData.qita;
        Taro.brands = mergeData;
        resolve(mergeData);
        setStorage({
          key: STORAGE_BRANDS,
          data: mergeData,
        });
      },
    });
  });
}

/**
 * 获取缓存额快递品牌版本号
 */
const brandVersionKey = 'brands_version';
function getStorageBrandVersion() {
  return new Promise((resolve) => {
    getStorage({
      key: brandVersionKey,
    })
      .then((res) => {
        const { data: version } = res.data || {};
        resolve({ version });
      })
      .catch(resolve);
  });
}

/**
 * 比较快递品牌版本号，检查是否更新
 * @param {*} params
 * @returns
 */
let checkBrandLock = false;
function checkAndRefreshBrand() {
  // 检查过版本号后不再触发检查，直接走缓存
  // 检查版本号接口需要防抖
  return new Promise((resolve) => {
    if (checkBrandLock) {
      resolve(false);
      return;
    }
    getStorageBrandVersion().then((data) => {
      const { version: storageVersion = '' } = data || {};
      request({
        url: apis['getBrandAllVersion'],
        toastLoading: false,
        onThen: ({ data: version }) => {
          checkBrandLock = true;
          resolve(!version || storageVersion !== version);
          setStorage({
            key: brandVersionKey,
            data: version,
          });
        },
      });
    });
  });
}

const checkAndRefreshBrandDebounce = debounce(checkAndRefreshBrand, 300, {
  leading: true,
  trailing: false,
});

export const get = (params = {}) => {
  const { filterKeys, dataKey = 'brands', then = () => {} } = params;
  return (dispatch) => {
    const triggerSet = (data) => {
      dispatch(
        save({
          [dataKey]: filter(filterKeys),
        }),
      );
      // 保证所有回调正确执行
      callbackIns.trigger(data);
    };

    const triggerGet = () => {
      getBrandsFromRemote().then((data) => {
        triggerSet(data);
      });
    };

    const triggerStorageGet = () => {
      getBrandsFromCache().then(() => {
        if (Taro.brands) {
          triggerSet(Taro.brands);
          return;
        }
        triggerGet();
      });
    };

    callbackIns.push(then);
    checkAndRefreshBrandDebounce().then((refresh) => {
      if (refresh) {
        triggerGet();
      } else {
        triggerStorageGet();
      }
    });
  };
};
