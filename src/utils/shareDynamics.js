import isEmpty from 'lodash/isEmpty';
import qs from 'qs';
import { useState } from '@tarojs/taro';
import { getPage } from '~base/utils/utils';

const shareUrlProtocol = 'share://';

/**
 *
 * @description 格式化分享信息
 * @param {*} data
 * @returns
 */
function formatShareInfo(data) {
  const { adUrl, title } = data || {};
  const [path, imageUrl] = `${adUrl}`.split(';');
  if (`${path}`.startsWith(shareUrlProtocol)) {
    return {
      title,
      path: `${path}`.replace(shareUrlProtocol, ''),
      imageUrl,
    };
  }
  return null;
}

/**
 *
 * @description 检查是否可以展示动态分享按钮：广告位
 */
export function useCheckShowDynamicsShare() {
  const [isShow, setIsShow] = useState(false);

  // 更新
  const update = (data) => {
    setIsShow(!!formatShareInfo(data));
  };

  return {
    isShow,
    update,
  };
}

let dynamicsShareInfo = null;
/**
 *
 * @description 获取动态分享信息：广告位
 */
export function getDynamicsShareInfo() {
  if (dynamicsShareInfo) {
    const dynamicsShareInfoTmp = { ...dynamicsShareInfo };
    dynamicsShareInfo = null;
    return dynamicsShareInfoTmp;
  }
  return null;
}

/**
 *
 * @description 设置动态分享信息：广告位
 */
export function setDynamicsShareInfo(opts) {
  const shareInfo = formatShareInfo(opts);
  dynamicsShareInfo = shareInfo;
  return !!shareInfo;
}

/**
 *
 * @param {Object} opt
 * @param {string} opt.title 分享标题
 * @param {string} opt.imageUrl 分享图片链接
 * @param {string} opt.path 分享页面路径
 * @param {string} opt.params 分享页面路径参数
 * @returns {Object} 分享信息
 */
export const createDynamicsShareInfo = (opt) => {
  const { title, path, imageUrl, params } = opt || {};
  const page = getPage();
  const { path: share_path, params: share_params } = page.$router;
  const _path = path || share_path;
  const _params = params || share_params || {};
  const shareUrl = `${_path}${!isEmpty(_params) ? `?${qs.stringify(_params)}` : ''}`;
  return {
    title,
    adUrl: `share://${shareUrl};${imageUrl}`,
  };
};
