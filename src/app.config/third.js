/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/query/index',
    'pages/query/appointment/index',
    'pages/query/detail/index',
    'pages/query/feedback/index',
    'pages/query/feedback/reason/index',
    'pages/query/match/index',
    'pages/brand/index',
    'pages/calendar/index',
    'pages/cutting/index',
    'pages/help/index',
    'pages/help/relation/index',
    'pages/realname/index',
    'pages/user/index',
    'pages/user/relation/index',
    'pages/user/relation/edit/index',
    'pages/user/qrcode/index',
    'pages/user/login/index',
    'pages/index/index',
    'pages/IDcode/index',
    'pages/city/index',
    'pages/webview/index',
    'pages/order/shop/index',
    'pages/order/station/index',
    'pages/query/operate/index',
    'pages/order/voucher/index',
    'pages/query/appointment/delivery/index',
    'pages/address/index',
    'pages/address/edit/index',
    'pages/welfare/reward/index',
    'pages/welfare/reward/record/index',
    'pages/ws/query/index',
    'pages/query/list/index',
  ],
  window: {
    navigationBarBackgroundColor: '#1abba1',
    navigationBarTitleText: '快宝驿站',
    backgroundColor: '#f2f2f2',
    enablePullDownRefresh: false,
  },
  tabBar: {
    color: '#666666',
    selectedColor: '#1abba1',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/query/index',
        iconPath: 'assets/tab/pickup.png',
        selectedIconPath: 'assets/tab/pickup-active.png',
        text: '取件',
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/user.png',
        selectedIconPath: 'assets/tab/user-active.png',
        text: '我的',
      },
    ],
  },
};

module.exports = config;
