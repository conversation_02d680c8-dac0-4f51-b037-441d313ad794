/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/query/index',
    'pages/query/appointment/index',
    'pages/query/detail/index',
    'pages/order/card/index',
    'pages/order/edit/index',
    'pages/order/edit/share/index',
    'pages/order/edit/temporary/index',
    'pages/user/index',
    'pages/webview/index',
    'pages/welfare/continuity/index',
    'pages/queryExpress/notice/notice',
    'pages/queryExpress/result/result',
    'pages/sendRecord/detail/detail',
    'pages/sendRecord/voucher/voucher',
    'pages/pickup/pickup',
  ],
  window: {
    navigationBarBackgroundColor: '#099dff',
    navigationBarTitleText: '微快递',
    backgroundColor: '#f2f2f2',
    enablePullDownRefresh: false,
  },
  tabBar: {
    color: '#666666',
    selectedColor: '#099dff',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/query/index',
        iconPath: 'assets/tab/wkd/search.png',
        selectedIconPath: 'assets/tab/wkd/search-active.png',
        text: '查快递',
      },
      {
        pagePath: 'pages/order/edit/index',
        iconPath: 'assets/tab/wkd/send.png',
        selectedIconPath: 'assets/tab/wkd/send-active.png',
        text: '寄快递',
      },
      {
        pagePath: 'pages/welfare/continuity/index',
        iconPath: 'assets/tab/wkd/welfare.png',
        selectedIconPath: 'assets/tab/wkd/welfare-active.png',
        text: '福利',
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/wkd/user.png',
        selectedIconPath: 'assets/tab/wkd/user-active.png',
        text: '我的',
      },
    ],
  },
};

// 分包
config.subPackages = [
  {
    root: 'pages',
    preloadRule: 'pages/query/index',
    pages: [
      // 快递柜
      'kdg/kdg',
      'kdg/result/index',
      // 品牌选择
      'brand/index',
      // 查件
      'IDcode/index',
      'query/list/index',
      'query/feedback/index',
      'query/feedback/reason/index',
      'query/match/index',
      'query/notice/index',
      'query/detail/target/index',
      // 福利中心
      'welfare/welfare',
      'welfare/center/center',
      'welfare/commission/commission',
      'welfare/invitation/invitation',
      'welfare/inviteList/inviteList',
      'welfare/lend/lend',
      'welfare/profitList/profitList',
      'welfare/record/record',
      'welfare/share/share',
      'welfare/withdrawal/withdrawal',
      'welfare/lotterys/index',
      'welfare/lotterys/details',
      'welfare/lotterys/target/index',
      'welfare/lotterys/list',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/edit/index',
    pages: [
      // 信用支付 - 微信支付分
      'order/credit-pay/index',
      // 同城急送
      'order/delivery/index',
      'order/delivery/goods/index',
      'order/delivery/append/index',
      'order/delivery/address/index',
      'order/delivery/address/edit/index',
      'order/delivery/address/choose/index',
      // 底单
      'order/voucher/index',
      // 地址
      'address/index',
      'address/batch/index',
      'address/edit/index',
      'address/import/index',
      'address/confirm/index',
      // 日期
      'calendar/index',
      // 图片剪切
      'cutting/index',
      // 城市选择
      'city/index',
      // 实名
      'realname/index',
      'realname/scan/index',
      // 实名列表
      'realnameList/index',
      // 下单
      'order/edit/goods/index',
      'order/edit/service/index',
      'customer/index',
      'customer/account/index',
      'customer/account/memberList/index',
      'customer/member/index',
      'customer/message/index',
      'customer/message/record/index',
      'customer/printer/index',
      'customer/qrcode/index',
      'customer/queryCode/index',
      'order/share/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/edit/index',
    pages: [
      // 订单
      'order/amount/index',
      'order/cancel/index',
      'order/courier/check/index',
      'order/courier/message/index',
      'order/detail/index',
      'order/history/index',
      'order/integral/index',
      'order/integral/detail/index',
      'order/pic/index',
      'order/pay/index',
      'order/result/index',
      'order/relation/index',
      'order/select/index',
      'order/service/index',
      'order/station/index',
      'order/qj-detail/index',
      'order/print-scan/index',
      // 发票
      'invoice/index',
      'invoice/edit/index',
      'invoice/detail/index',
      'invoice/result/index',
      'invoice/title/index',
      'invoice/extra/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: [
      // 微商相关
      'ws/index',
      'ws/detail/index',
      'ws/query/index',
      'ws/order/edit/index',
      'ws/order/edit-yhj/index',
      'ws/bill/index',
      'ws/bill/detail/index',
      // 帮助文档
      'help/index',
      'help/relation/index',
      // 我的
      'user/relation/index',
      'user/setting/index',
      'user/relation/edit/index',
      'user/login/index',
      'user/ecode/index',
      'user/ecode/edit/index',
      'user/ecode/detail/index',
      'user/message/index',
      'user/message/detail/index',
      'user/service/index',
      'user/service/detail/index',
      'user/qrcode/index',
      'user/wallet/index',
      // 疫情查询
      'user/yiqing/index',
      'user/yiqing/detail/index',
      'user/yiqing/rank/index',
      // 会员相关
      'user/member/index',
      'user/member/right/index',
      'user/member/invite/index',
      'order/index',
      // 运费比价
      'user/price/index',
      // 停发区查询
      'closedArea/index',
      'closedArea/result/index',
      'closedArea/selectBrand/index',
    ],
  },
  {
    root: 'pages',
    pages: ['out/choose-file/index'],
  },
];

/**
 * @description 切换tab 福利=》订单
 */
function switchWelfarePage() {
  const welfareTabIndex = config.tabBar.list.findIndex(
    (item) => item.pagePath == 'pages/welfare/continuity/index',
  );
  welfareTabIndex > -1 && config.tabBar.list.splice(welfareTabIndex, 1);
  const welfarePageIndex = config.pages.findIndex(
    (item) => item === 'pages/welfare/continuity/index',
  );
  config.pages.splice(welfarePageIndex, 1);
}

if (process.env.PLATFORM_ENV === 'alipay') {
  // 去除福利tab和页面
  switchWelfarePage();
  // // 支付宝去除分包
  // config.subPackages.map(item => {
  //   config.pages.push(...item.pages.map(path => `${item.root}/${path}`));
  // });
  // config.subPackages = [];
  config.pages.push(
    ...[
      'pages/bill/bill',
      'pages/bill/detail/index',
      'pages/webview-ghost/index',
      'pages/retain/index',
      'pages/welfare/coupon/index',
      'pages/retain/retain',
      'pages/welfare/attention/index',
      'pages/realnameAuth/realnameAuth',
    ],
  );
  config.plugins = {
    love: {
      version: '*',
      provider: '2021001131694653',
    },
    ZhiMaCredit: {
      version: '*',
      provider: '2021002151672975',
    },
    gomini: {
      version: '*',
      provider: '2021001185604232',
    },
  };
  config.window.enableInPageRenderInput = 'YES';
} else if (process.env.PLATFORM_ENV === 'swan') {
  // 去除福利tab和页面
  switchWelfarePage();
} else {
  config.functionalPages = {
    independent: true,
  };
  config.miniApp = {
    useAuthorizePage: true,
  };
  if (process.env.PLATFORM_ENV === 'weapp') {
    config.embeddedAppIdList = [
      'wx16af9eb52c6961c0',
      'wx1ac840bddf4b78cf',
      'wx61de8f0176155640',
      'wxf1a81af0f4e32ade',
      'wx9a713fd690f464c5',
      'wx9e33d0d6b7227315',
      'wxa8f61aded5dbe320',
    ];
  }
}

module.exports = config;
