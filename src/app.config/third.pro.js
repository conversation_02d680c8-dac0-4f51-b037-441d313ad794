/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/query/index',
    'pages/query/appointment/index',
    'pages/query/detail/index',
    'pages/user/index',
    'pages/user/relation/edit/index',
    'pages/user/login/index',
    'pages/index/index',
    'pages/IDcode/index',
    'pages/webview/index',
    'pages/order/index',
    'pages/order/card/index',
    'pages/order/edit/index',
    'pages/welfare/reward/index',
    'pages/welfare/reward/record/index',
  ],
  window: {
    navigationBarBackgroundColor: '#1abba1',
    navigationBarTitleText: '快宝驿站',
    backgroundColor: '#f2f2f2',
    enablePullDownRefresh: false,
  },
  tabBar: {
    color: '#666666',
    selectedColor: '#1abba1',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/query/index',
        iconPath: 'assets/tab/pickup.png',
        selectedIconPath: 'assets/tab/pickup-active.png',
        text: '取件',
      },
      {
        pagePath: 'pages/order/edit/index',
        iconPath: 'assets/tab/send.png',
        selectedIconPath: 'assets/tab/send-active.png',
        text: '寄件',
      },
      {
        pagePath: 'pages/order/index',
        iconPath: 'assets/tab/order.png',
        selectedIconPath: 'assets/tab/order-active.png',
        text: '订单',
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/user.png',
        selectedIconPath: 'assets/tab/user-active.png',
        text: '我的',
      },
    ],
  },
};
config.subPackages = [
  {
    root: 'pages',
    preloadRule: 'pages/query/index',
    pages: [
      'query/feedback/index',
      'query/feedback/reason/index',
      'query/match/index',
      'query/operate/index',
      'brand/index',
      'query/appointment/delivery/index',
      'query/list/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/edit/index',
    pages: [
      'calendar/index',
      'cutting/index',
      'address/index',
      'address/batch/index',
      'address/edit/index',
      'address/import/index',
      'address/confirm/index',
      'realname/index',
      'city/index',
      'order/integral/index',
      'order/integral/detail/index',
      'order/integral/record/index',
      'order/integral/dak/index',
      'order/shop/index',
      'order/pay/index',
      'order/result/index',
      'order/relation/index',
      'order/edit/goods/index',
      'order/edit/service/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/index',
    pages: [
      'order/courier/message/index',
      'order/select/index',
      'order/station/index',
      'order/detail/index',
      'order/voucher/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: [
      'help/index',
      'help/relation/index',
      'user/relation/index',
      'user/qrcode/index',
      'order/history/index',
      'ws/query/index',
    ],
  },
];
if (process.env.PLATFORM_ENV === 'weapp') {
  config.plugins = {
    payPlugin: {
      version: '2.2.0',
      provider: 'wx1a6252fee4475cc1',
    },
  };
}
module.exports = config;
