/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

module.exports = (function () {
  const MODE_ENV = process.env.MODE_ENV || 'yz';
  const config = require(`./${MODE_ENV}${
    process.env.PLATFORM_ENV === 'alipay' && MODE_ENV === 'third.pro' ? '.alipay' : ''
  }`);
  const { navigationBarBackgroundColor, backgroundColor } = config.window;
  config.plugins = config.plugins || {};
  if (process.env.PLATFORM_ENV === 'alipay') {
    // 支付宝 alipay
    // config.window.enableInPageRenderInput = "YES";
    config.window.allowsBounceVertical = 'NO';
    config.window.backgroundImageColor = navigationBarBackgroundColor;
    config.useDynamicPlugins = true;
    if (MODE_ENV !== 'third.pro') {
      config.plugins.subscribeMsg = {
        version: '*',
        provider: '2021001155639035',
      };
    }
  } else if (process.env.PLATFORM_ENV === 'weapp') {
    // 微信 weapp
    config.window.backgroundColorTop = navigationBarBackgroundColor;
    config.window.backgroundColorBottom = backgroundColor;
    config.window.navigationBarTextStyle = 'white';
    config.window.backgroundTextStyle = 'dark';
    config.permission = {
      'scope.userLocation': {
        desc: '快捷方便的帮助用户获取位置信息',
      },
    };
    config.plugins.speechRecognition = {
      version: '0.3.4',
      provider: 'wx069ba97219f66d99',
    };
    config.lazyCodeLoading = 'requiredComponents';
    let requiredPrivateInfos = ['getLocation', 'chooseLocation', 'chooseAddress'];
    if (MODE_ENV === 'yz' || MODE_ENV === 'third.post' || MODE_ENV === 'third.pro') {
      requiredPrivateInfos = [
        'getLocation',
        'chooseLocation',
        'chooseAddress',
        'startLocationUpdate',
        'onLocationChange',
      ];
    }
    config.requiredPrivateInfos = requiredPrivateInfos;
  }

  config.networkTimeout = {
    request: 30000,
    connectSocket: 60000,
    uploadFile: 30000,
    downloadFile: 30000,
  };
  config.resizable = false;
  config.debug = false;
  return config;
})();
