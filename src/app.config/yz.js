/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/query/index',
    'pages/query/detail/index',
    'pages/query/appointment/index',
    'pages/user/index',
    'pages/user/login/index',
    'pages/index/index',
    'pages/IDcode/index',
    'pages/webview/index',
    'pages/order/index',
    'pages/order/edit/index',
    'pages/order/card/index',
    'pages/user/relation/edit/index',
    'pages/welfare/reward/index',
    'pages/welfare/reward/record/index',
    'pages/wxpay/index',
  ],
  window: {
    navigationBarBackgroundColor: '#1abba1',
    navigationBarTitleText: '快宝驿站',
    backgroundColor: '#f2f2f2',
    enablePullDownRefresh: false,
  },
  tabBar: {
    custom: process.env.PLATFORM_ENV === 'weapp',
    color: '#666666',
    selectedColor: '#1abba1',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/query/index',
        iconPath: 'assets/tab/pickup.png',
        selectedIconPath: 'assets/tab/pickup-active.png',
        text: '取件',
      },
      {
        pagePath: 'pages/order/edit/index',
        iconPath: 'assets/tab/send.png',
        selectedIconPath: 'assets/tab/send-active.png',
        text: '寄件',
      },
      {
        pagePath: 'pages/order/index',
        iconPath: 'assets/tab/order.png',
        selectedIconPath: 'assets/tab/order-active.png',
        text: '订单',
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/user.png',
        selectedIconPath: 'assets/tab/user-active.png',
        text: '我的',
      },
    ],
  },
};
config.subPackages = [
  {
    root: 'pages',
    preloadRule: 'pages/query/index',
    pages: [
      // 快递柜
      'kdg/express/index',
      'kdg/storage/index',
      'kdg/storage/pay/index',
      'kdg/storage/result/index',
      'kdg/pickup-result/index',
      // 查件
      'query/feedback/index',
      'query/feedback/reason/index',
      // 投诉反馈对话页面
      'query/feedback/conversation/index',
      'query/match/index',
      'query/operate/index',
      'brand/index',
      'query/appointment/delivery/index',
      'query/appointment/delivery-address/index',
      'query/appointment/delivery-result/index',
      'query/appointment/self-out/index', // 自助出库
      'query/appointment/self-out/result/index', // 自助出库结果
      'query/list/index',
      'pickup/code/index',
      'pickup/pay/index',
      'pickup/pay-cabinet/index', // 柜机支付
      'pickup/pay-cabinet/result/index', // 柜机支付结果
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/edit/index',
    pages: [
      'calendar/index',
      'cutting/index',
      'address/index',
      'address/batch/index',
      'address/edit/index',
      'address/import/index',
      'address/confirm/index',
      'address/orderEdit/index',
      'address/addrCode/index',
      'realname/index',
      // 实名拍照
      'realname/scan/index',
      // 实名列表
      'realnameList/index',
      'city/index',
      'order/integral/index',
      'order/integral/detail/index',
      'order/integral/record/index',
      'order/shop/index',
      'order/pay/index',
      'order/result/index',
      'order/relation/index',
      'order/edit/goods/index',
      'order/edit/service/index',
      'order/share/index',
      'order/print-scan/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/index',
    pages: [
      'order/courier/message/index',
      'order/select/index',
      'order/station/index',
      'order/detail/index',
      'order/detail-cabinet/index',
      'order/detail-delivery/index',
      'order/voucher/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: [
      'help/index',
      'help/relation/index',
      'user/relation/index',
      'user/qrcode/index',
      'order/history/index',
      'ws/query/index',
      'user/fyxc/index',
      // 停发区查询
      'closedArea/index',
      'closedArea/result/index',
      'closedArea/selectBrand/index',
    ],
  },
  {
    root: 'pages',
    pages: ['out/choose-file/index', 'out/company-qrcode/index'],
  },
];
if (process.env.PLATFORM_ENV === 'alipay') {
  // 支付宝去除分包
  // config.subPackages.map(item => {
  //   config.pages.push(...item.pages.map(path => `${item.root}/${path}`));
  // });
  // config.subPackages = [];
  // 我的钱包页
  config.pages.push(
    ...[
      'pages/user/wallet/index',
      'pages/user/wallet/record/index',
      'pages/welfare/continuity/index',
      'pages/post-detail/index',
      'pages/retain/retain',
      'pages/webview-ghost/index',
    ],
  );

  config.tabBar.list.splice(3, 0, {
    pagePath: 'pages/welfare/continuity/index',
    iconPath: 'assets/tab/welfare.png',
    selectedIconPath: 'assets/tab/welfare-active.png',
    text: '福利',
  });

  config.plugins = {
    xlightPlugin: {
      version: '*',
      provider: '2021001192652032',
    },
    joinclub: {
      version: '*',
      provider: '2021002136602989',
    },
  };
}
module.exports = config;
