/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getDocumentById } from '@/utils/document';
import request from '@base/utils/request';
import { getSessionIDFromLoginData } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { useCallback, useEffect, useRef, useState } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import './index.scss';

const agreeMaps = {
  serviceAgreement: getDocumentById(0),
  yjvipAgreement: getDocumentById('yjvip'),
  bindMobileAgreement: getDocumentById(1),
  mzAgreement: getDocumentById('10'),
};

const Index = (props) => {
  const { agreeType, actionRef, loginData, style } = props;
  const [viewUrl, setViewUrl] = useState('');
  const agree_slefRef = useRef({
    mark: '',
    agreeType: '',
  });
  // 阻止冒泡
  const onCatch = (e) => {
    e.stopPropagation();
  };
  const signAgreement = () => {
    if (process.env.MODE_ENV === 'wkd') {
      console.log('微快递暂未支持协议签订');
    } else {
      request({
        toastSuccess: false,
        toastLoading: false,
        toastError: false,
        directTriggerThen: true,
        url: '/api/weixin/mini/user/Agreement/signAgreement',
        data: {
          mark: agree_slefRef.current.mark,
          agreementType: agree_slefRef.current.agreeType,
        },
      });
    }
  };

  const handleShowAgreement = useCallback(() => {
    Taro.navigator({
      url: viewUrl,
      target: 'webview',
      force: true,
    });
  }, [agreeType, viewUrl]);

  useEffect(() => {
    if (!actionRef || !Object.keys(actionRef).includes('current')) return;
    actionRef.current = { signAgreement };
  }, [actionRef]);

  useEffect(() => {
    if (process.env.MODE_ENV === 'wkd') {
      const { url = '' } = agreeMaps[agreeType] || {};
      setViewUrl(url);
      // console.log("微快递暂未支持协议签订");
    } else {
      const { logined } = loginData || {};
      if (!logined) return;
      agree_slefRef.current.agreeType = agreeType;
      request({
        toastSuccess: false,
        toastLoading: false,
        toastError: false,
        mastHasMobile: false,
        data: {
          agreementType: agreeType,
          rendering: 1,
        },
        url: '/api/weixin/mini/user/Agreement/getAgreement',
      }).then((res) => {
        const { code, data: { viewUrl, mark } = {} } = res;
        if (code == 0) {
          setViewUrl(viewUrl);
          agree_slefRef.current.mark = mark;
        }
      });
    }
  }, [agreeType, getSessionIDFromLoginData(loginData, false)]);

  return (
    <View onClick={onCatch} hoverStopPropagation className='kb-agreement'>
      <AtButton className='kb-button__link' size='small' onClick={handleShowAgreement}>
        <View style={style}>《{agreeMaps[agreeType].name}》</View>
      </AtButton>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  agreeType: '',
};

export default connect(({ global }) => ({
  loginData: global.loginData,
}))(Index);
