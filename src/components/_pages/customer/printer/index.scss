/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.bindPrinter{
    background: $color-bg;
    margin-top: $spacing-v-md;
    padding: 0 $spacing-h-md;
    &__title{
        display: flex;
        justify-content: space-between;
        padding: $spacing-h-md 0;
        &__name{
            display: flex;
            color: $color-black-1;
            font-size: $font-size-lg;
            align-items: center;
            &__agent{
                color: $color-brand;
            }
        }
    }
    &__bindTab{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: $spacing-h-md 0;
        border-top: $width-base solid $color-border-base;
        &__titleContent{
            color: $color-grey-2;
            font-size: $font-size-sm;
        }
    }
}
