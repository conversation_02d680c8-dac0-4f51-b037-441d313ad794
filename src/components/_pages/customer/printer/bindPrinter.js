/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro, { useState, useEffect } from '@tarojs/taro';
import { AtButton, AtInput } from 'taro-ui';
import { scanCode } from '@/utils/scan';
import request from '@base/utils/request';
import KbModal from '@base/components/modal';

import './index.scss';

const tabList = [
  {
    title: '下载并安装快宝云打印软件',
    content: '点击【绑定】-【云打印机二维码】获取',
    buttonText: '扫码添加',
    key: 'scan',
  },
  {
    title: '下载并安装快宝云打印软件',
    content: '点击【绑定】-【访问密钥】获取',
    buttonText: '密钥添加',
    key: 'bind',
  },
];

const BindPrinter = (props) => {
  const { customerInfo, refreshCustomerInfo } = props;
  const [changeStatus, setChangeStatus] = useState(false);
  const [isOpened, setIsOpened] = useState(false);
  const [printKey, setPrintKey] = useState('');

  const { bind_agent_name, customer_agent, id: customer_id } = customerInfo || {};
  const isRaadyBind = !!bind_agent_name && !!customer_agent;
  useEffect(() => {
    setChangeStatus(!isRaadyBind);
  }, [isRaadyBind]);

  const onChangeStatus = () => setChangeStatus(!changeStatus);

  const onScan = () => {
    console.info('扫码');
    scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
    }).then(onBindAgent);
  };
  const onOpenModal = () => setIsOpened(!isOpened);

  const onConfirm = () => {
    if (!printKey) {
      Taro.showToast({
        title: '输入密钥为空',
        icon: 'none',
      });
      return;
    }
    setIsOpened(false);
    onBindAgent(printKey, 'input');
    setPrintKey('');
  };

  const onBindAgent = (value, type = 'scan') => {
    request({
      url: '/g_wkd/v2/work/Customer/bindCustomerAgent',
      data: {
        customer_id,
        info: { key: value, scene_id: type == 'scan' ? 'scanInfo' : '' },
      },
      toastError: false,
      onThen: ({ code, msg }) => {
        if (code == 0) {
          setChangeStatus(!changeStatus);
          refreshCustomerInfo();
        }
        if (msg) {
          Taro.showToast({
            title: msg,
            icon: 'none',
          });
        }
      },
    });
  };

  return (
    <View className='bindPrinter'>
      {isRaadyBind && !changeStatus ? (
        <View className='bindPrinter__title'>
          <View className='bindPrinter__title__name'>
            <View>云打印机</View>
            <View className='bindPrinter__title__name__agent'>【{bind_agent_name}】</View>
          </View>
          <AtButton type='secondary' size='small' onClick={onChangeStatus}>
            更换
          </AtButton>
        </View>
      ) : (
        <View>
          <View className='bindPrinter__title'>
            <View className='bindPrinter__title__name'>
              <View>{isRaadyBind ? '更换' : '绑定'}打印机</View>
            </View>
            {isRaadyBind && (
              <AtButton type='secondary' size='small' onClick={onChangeStatus}>
                取消更换
              </AtButton>
            )}
          </View>
          {tabList.map((item) => (
            <View key={item.key} className='bindPrinter__bindTab'>
              <View className='bindPrinter__bindTab__titlecard'>
                <View className='bindPrinter__bindTab__titlecard__title'>{item.title}</View>
                <View className='bindPrinter__bindTab__titleContent'>{item.content}</View>
              </View>
              <AtButton
                type='secondary'
                size='small'
                onClick={item.key === 'scan' ? onScan : onOpenModal}
              >
                {item.buttonText}
              </AtButton>
            </View>
          ))}
        </View>
      )}
      <KbModal
        title='请输入密钥'
        isOpened={isOpened}
        onClose={() => setIsOpened(false)}
        onCancel={() => setIsOpened(false)}
        onConfirm={onConfirm}
      >
        {isOpened && (
          <AtInput
            placeholderClass='placeholder'
            cursor={-1}
            className='kb-input__circle'
            border={false}
            value={printKey}
            focus
            cursorSpacing={100}
            placeholder='请输入密钥'
            onChange={(value) => setPrintKey(value)}
          />
        )}
      </KbModal>
    </View>
  );
};

export default BindPrinter;
