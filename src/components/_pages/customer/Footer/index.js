/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { AtButton } from "taro-ui";
import isArray from "lodash/isArray";

/**
 * 只提供基础按钮配置，功能型按钮没有配置
 *  text, type, cb, openType
 * 默认是返回按钮
 */

const Footer = props => {
  const { button } = props;

  const buttonList = isArray(button)
    ? button
    : [
        {
          text: "返回"
        }
      ];

  const onNavigator = () => {
    Taro.navigateBack();
  };

  return (
    <View className="at-row at-row__align--center at-row__justify--between">
      {buttonList.map(item => (
        <View className="kb-spacing-md at-col" key={item.text}>
          <AtButton
            circle
            type={item.type || "primary"}
            onClick={item.cb || onNavigator}
            openType={item.openType || ""}
          >
            {item.text}
          </AtButton>
        </View>
      ))}
    </View>
  );
};

Footer.options = {
  addGlobalClass: true
};

export default Footer;
