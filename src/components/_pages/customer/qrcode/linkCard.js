/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import { View } from '@tarojs/components';
import request from '@base/utils/request';
import { setClipboardData } from '@/utils/qy';
import { AtButton } from 'taro-ui';

import './index.scss';

const Index = (props) => {
  const { customer_id } = props;
  const [pageInfo, setPageInfo] = useState({});
  useEffect(() => {
    if (customer_id) {
      request({
        url: '/g_wkd/v2/work/Customer/customerMiniUrlInfo',
        data: { customer_id },
        onThen: ({ code, data }) => {
          code == 0 && setPageInfo(data);
        },
      });
    }
  }, [customer_id]);

  return (
    <View className='qr_linkCard'>
      <View className='qr_linkCard__title'>将专属寄件链接插入公众号/小程序</View>
      <View className='qr_linkCard__buttonList'>
        <View className='qr_linkCard__buttonList__desc'>
          小程序路径：/pages/index/index?source...
        </View>
        {!!pageInfo.page && (
          <AtButton type='secondary' size='small' onClick={() => setClipboardData(pageInfo.page)}>
            复制
          </AtButton>
        )}
      </View>

      <View className='qr_linkCard__buttonList'>
        <View className='qr_linkCard__buttonList__desc'>小程序AppId：{pageInfo.appid}</View>
        {!!pageInfo.appid && (
          <AtButton type='secondary' size='small' onClick={() => setClipboardData(pageInfo.appid)}>
            复制
          </AtButton>
        )}
      </View>
    </View>
  );
};

export default Index;
