/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.qr_linkCard {
  margin-top: $spacing-v-md;
  background-color: $color-bg;
  padding: 0 $spacing-v-md;

  &__title {
    height: $spacing-v-md * 5;
    display: flex;
    justify-content: start;
    align-items: center;
  }

  &__buttonList {
    height: $spacing-v-md * 5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: $width-base solid $color-grey-4;

    &__desc {
      color: $color-grey-2;
      font-size: $font-size-base;
    }

  }
}

.qrcode_info {
  margin-top: $spacing-v-md;
  background-color: $color-bg;
  padding: $spacing-v-md*2 $spacing-v-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &__title {
    font-size: $font-size-xl;
    color: $color-grey-1;
  }

  &__image {
    width: 40%;
    padding: $spacing-v-md*2 0;
  }

  &__buttonList {
    width: 60%;
    display: flex;
    justify-content: space-between;
  }
}
