/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import request from '@base/utils/request';
import { AtButton } from 'taro-ui';

import './index.scss';

const Index = (props) => {
  const { customerInfo } = props;
  const { id: customer_id, customer, qrcode_url } = customerInfo || {};

  const printCustomerQrcode = () =>
    request({
      url: '/g_wkd/v2/work/Customer/printCustomerQrcode',
      data: { customer_id },
      onThen: ({ code }) => {
        code == 0 &&
          Taro.showToast({
            title: '打印成功',
            duration: 1000,
          });
      },
    });

  const onSaveImage = () => {
    Taro.getImageInfo({
      src: qrcode_url,
      success: function (res) {
        Taro.getSetting({
          success(ress) {
            if (!ress.authSetting['scope.writePhotosAlbum']) {
              Taro.authorize({
                scope: 'scope.writePhotosAlbum',
                success() {
                  Taro.saveImageToPhotosAlbum({
                    filePath: res.path,
                    success: () => {
                      Taro.showToast({
                        title: '保存成功',
                        duration: 1000,
                      });
                    },
                  });
                },
              });
            } else {
              Taro.saveImageToPhotosAlbum({
                filePath: res.path,
                success: () => {
                  Taro.showToast({
                    title: '保存成功',
                    duration: 1000,
                  });
                },
              });
            }
          },
        });
      },
    });
  };

  const onShare = () =>
    request({
      url: '/g_wkd/v2/work/Customer/getShareOrSendImage',
      data: {
        type: 'send_order',
        customer_id,
      },
      onThen: (res) => {
        if (res.code == 0) {
          Taro.previewImage({
            current: res.data,
            urls: [res.data],
          });
        }
      },
    });
  return (
    <View className='qrcode_info'>
      <View className='qrcode_info__title'>{customer} · 寄件专用</View>
      <Image className='qrcode_info__image' src={qrcode_url} mode='widthFix' />
      <View className='qrcode_info__buttonList'>
        <AtButton type='primary' size='small' onClick={printCustomerQrcode}>
          打印
        </AtButton>
        <AtButton type='primary' size='small' onClick={onSaveImage}>
          保存
        </AtButton>
        <AtButton type='primary' size='small' onClick={onShare}>
          分享
        </AtButton>
      </View>
    </View>
  );
};

export default Index;
