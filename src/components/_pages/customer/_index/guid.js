/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from "@tarojs/components";
import Taro, { useEffect, useState, useMemo } from "@tarojs/taro";
import { AtIcon } from "taro-ui";

import "./index.scss";

const _tabList = [
  {
    title: "企业寄件码",
    index: "qrcode",
    icon: "margin_qrcode",
    path: "customer/qrcode"
  },
  {
    title: "成员管理",
    index: "member",
    icon: "xiaoyoutongtubiao-441",
    path: "customer/member"
  },
  {
    title: "企业收发薄",
    index: "address",
    icon: "xiaoyoutongtubiao-45",
    path: "address",
    params: {
      org: "company"
    }
  },
  {
    title: "短信通知",
    index: "message",
    icon: "xiaoyoutongtubiao-46",
    path: "customer/message"
  },
  {
    title: "自助打印",
    index: "print",
    icon: "xiaoyoutongtubiao-48",
    path: "customer/printer"
  },
  {
    title: "数据对账",
    index: "bill",
    icon: "xiaoyoutongtubiao-47",
    path: "customer/account"
  }
];

const Guid = props => {
  const { customerInfo, applyBadgeStatus } = props;
  const {
    customer_id,
    isAdmin,
    customer_type,
    is_print,
    allow_export,
    customer_agent
  } = customerInfo || {};
  const [tabList, setTabList] = useState([]);

  useEffect(() => {
    if (isAdmin) {
      setTabList([
        ..._tabList.filter(item => {
          switch (item.index) {
            case "print":
              return customer_type !== "agent_customer" && is_print == 1;
            case "bill":
              return allow_export == "1";
            default:
              return true;
          }
        })
      ]);
    } else {
      setTabList(_tabList.filter(item => item.index === "qrcode"));
    }
  }, [allow_export, customer_id, customer_type, isAdmin, is_print]);

  const printText = useMemo(() => {
    if (customer_type !== "agent_customer") {
      if (is_print == "1") {
        if (customer_agent) {
          return "";
        } else {
          return "配置打印机";
        }
      } else {
        return "暂未开启";
      }
    } else {
      return "";
    }
  }, [customer_type, is_print, customer_agent]);

  const onNavigator = (path, params = {}) => {
    if (path) {
      Taro.navigator({
        url: path,
        options: {
          customer_id,
          ...params
        }
      });
    } else {
      Taro.showToast({ title: "暂未配置", icon: "none" });
    }
  };

  const onToWebview = () => {
    Taro.navigator({
      url: `webview?src=${encodeURIComponent(
        "https://upload.kuaidihelp.com/admin/floorPageFile/1586230677.html"
      )}`
    });
  };

  return (
    <View className='customer_index_guid'>
      {tabList.map(item => (
        <View
          key={item.index}
          className='customer_index_guid__tab'
          onClick={onNavigator.bind(null, item.path, item.params)}
        >
          <View
            className={
              applyBadgeStatus && item.index === "member"
                ? "customer_index_guid__tab__badge"
                : ""
            }
          >
            <AtIcon
              prefixClass='kb-icon-xyt'
              value={item.icon || ""}
              size='28'
            />
          </View>

          <View className='customer_index_guid__tab__desc'>
            {item.title || ""}
          </View>
          {!!printText && item.index === "print" && (
            <View className='customer_index_guid__tab__printText'>
              {printText}
            </View>
          )}
        </View>
      ))}
      {/* <View className='customer_index_guid__ad' onClick={onToWebview}>
        <View>点此了解Wps/Excel表格内下单打单</View>
        <AtIcon
          prefixClass='kb-icon-xyt'
          value='arrow'
          size='10'
          color='#ffa500'
        />
      </View> */}
    </View>
  );
};

Guid.options = {
  addGlobalClass: true
};

export default Guid;
