/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import KbButton from '@base/components/button';

const Invite = (props) => {
  const { text, info, size = 'small' } = props;
  return (
    <KbButton size={size} circle type='primary' openType='share' page='customer.invite' info={info}>
      {text}
    </KbButton>
  );
};
export default Invite;
