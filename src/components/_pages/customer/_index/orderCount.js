/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text } from '@tarojs/components';
import Taro, { useEffect, useState, useCallback } from '@tarojs/taro';
import request from '@base/utils/request';
import { AtButton } from 'taro-ui';

import './index.scss';

const mockData = {
  customer_id: 141972,
  customer: '大客户名称',
  courier_mobile: '13512341234',
  courier: '王现勇',
};

const elemList = [
  { title: '本月', index: 'this_month', num: '0' },
  { title: '上月', index: 'yester_month', num: '0' },
  { title: '昨日', index: 'yester_day', num: '0' },
  { title: '今日', index: 'this_day', num: '0' },
];

const OrderCount = (props) => {
  const { customerInfo: { id: customer_id } = {} } = props;
  const [orderInfo, setOrderInfo] = useState(elemList);

  const getOrderCount = useCallback(() => {
    request({
      url: '/g_wkd/v2/work/Order/getCustomerOrderInfo',
      toastLoading: false,
      data: { customer_id },
      nonceKey: 'customer_id',
      onThen: ({ code, data }) => {
        if (code == 0) {
          setOrderInfo(
            elemList.map((item) => ({
              ...item,
              num: data[item.index],
            })),
          );
        }
      },
    });
  }, [customer_id]);
  useEffect(() => {
    if (customer_id) {
      getOrderCount();
    }
  }, [customer_id, getOrderCount]);

  const onNavigator = () => {
    Taro.navigator({
      url: 'order',
      options: {
        customer_id,
      },
    });
  };

  return (
    <View className='customer_index_orderCount'>
      <View className='customer_index_orderCount__descript'>
        {orderInfo.map((item) => (
          <View key={item.index}>
            <Text className='customer_index_orderCount__descript__title'>{item.title}</Text>
            <Text className='customer_index_orderCount__descript__num'>{item.num}</Text>
          </View>
        ))}
      </View>
      <AtButton type='secondary' size='small' onClick={onNavigator}>
        订单管理
      </AtButton>
    </View>
  );
};

OrderCount.defaultProps = {
  ...mockData,
};

export default OrderCount;
