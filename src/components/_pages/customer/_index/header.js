/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text } from '@tarojs/components';
import Taro, { useEffect, useState, useCallback } from '@tarojs/taro';
import request from '@base/utils/request';

import './index.scss';

const mockData = {
  customer_id: 141972,
  customer: '大客户名称',
  courier_mobile: '13512341234',
  courier: '王现勇',
};

const Header = (props) => {
  const { customerInfo, children } = props;
  const { id: customer_id, customer, courier_name, courier_mobile } = customerInfo || {};
  const [userCount, setUserCount] = useState(0);

  const getCustomerUserCount = useCallback(() => {
    request({
      url: '/g_wkd/v2/work/CustomerUser/customerUserCount',
      toastLoading: false,
      nonceKey: 'customer_id',
      data: { customer_id },
      onThen: ({ code, data }) => {
        code == 0 && setUserCount(data || 0);
      },
    });
  }, [customer_id]);
  useEffect(() => {
    if (customer_id) {
      getCustomerUserCount();
    }
  }, [customer_id, getCustomerUserCount]);

  return (
    <View className='customer_index_header'>
      <View>
        <View>
          <Text className='customer_index_header__title'>{customer}</Text>
          <Text className='customer_index_header__descript'>成员({userCount})</Text>
        </View>
        <View className='customer_index_header__courierInfo'>
          揽件：{courier_name} {courier_mobile}
        </View>
      </View>
      {children}
    </View>
  );
};

Header.defaultProps = {
  ...mockData,
};

export default Header;
