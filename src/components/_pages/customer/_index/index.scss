/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.customer_index_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $color-bg;
  padding: $spacing-v-md $spacing-h-md;
  margin-top: $spacing-v-md;

  &__title {
    font-size: $font-size-lg;
    font-weight: bold;
    margin-right: $spacing-v-md;
  }

  &__descript {
    font-size: $font-size-base;
    color: $color-grey-2;

  }

  &__courierInfo {
    font-size: $font-size-base;
    color: $color-grey-2;
    margin-top: $spacing-v-sm;
  }
}

.customer_index_orderCount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $color-bg;
  padding: $spacing-v-md $spacing-h-md;
  margin-top: $spacing-v-md;

  &__descript {
    display: flex;
    justify-content: start;
    align-items: center;
    font-size: $font-size-base;

    &__title {
      color: $color-grey-2;
    }

    &__num {
      color: $color-black-1;
      margin-left: $spacing-v-sm;
      margin-right: $spacing-v-md;
      font-weight: bold;
    }
  }
}

.customer_index_guid {
  display: flex;
  justify-content: start;
  align-items: center;
  flex-wrap: wrap;
  background-color: $color-bg;
  padding-top: $spacing-h-md *3;
  margin-top: $spacing-v-md;
  position: relative;

  &__tab {
    width: 33%;
    padding-bottom: $spacing-h-md *3;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;

    &__desc {
      color: $color-grey-1;
      font-size: $font-size-base;
      margin-top: $spacing-v-sm;
    }

    &__printText {
      color: $color-grey-2;
      font-size: $font-size-sm;
      position: absolute;
      left: 50%;
      top: 80%;
      transform: translate(-50%, -50%);
    }

    &__badge {
      position: relative;

      &::after {
        content: "";
        width: $spacing-h-md*0.8;
        height: $spacing-h-md*0.8;
        background-color: #f40;
        position: absolute;
        right: -30%;
        top: 0;
        border-radius: 50%;
      }
    }
  }

  &__ad {
    position: absolute;
    left: 0;
    bottom: 0;
    transform: translate(0, 150%);
    display: flex;
    justify-content: center;
    background-color: $color-orange-lightest;
    width: 100%;
    padding: $spacing-v-sm 0;
    color: $color-orange-lighter;
    font-size: $font-size-base;
  }
}
