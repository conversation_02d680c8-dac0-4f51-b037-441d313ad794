/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 公共底部：
 * 1、标记版权信息
 * 2、兼容iphoneX等全屏手机底部样式
 * 3、承载页面需要引入的公共组件：登录组件、toast提示组件
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbLogin from '@base/components/login';
import KbToast from '@base/components/toast';
import KbModal from '@base/components/modal';
import KbActionSheet from '@base/components/action-sheet';
import KbClipboard from '@/components/_pages/clipboard';
import './index.scss';

const Index = (props) => {
  const { toast, modal, actionSheet, autoTriggerLoginModal } = props;
  return (
    <Fragment>
      <View className='kb-page__footer-view'>{props.children}</View>
      <KbActionSheet {...actionSheet} />
      <KbModal {...modal} />
      <KbToast {...toast} />
      <KbClipboard />
      {/* 登录信息获取与更新 */}
      <KbLogin autoTriggerLoginModal={autoTriggerLoginModal} />
    </Fragment>
  );
};

export default Index;
