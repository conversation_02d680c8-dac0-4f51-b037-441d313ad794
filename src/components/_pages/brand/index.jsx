/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image } from '@tarojs/components';

const specification = {
  mini: '25rpx',
  sm: '50rpx',
  md: '80rpx',
  lg: '100rpx',
};
const KbBrand = (props) => {
  const { size, brand, circle, className } = props;
  const style = {
    width: specification[size],
    height: specification[size],
  };
  return (
    <Image
      className={className}
      circle={circle}
      defaultSource='https://cdn-img.kuaidihelp.com/brand_logo/icon_other.png'
      src={`https://cdn-img.kuaidihelp.com/brand_logo/icon_${
        brand ? brand : 'other'
      }.png?v=20230314`}
      style={style}
    />
  );
};
KbBrand.defaultProps = {
  size: 'sm',
  brand: '',
  circle: true,
};
KbBrand.options = {
  addGlobalClass: true,
};
export default KbBrand;
