/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getStorage, setStorage } from '@base/utils/utils';

export const TCJS_STORAGE_KEY = 'TCJS_STORAGE_KEY';

export const setTCJSRelationStorage = (relationInfoData) => {
  setStorage({
    key: TCJS_STORAGE_KEY,
    data: relationInfoData,
  });
};

export function getTCJSRelationStorage() {
  return new Promise((resolve) => {
    getStorage({
      key: TCJS_STORAGE_KEY,
    })
      .then((res) => {
        const { data = null } = res.data || {};
        resolve(data);
      })
      .catch(() => resolve(null));
  });
}
