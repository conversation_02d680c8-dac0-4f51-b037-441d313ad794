/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import Taro from '@tarojs/taro';

const GLOBAL_KEY_GOODS_LIST = 'GLOBAL_KEY_GOODS_LIST';

export const requestGoodsList = () => {
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/rushOrder/Order/goodsType',
      toastLoading: false,
      onThen: ({ data }) => {
        const list = data && data.length > 0 ? data : [];
        Taro.kbSetGlobalData(GLOBAL_KEY_GOODS_LIST, list);
        resolve(list);
      },
    });
  });
};

export const getGoodsInfo = (goods_name) => {
  const list = Taro.kbGetGlobalData(GLOBAL_KEY_GOODS_LIST) || [];
  return list.find((item) => item.title == goods_name) || {};
};
