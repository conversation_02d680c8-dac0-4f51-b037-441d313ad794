/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getGoodsInfo } from '@/components/_pages/delivery/_utils/goods';
import rules from '@base/utils/rules';
import { extractData } from '@base/utils/utils';

// 地址表单相关
export const addressKeys = [
  'name',
  'mobile',
  'province',
  'city',
  'district',
  'address',
  'house_num',
  'longitude',
  'latitude',
];

export const addressKeysMap = {
  all: '地址信息',
  whatever: '地址信息有误',
  name: '姓名',
  mobile: '联系电话',
  province: '省份',
  city: '城市',
  district: '地区',
  address: '详细地址',
  house_num: '门牌号',
  longitude: '经纬度',
  latitude: '经纬度',
  service: '增值服务',
};

export const addressList = [
  {
    key: 'send',
    placeholder: ['从哪里取'],
    color: 'blue',
    tag: '寄',
  },
  {
    key: 'receive',
    placeholder: ['送到哪里'],
    color: 'orange',
    tag: '收',
  },
];

export const prefixMap = {
  send: '寄件',
  receive: '收件',
};

// 额外信息相关
export const goodsKeys = ['goods_name', 'goods_weight', 'goods_remark'];
export const remarkMaps = [
  { label: '带文件夹', key: '1' },
  { label: '带防水袋', key: '2' },
  { label: '带纸箱', key: '3' },
  { label: '要爬楼', key: '4' },
  { label: '上门请联系', key: '5' },
];

// 生成表单
const unRequiredKeys = ['house_num', ...goodsKeys];
const noStorageKey = [];
const addressKeysRules = {
  name: {
    min: 1,
  },
  address: {
    validator: (value, currentRule = {}) => {
      let tag = currentRule.tag || '详细地址',
        verifyResult = false,
        rule = /^[A-Za-z0-9]+$/;
      value = value + '';
      if (rule && rule.test(value)) {
        verifyResult = `${tag}不能填写为纯字母或纯数字格式`;
      }
      return verifyResult;
    },
  },
};

export function getFormItem({
  keys,
  data,
  form = {},
  prefix = '',
  clean = true,
  merge = {}, // 合入的表单配置项
}) {
  const formData = { ...data };
  keys.map((key) => {
    const formKey = prefix ? `${prefix}_${key}` : key;
    const commonRules = addressKeysRules[key] || {};
    const data = {
      value: formData[formKey] || '',
      clean,
      storage: !noStorageKey.includes(formKey),
      required: !unRequiredKeys.includes(key),
      ...commonRules,
      ...merge[formKey],
    };
    const addressKeyValue = addressKeysMap[key];
    if (addressKeyValue) {
      data.tag = (prefixMap[prefix] || '') + addressKeyValue;
    }
    if (key === 'mobile') {
      data.customMsg = data.tag + '格式不正确';
      data.reg = 'phone';
    }
    form[formKey] = data;
  });
  return form;
}

export const getForm = ({
  list = addressList,
  keys = addressKeys,
  action = 'order',
  data,
} = {}) => {
  const form = {};
  list.map((item) => {
    const prefix = item.key;
    getFormItem({
      keys,
      data,
      form,
      prefix,
      clean: action === 'address' ? true : prefix !== 'send',
    });
  });
  return form;
};

export const cleanOrderEditFormInfo = (keys, replaceData = {}) => {
  // 清空订单编辑表单信息
  const cleanDataMap = {};
  keys.map((item) => {
    cleanDataMap[item] = {
      clean: true,
      ...replaceData[item],
    };
  });
  return cleanDataMap;
};

// 通用类方法
export const mergeDataToFull = ({ data, prefix = '' }) => {
  const obj = {};
  for (let key in data) {
    obj[`${prefix ? prefix + '_' : ''}${key}`] = data[key];
  }
  return obj;
};

export const extractDataFromFull = ({ data, prefix = '' }) => {
  const obj = {};
  for (let key in data) {
    const [org1, org2, org3] = key.split('_');
    if (prefix == org1) {
      obj[org3 ? org2 + '_' + org3 : org2] = data[key];
    }
  }
  return obj;
};

//转换标准微快递地址信息
export function transferTcjsAddress(data = {}, action = 'all') {
  let arr = [
    ['shipper_province', 'send_province'],
    ['shipper_city', 'send_city'],
    ['shipper_district', 'send_district'],
    ['shipper_address', 'send_address'],
    ['shipper_house_num', 'send_house_num'],
    ['shipper_lng', 'send_longitude'],
    ['shipper_lat', 'send_latitude'],

    ['shipping_province', 'receive_province'],
    ['shipping_city', 'receive_city'],
    ['shipping_district', 'receive_district'],
    ['shipping_address', 'receive_address'],
    ['shipping_house_num', 'receive_house_num'],
    ['shipping_lng', 'receive_longitude'],
    ['shipping_lat', 'receive_latitude'],
  ];
  if (action == 'all') {
    arr = arr.concat([
      ['shipper_name', 'send_name'],
      ['shipper_mobile', 'send_mobile'],
      ['shipping_name', 'receive_name'],
      ['shipping_phone', 'receive_mobile'],
      ['shipping_mobile', 'receive_mobile'], //fix
    ]);
  }
  return extractData(data, arr);
}

// 整理距离数据
export const formatDistance = (data) => {
  let str = '--m';
  let distance = data * 1;
  if (distance < 10) {
    str = '<10m';
  } else {
    str = (distance / 1000).toFixed(2) + 'km';
  }
  return str;
};

// 检查联系方式组
export const checkContactGroup = (value, { rule, msg } = rules.contact) => {
  const list = `${value}`.split(/\s+|[,，;]+/g).filter((item) => !!item);
  let errorIndex = -1;
  for (let i = 0, len = list.length; i < len; i++) {
    if (!rule.test(list[i])) {
      errorIndex = i;
      break;
    }
  }
  if (errorIndex >= 0) {
    return msg;
  }
};

export const transferTcjsExtraInfoData = (extraInfo) => {
  const {
    goods_name_id,
    goods_name,
    goods_weight,
    goods_remark,
    tips_fee,
    tipsFee = tips_fee,
    reserve_time,
  } = extraInfo || {};
  const { id: goods_id } = getGoodsInfo(goods_name);
  return {
    cargo_type: goods_name,
    cargo_type_id: goods_id || goods_name_id,
    cargo_weight: goods_weight,
    info: goods_remark,
    reserve_time: reserve_time,
    cargo_price: 50,
    tips: tipsFee,
  };
};
