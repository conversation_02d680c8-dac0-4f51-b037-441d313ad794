/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbListBox from '@/components/_pages/order/listbox';
import KbModal from '@base/components/modal';
import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro, { Fragment, useState } from '@tarojs/taro';
import isFunction from 'lodash/isFunction';
import { AtFloatLayout, AtInput } from 'taro-ui';
import './index.scss';

// 小费列表配置
const rewards = [
  { value: '0', label: '不加小费' },
  { value: '1', label: '1元' },
  { value: '2', label: '2元' },
  { value: '5', label: '5元' },
  { value: '10', label: '10元' },
  { value: '15', label: '15元' },
  { value: '20', label: '20元' },
  { value: '30', label: '30元' },
  { value: '50', label: '50元' },
  { value: '-1', label: '其他金额' },
];

function Index(props) {
  const { actionRef, current = '0', onChange } = props;

  const [isOpenedOther, setIsOpenOther] = useState(false);
  const [isOpened, setIsOpen] = useState(false);
  const [otherValue, setOtherValue] = useState('');
  const [selectted, setSelectted] = useState();

  if (actionRef) {
    actionRef.current = {
      open: () => {
        handleAction('open');
      },
    };
  }

  const getRewardsItem = (label) => {
    return rewards.find((item) => item.label === label) || {};
  };

  // 处理各种操作
  const handleAction = (key, item) => {
    switch (key) {
      case 'open':
        const { label: selectLabel } = rewards.find((item) => item.value == (current || '0')) || {};
        if (selectLabel) {
          setSelectted(selectLabel);
        }
        switchFloatLayoutStatus(true);
        break;
      case 'select':
        const { label } = item;
        if (selectted === label) return;
        if (label === '其他金额') {
          switchFloatLayoutStatus(false);
          setIsOpenOther(true);
        } else {
          setSelectted(label);
        }
        break;
      case 'cancel':
        onClose();
        break;
      case 'confirm':
        setSelectted(item);
        switchFloatLayoutStatus(false, () => {
          const { value: tipsFee } = getRewardsItem(item);
          onChange({
            tipsFee,
          });
        });
        break;
      case 'other_input':
        setOtherValue(item);
        break;
      case 'other_confirm':
        if (otherValue > 0) {
          setIsOpenOther(false);
          onChange({
            tipsFee: otherValue,
          });
        } else {
          Taro.kbToast({
            text: '请输入小费金额',
          });
        }
        break;
      default:
        break;
    }
  };

  // 切换弹窗状态
  const switchFloatLayoutStatus = (isOpened, then) => {
    setIsOpen(isOpened);
    isFunction(then) && then();
  };

  // 关闭弹窗
  const onClose = () => switchFloatLayoutStatus(false);

  //处理选择
  const handleSelectChange = (item) => {
    handleAction('select', item);
  };

  return (
    <View>
      <AtFloatLayout isOpened={isOpened} onClose={onClose}>
        {!!isOpened && (
          <Fragment>
            <View className='kb-float-layout__bars'>
              <View
                className='layout-bars__cancel'
                hoverClass='kb-hover'
                onClick={() => {
                  handleAction('cancel');
                }}
              >
                取消
              </View>
              <View className='layout-bars__title'>加小费</View>
              <View
                className='layout-bars__confirm'
                hoverClass='kb-hover'
                onClick={() => handleAction('confirm', selectted)}
              >
                确定
              </View>
            </View>
            <View className='kb-package'>
              <View className='kb-package-warp'>
                <KbListBox list={rewards} selectted={selectted} onChange={handleSelectChange} />
              </View>
            </View>
          </Fragment>
        )}
      </AtFloatLayout>
      <KbModal
        isOpened={isOpenedOther}
        top={false}
        title='自定义小费'
        cancelText='取消'
        onCancel={() => setIsOpenOther(false)}
        onConfirm={() => handleAction('other_confirm')}
      >
        {isOpenedOther ? (
          <View>
            <AtInput
              type='digit'
              placeholder='请输入小费金额'
              value={otherValue}
              onChange={(value) => {
                handleAction('other_input', value);
              }}
            />
          </View>
        ) : null}
      </KbModal>
    </View>
  );
}

Index.defaultProps = {
  actionRef: null,
  current: '',
  onChange: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
