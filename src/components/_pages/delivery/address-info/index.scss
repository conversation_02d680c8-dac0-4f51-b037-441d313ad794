/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$address-placeholder-height: 150px;
$address-border-radius: $border-radius-lg;

.kb-address-info {
  &-item {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 150px;
    background: $color-white;
    &-top {
      border-radius: 10px 10px 0 0;
      &::before {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 90px;
        width: 100%;
        height: 2px;
        background: #eee;
        content: '';
      }
    }
    &-bottom {
      border-radius: 0 0 10px 10px;
    }
    &-icon {
      position: relative;
      padding: 0 $spacing-h-md;

      &__text {
        position: absolute;
        top: 50%;
        left: 50%;
        color: $color-white;
        font-size: $font-size-base;
        transform: translate(-50%, -60%);
      }
    }
    &-content {
      flex: 1;
    }
    .item-bar {
      &__item {
        &--icon {
          padding: 2 * $spacing-v-md $spacing-h-md;
          line-height: 1;

          &.kb-hover {
            border-radius: 0;
          }
        }

        &--icon::before {
          display: inline-block;
          width: 0;
          height: 100%;
          vertical-align: middle;
          content: '';
        }

        &--lib::before,
        &:first-child::after {
          height: $address-placeholder-height/2.5;
          margin: 0 $spacing-h-md 0 2 * $spacing-h-md;
          border-right: $border-lightest;
          content: '';
        }

        &--lib {
          display: flex;
          align-items: center;
          padding-right: 2 * $spacing-h-md;
          padding-left: 0;

          &::before {
            margin: 0 2 * $spacing-h-md 0 0;
          }
        }
      }
    }
  }
}
