/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPicker from '@base/components/picker';
import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';
import { formatFormTimeToOrder, formatOrderTimeToForm, getTimesArr } from './utils';

class Index extends Taro.Component {
  static defaultProps = {
    value: '',
    onChange: noop,
  };

  constructor() {
    super(...arguments);
    this.state = {
      cValue: [],
      times: [],
    };
    this.pickerRef = '';
  }

  componentDidUpdate(prevProps) {
    if (this.props.value != prevProps.value) {
      this.setState({
        cValue: formatOrderTimeToForm(this.props.value),
      });
    }
  }

  // 保存picker的ref
  onRef = (ref) => {
    this.pickerRef = ref;
  };

  onShowPicker() {
    this.initTimeData().then(() => {
      this.pickerRef.show();
    });
  }

  initTimeData() {
    return new Promise((resolve) => {
      this.setState(
        {
          times: getTimesArr(),
        },
        () => {
          resolve();
        },
      );
    });
  }

  onConfirm = (e) => {
    const { result, value, obj } = e;
    console.log('result, value, obj', result, value, obj);
    const res = formatFormTimeToOrder(value);
    this.props.onChange({
      reserve_time: res,
    });
  };

  render() {
    const { times, cValue } = this.state;
    return (
      <View>
        <KbPicker
          mode='linkage'
          options={times}
          value={cValue}
          level={3}
          onConfirm={this.onConfirm}
          onRef={this.onRef}
        />
      </View>
    );
  }
}

export default Index;
