/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import dayjs from 'dayjs';

const padZero = (n) => (Number(n) > 9 ? n : '0' + n);

const getMinutes = (n1) => {
  const arr = [];
  const n = Number(n1);
  for (let i = 0; i < 60; i = i + 5) {
    if ((!Number.isNaN(n) && n < i) || Number.isNaN(n)) {
      arr.push(i);
    }
  }
  return arr;
};

const getHours = (n1) => {
  const arr = [];
  const n = Number(n1);
  for (let i = 0; i < 24; i++) {
    if ((!Number.isNaN(n) && n <= i) || Number.isNaN(n)) {
      arr.push(i);
    }
  }
  return arr;
};

const formatMinutes = (m) => {
  return m && m.length > 0
    ? m.map((item) => ({
        value: pad<PERSON>ero(item),
        label: pad<PERSON>ero(item) + '分',
        children: [],
      }))
    : [];
};

const formatTimes = (isToday) => {
  const oDate = dayjs();
  const nHour = oDate.get('hour');
  const todayHours = getHours(nHour == 23 ? 0 : nHour + 1);
  todayHours.unshift(-1);
  const todayMinutes = getMinutes(oDate.get('minute'));
  const hours = getHours();
  const minutes = getMinutes();
  const h = isToday ? todayHours : hours;
  return h && h.length > 0
    ? h.map((item, index) => {
        const m = minutes;
        if (isToday) {
          if (index == 0) {
            return {
              value: '',
              label: '立即取件',
              children: [],
            };
          }
          if (index == 1) {
            return {
              value: padZero(item),
              label: padZero(item) + '时',
              children: formatMinutes(todayMinutes),
            };
          }
        }
        return {
          value: padZero(item),
          label: padZero(item) + '时',
          children: formatMinutes(m),
        };
      })
    : [];
};

export const getTimesArr = () => {
  const oDate = dayjs();
  return [
    {
      value: oDate.format('YYYY-MM-DD'),
      label: '今天',
      children: formatTimes(true),
    },
    {
      value: oDate.add(1, 'day').format('YYYY-MM-DD'),
      label: '明天',
      children: formatTimes(false),
    },
    {
      value: oDate.add(2, 'day').format('YYYY-MM-DD'),
      label: '后天',
      children: formatTimes(false),
    },
  ];
};

// 将订单值转换为表单值
export const formatOrderTimeToForm = (v) => {
  if (!v) {
    return [dayjs().format('YYYY-MM-DD'), '', ''];
  }
  const day = dayjs(v).format('YYYY-MM-DD');
  const hour = padZero(dayjs(v).get('hour'));
  const minute = padZero(dayjs(v).get('minute'));
  const second = padZero(dayjs(v).get('second'));
  return [day, hour, minute, second];
};

// 将表单值转换为订单值
export const formatFormTimeToOrder = (v) => {
  const [v0, v1, v2, v3 = '00'] = v || [];
  let str = '';
  if (v1) {
    str = `${v0} ${v1}:${v2}:${v3}`;
  }
  return str;
};
