/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getBrandsTCJS } from '@/actions/tcjsBrand';
import KbAppointmentTime from '@/components/_pages/delivery/appointment-time';
import KbTipFee from '@/components/_pages/delivery/tip-fee';
import {
  cleanOrderEditFormInfo,
  getFormItem,
  goodsKeys,
} from '@/components/_pages/delivery/_utils';
import { requestGoodsList } from '@/components/_pages/delivery/_utils/goods';
import Form from '@base/utils/form';
import { checkIsReLogin, noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import './index.scss';

@connect(
  ({ global }) => ({
    loginData: global.loginData,
    brands: global.brands,
  }),
  {
    dispatchGet: getBrandsTCJS,
  },
)
class Index extends Component {
  static defaultProps = {
    data: {},
    address: {},
    onChange: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    this.state = {
      form: { data: {}, disabled: true },
    };
    // refs
    this.tipFeeRef = createRef();
    this.appointmentTimeRef = createRef();
  }

  componentDidMount() {
    this.props.dispatchGet();
    this.createForm(() => {
      requestGoodsList().then((data) => {
        if (data && data[0]) {
          this.formIns.update({
            goods_name: data[0].title || '',
          });
        }
      });
    });
  }

  componentWillReceiveProps(nextProps) {
    const {
      data: nextData,
      relationInfo: nextRelationInfo,
      loginData: nextLoginData,
      dynamicForms: nextDynamicForms,
    } = nextProps;
    const { data, relationInfo, loginData, dynamicForms } = this.props;
    // 处理组件内部data更新
    if (nextRelationInfo != relationInfo) {
      this.createForm();
    }
    if (nextRelationInfo != relationInfo) {
      this.formIns.update({ service: {} });
    }
    if (nextData && nextData !== data) {
      const { clean, pay_status, orderPrice, ...nextDataRest } = nextData;
      if (this.triggerClean(clean)) {
        return;
      }
      this.formIns.update(nextDataRest);
    }
    // 表单项设置更新
    if (nextDynamicForms !== dynamicForms && nextDynamicForms) {
      this.triggerWhetherFormLocked(nextDynamicForms);
      return;
    }
    // 检查是否重新登录
    checkIsReLogin(nextLoginData, loginData, () => {});
  }

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onReady) => {
    const form = getFormItem({
      keys: goodsKeys,
      merge: {
        goods_name: { required: true, clean: false },
        goods_weight: { value: 1, clean: 1 },
      },
    });
    getFormItem({
      form,
      keys: ['reserve_time', 'tipsFee'],
      merge: {
        reserve_time: {
          value: '',
          required: false,
          clean: '',
        },
        tipsFee: {
          value: '',
          required: false,
          clean: '',
        },
      },
    });
    this.formIns = new Form(
      {
        form,
        onUpdate: (data) => {
          const {
            data: { goods_weight: changeWeight },
            formEvent,
            eventType,
          } = data;
          if (eventType === 'clean') {
            // 清除操作
            this.cleanFormInfo(['serviceData']);
          }
          if (formEvent === 'blur' && changeWeight) {
            return;
          }
          this.props.onChange(data);
        },
        onReady,
      },
      this,
    );
  };
  // 触发清理表单
  triggerClean = (clean) => {
    clean && this.formIns.clean();
    return clean;
  };
  // 清除信息
  cleanFormInfo = (keys, replaceData) => {
    this.setState(cleanOrderEditFormInfo(keys, replaceData));
  };
  // 是否触发表单锁定为自定义内容
  triggerWhetherFormLocked = (dynamicForms) => {
    let updateData = {};
    if (!dynamicForms) return;
    Object.keys(dynamicForms).forEach((i) => {
      if (dynamicForms[i].value) {
        updateData[i] = dynamicForms[i].value;
      }
    });
    if (Object.keys(updateData).length) {
      this.formIns.update({ ...updateData });
      return updateData;
    }
    return false;
  };

  //处理子组件数据更新
  handleChange = (key, data) => {
    switch (key) {
      case 'tipsFee':
        let { tipsFee } = data;
        this.formIns.update({
          tipsFee,
        });
        break;
      case 'appointment-time':
        let { reserve_time } = data;
        this.formIns.update({
          reserve_time,
        });
        break;
    }
  };

  //处理页面跳转
  onJumpTo(key) {
    const { form: { data: formData } = {} } = this.state;
    switch (key) {
      case 'goods':
        Taro.navigator({
          url: 'order/delivery/goods',
          key: 'routerParamsChange',
          options: {
            data: formData,
          },
          onArrived: () => {},
        });
        break;
    }
  }

  getFormatGoods(data = {}) {
    let goodFormValue = '';
    if (data.goods_name) {
      goodFormValue += `${data.goods_name}/`;
    }
    if (data.goods_weight > 0) {
      goodFormValue += `${data.goods_weight}kg/`;
    }
    goodFormValue = goodFormValue ? goodFormValue.substring(0, goodFormValue.length - 1) : '';
    return goodFormValue;
  }

  render() {
    const { form: { data } = {} } = this.state;

    return (
      <View className='kb-extra-info'>
        <View className='kb-extra-info_yjkd__other'>
          <View className='yjkd__other at-row kb-size__base'>
            <View
              className='yjkd__other_item at-row'
              hoverClass='kb-hover'
              onClick={this.onJumpTo.bind(this, 'goods')}
            >
              <View className={`yjkd__other_val at-col ${data.goods_name ? '' : 'kb-color__grey'}`}>
                {(data && this.getFormatGoods(data)) || '物品信息'}
              </View>
              <AtIcon
                prefixClass='kb-icon'
                value='arrow'
                className='kb-color__grey kb-icon-size__sm'
              />
            </View>
            <View
              className='yjkd__other_item at-row'
              hoverClass='kb-hover'
              onClick={() => {
                this.tipFeeRef.current.open();
              }}
            >
              <View className='yjkd__other_val at-col'>
                {data.tipsFee > 0 ? `小费:${data.tipsFee}元` : '加小费'}
              </View>
              <AtIcon
                prefixClass='kb-icon'
                value='arrow'
                className='kb-color__grey kb-icon-size__sm'
              />
            </View>
            <View
              className='yjkd__other_item at-row'
              hoverClass='kb-hover'
              onClick={() => {
                this.appointmentTimeRef.current.onShowPicker();
              }}
            >
              <View className='yjkd__other_val at-col'>
                {data.reserve_time ? data.reserve_time : '立即取件'}
              </View>
              <AtIcon
                prefixClass='kb-icon'
                value='arrow'
                className='kb-color__grey kb-icon-size__sm'
              />
            </View>
          </View>
        </View>
        <KbAppointmentTime
          ref={this.appointmentTimeRef}
          value={data.reserve_time}
          onChange={(e) => this.handleChange('appointment-time', e)}
        />
        <KbTipFee
          actionRef={this.tipFeeRef}
          current={data.tipsFee}
          onChange={(e) => this.handleChange('tipsFee', e)}
        />
      </View>
    );
  }
}

export default Index;
