/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$address-placeholder-height: 150px;
$address-border-radius: $border-radius-lg;

.kb-extra-info {
  &_yjkd {
    &__weight {
      height: 90px;
      line-height: 90px;
      border-bottom: $border-lighter;

      .yjkd__weight {
        width: 360px;

        .at-input {
          margin-left: 0 !important;
          padding: 0 !important;
        }

        &_title {
          image {
            width: 20px;
            height: 20px;
            margin-left: 10px;
          }

          .kb-icon-star {
            background-image: linear-gradient(to right, $color-red, $color-yellow);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        &_opt {
          flex-shrink: 0;
          width: 40px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border: $border-lightest;
          border-radius: 50%;
        }

        &_input {
          /*  #ifdef swan  */
          width: 280px !important;
          /*  #endif  */
          height: 90px;
          padding: 10px 20px 0;
          line-height: 90px;
        }
      }
    }

    &__other {
      padding: 20px 0;

      .yjkd__other {
        height: 60px;
        line-height: 60px;
        background: $color-grey-7;

        &_item {
          position: relative;
          align-items: center;
          width: 33.33%;
          padding: 0 10px;

          &::after {
            position: absolute;
            top: 50%;
            right: 0;
            height: 30px;
            border-right: $width-base solid $color-grey-6;
            border-right-color: rgb(230, 230, 230);
            border-right-width: $width-base;
            border-right-style: solid;
            transform: translateY(-50%);
            content: '';
          }

          &:last-child::after {
            border-right: none;
          }
        }

        &_val {
          margin-right: 10px;
          overflow: hidden;
          white-space: nowrap;
          text-align: center;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
