/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getAvailableBrands } from '@/actions/tcjsBrand';
import { formatDistance, transferTcjsExtraInfoData } from '@/components/_pages/delivery/_utils';
import request from '@base/utils/request';
import isArray from 'lodash/isArray';

export function getQuotation(opt) {
  const { brands, addrData, extraInfo } = opt || {};
  return new Promise((resolve) => {
    const brandList = getAvailableBrands(brands);
    request({
      url: '/g_wkd/v2/rushOrder/Order/quotation',
      data: {
        brand: brandList.join(','),
        ...addrData,
        ...transferTcjsExtraInfoData(extraInfo),
      },
      toastLoading: false,
      toastError: true,
      onThen: ({ data }) => {
        let quotation = data;
        if (isArray(quotation) && quotation.length > 0) {
          quotation = quotation.map((item, index) => {
            const { distance } = item || {};
            index == 0 && (item.label = 'min');
            item = { ...item, formatDistance: formatDistance(distance) };
            return item;
          });
        }
        resolve({ quotation });
      },
    });
  });
}
