/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbEstimatedFeeContent from '@/components/_pages/delivery/estimated-fee/content';
import { getQuotation } from '@/components/_pages/delivery/estimated-fee/utils';
import { transferTcjsAddress } from '@/components/_pages/delivery/_utils';
import KbMoveArea from '@/components/_pages/order/move-area';
import KbLoader from '@base/components/loader';
import KbMask from '@base/components/mask';
import { debounce, isFullData, noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import { Fragment, useCallback, useEffect, useRef, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import './index.scss';

function Index(props) {
  const {
    relationInfo,
    address,
    extraInfo,
    moveAreaStatus,
    moveAreaData,
    onChange = noop,
    onChangeArea = noop,
  } = props || {};
  const { brand } = relationInfo || {};
  const { goods_name, goods_weight, reserve_time } = extraInfo || {};
  const { brands_tcjs: brands = {} } = useSelector((state) => state.global);
  const [loading, setLoading] = useState(false);
  const [quotationList, setQuotationList] = useState([]);

  const actionRefCurrent = useRef({});
  actionRefCurrent.current.getYjQuotation = ({ brand, brands, address, extraInfo }) => {
    setLoading(true);
    actionRefCurrent.current.loading = true;
    const addrData = transferTcjsAddress(address);
    getQuotation({ brands, addrData, extraInfo }).then(({ quotation }) => {
      setLoading(false);
      actionRefCurrent.current.loading = false;
      let list = quotation;
      // 接口返回报价单null时，置空价格
      if (actionRefCurrent.current.hasList && !list) {
        list = actionRefCurrent.current.list.map(({ total_fee, ...rest }) => {
          return {
            ...rest,
          };
        });
      }
      if (isArray(list)) {
        list = list.map((item) => {
          console.log('报价单', brand);
          const brandArr = brand ? brand.split(',') : [];
          if (brandArr && brandArr.length > 0) {
            if (brandArr.includes(item.brand)) {
              item.selected = '1';
            }
          } else if (item.label === 'min') {
            item.selected = '1';
          }
          return {
            ...item,
          };
        });
        triggerUpdate(list);
      }
    });
  };

  const getYjQuotationDebounce = useCallback(
    debounce(actionRefCurrent.current.getYjQuotation, 500, { trailing: true }),
    [],
  );

  useEffect(() => {
    if (isEmpty(address)) return;
    const restAddressData = { ...address };
    delete restAddressData.send_house_num;
    delete restAddressData.receive_house_num;
    if (!isFullData(restAddressData) || !goods_name) {
      triggerUpdate([]);
      return;
    }
    getYjQuotationDebounce({ brand, brands, address, extraInfo });
  }, [brands, address, goods_name, goods_weight, reserve_time]);

  const triggerChange = () => {
    onChange({ changeSource: 'list', quotationList: actionRefCurrent.current.list });
  };

  // 列表更新
  // 百度渲染延迟才能正常展示选项卡；
  useEffect(() => {
    // 此处主要是更新布局
    if (!actionRefCurrent.current.hasList) return;
    triggerChange();
  }, [quotationList]);

  useEffect(() => {
    const { hasList, count } = actionRefCurrent.current;
    if (!hasList || count > 3) return;
    const { maxHeight = 0 } = moveAreaData || {};
    if (maxHeight) return;
    // maxHeight === 0且尝试次数小于3，应再次重置高度
    triggerChange();
    actionRefCurrent.current.count++;
  }, [moveAreaData]);

  const triggerUpdate = (list = []) => {
    actionRefCurrent.current.count = 0;
    actionRefCurrent.current.hasList = list && list.length > 0;
    actionRefCurrent.current.list = list;
    setQuotationList(list);
  };

  const handleChange = (key, e) => {
    const { selected } = e || {};
    if (key === 'chooseBrand') {
      onChange({
        changeSource: 'chooseBrand',
        selected: isArray(selected) ? selected.join(',') : '',
      });
      actionRef.current.setScrollTopFn && actionRef.current.setScrollTopFn();
    }
  };

  const actionRef = useRef();
  const handleClickMask = () => {
    actionRef.current.switchStatus('min');
  };

  return (
    <Fragment>
      {actionRefCurrent.current.hasList ? (
        <Fragment>
          <KbMask show={moveAreaStatus === 'max'} zIndex='1' onClick={handleClickMask} />
          <KbMoveArea data={moveAreaData} onChange={onChangeArea} actionRef={actionRef}>
            <View className='kb-estimatedFeeList-content'>
              <KbEstimatedFeeContent
                loading={loading}
                brands={brands}
                quotationList={quotationList}
                onChange={handleChange}
              />
            </View>
          </KbMoveArea>
        </Fragment>
      ) : loading ? (
        <View className='kb-estimatedFeeList-loader'>
          <KbLoader size='small' />
        </View>
      ) : null}
    </Fragment>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
