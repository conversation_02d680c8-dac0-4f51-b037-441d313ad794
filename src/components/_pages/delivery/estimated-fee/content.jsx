/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCheckbox from '@base/components/checkbox';
import { Image, Text, View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './content.scss';

const Index = (props) => {
  const { loading, brands, quotationList = [], onChange } = props;

  const [selected, setSelected] = useState([]);

  useEffect(() => {
    if (quotationList && quotationList.length > 0) {
      const selectedArr = [];
      quotationList.map((item) => {
        if (item.selected == 1) {
          selectedArr.push(item.brand);
        }
      });
      setSelected(selectedArr);
    }
  }, [quotationList]);

  useEffect(() => {
    onChange('chooseBrand', { selected });
  }, [selected]);

  const onChooseBrand = (item) => {
    const { available, unavailable_msg } = item || {};
    if (available <= 0 && unavailable_msg) {
      Taro.kbToast({
        text: unavailable_msg,
      });
      return;
    }
    let list = [...selected];
    if (selected.includes(item.brand)) {
      const index = selected.findIndex((i) => i == item.brand);
      list.splice(index, 1);
    } else {
      list.push(item.brand);
    }
    setSelected(list);
  };

  const handleBrandDesc = (brandInfo, ev) => {
    ev.stopPropagation();
    Taro.kbModal({
      top: false,
      title: '温馨提示',
      content: brandInfo.desc,
    });
  };

  return (
    <View className='kb-estimatedFeeList-list--content'>
      {quotationList.map((item) => {
        const itemCls = classNames('kb-estimatedFeeList-list-item');
        // const itemCls = classNames('kb-estimatedFeeList-list-item', {
        //   'make-disabled': item.available <= 0,
        // });
        const brandInfo = (brands && brands[item.brand]) || {};
        return (
          <View
            class={itemCls}
            key={item.brand}
            onClick={onChooseBrand.bind(null, item)}
            hoverClass='kb-hover-opacity'
          >
            <View className='kb-estimatedFeeList-list-item__body at-row at-row__justify--between at-row__align--center'>
              <View className='kb-margin-md-r'>
                <Image
                  lazyLoad
                  mode='widthFix'
                  style={{ width: '30px', height: '30px' }}
                  src={brandInfo.logo_link}
                />
              </View>
              <View className='at-col'>
                <View className='at-row at-row__justify--between at-row__align--center'>
                  <View className=''>
                    <View
                      className='kb-color__black yj-brand-name'
                      onClick={handleBrandDesc.bind(this, brandInfo)}
                      hoverClass='kb-hover-opacity'
                      hoverStopPropagation
                    >
                      {brandInfo.name}&gt;
                    </View>
                    <View className='kb-color__grey kb-size__base'>{item.formatDistance}</View>
                  </View>
                  <View className='at-row at-row__justify--end at-row__align--center kb-freight__row'>
                    <View className='kb-size__sm'>预估：</View>
                    <View className='kb-freight'>
                      {loading ? (
                        <AtIcon
                          prefixClass='kb-icon'
                          value='loading'
                          className='kb-icon-size__base kb-color__grey'
                        />
                      ) : (
                        <Fragment>
                          <Text>{item.total_fee > 0 ? item.total_fee : '--'}</Text>
                          <Text className='kb-freight__uint'>元</Text>
                        </Fragment>
                      )}
                    </View>
                  </View>
                </View>
                {item.tips ? (
                  <View className='kb-estimatedFeeList-list__tips'>{item.tips}</View>
                ) : null}
              </View>
              <View className='kb-spacing-md-l pt-b-20'>
                <KbCheckbox
                  checked={selected.includes(item.brand)}
                  onChange={onChooseBrand.bind(null, item)}
                />
              </View>
            </View>
            {item.label == 'min' && <View className='kb-tag-badge__sh'>最实惠</View>}
          </View>
        );
      })}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
