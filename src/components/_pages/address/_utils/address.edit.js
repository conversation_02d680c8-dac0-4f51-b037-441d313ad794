/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { apiURLs, checkDataComplete } from '@/components/_pages/address/_utils';
import {
  addressKeys,
  formatFormDataByPrefix,
  formatRequestEdit,
} from '@/components/_pages/address/_utils/index';
import { formatAddress, getForm, getFormItem, goodsKeys } from '@/components/_pages/order/_utils';
import { editOrder } from '@/components/_pages/order/_utils/order.action';
import Form from '@base/utils/form';
import { refreshControl, REFRESH_KEY_ADDRESS } from '@/utils/refresh-control';
import request from '@base/utils/request';
import { extractData, noop } from '@base/utils/utils';
import Taro from '@tarojs/taro';

export const disturbAddress = (data, org, action = 'add') => {
  let res = {};
  data &&
    Object.keys(data).forEach((key) => {
      if (addressKeys.find((i) => key.includes(i))) {
        if (action === 'add' && !key.includes(`${org}_`)) {
          res[`${org}_${key}`] = data[key];
        } else {
          res[key.includes(org) ? key.replace(`${org}_`, '') : key] = data[key];
        }
      } else {
        res[key] = data[key];
      }
    });

  return res;
};
export function createFormConfig({ onReady, params, onUpdate = () => {} }, page) {
  const { org, type = 'company', action, source, fixFor = 'edit', pageType = '' } = params;
  const { key, toast: toastSuccess } = page.actionData();
  const form = getForm({ list: [{ key: org }], action: 'address' });
  const { isBatch } = page.state;
  const getConfig = () => {
    if (source != 'list') {
      const extraInfoKeys = [...goodsKeys, 'service'];
      return {
        form: getFormItem({
          form,
          keys: isBatch ? extraInfoKeys : [],
          merge: { service: { required: false, value: {} } },
        }),
        onSubmit: (req) => {
          const extraInfo = extractData(req, extraInfoKeys);
          const { error } = checkDataComplete(extraInfo, goodsKeys);
          if ((!isBatch && req.send_save == 1) || req.receive_save == 1) {
            const {
              district: county,
              address: detail,
              ...rest
            } = disturbAddress(req, org, 'clear');
            let data = { ...rest, county, detail };
            request({
              url: apiURLs[type]['save'],
              data: {
                ...data,
                type: org,
              },
              formatRequest: (req) => formatRequestEdit(req, { params, page }),
            });
          }
          const data = isBatch
            ? {
                current: {
                  ...formatAddress(req, org).data,
                  extraInfo: error === 'all' ? null : extraInfo,
                },
              }
            : {
                data: { ...formatAddress(req, org).data },
              };
          const address = formatFormDataByPrefix(page.state.form.data, req);
          if (fixFor != 'edit') {
            Taro.navigator({
              post: {
                type: 'addressSelect',
                data: { address },
              },
            });
          } else if (action == 'fix') {
            editOrder({
              address,
            });
          } else {
            Taro.navigator({
              post: {
                type: isBatch ? 'addressSelectEdit' : 'addressEdit',
                data,
              },
            });
          }
        },
        onReady: onReady || noop,
        onUpdate,
      };
    }
    return {
      form,
      api: {
        url: apiURLs[type][key],
        quickTriggerThen: true,
        toastError: true,
        toastSuccess,
        formatRequest: (req) => formatRequestEdit(req, { params, page }),
        onThen: (res) => {
          const { code } = res;
          if (code == 0) {
            const {
              form: { data },
            } = page.state;
            refreshControl(REFRESH_KEY_ADDRESS);
            Taro.navigator(
              action === 'select'
                ? {
                    post: {
                      index: pageType === 'addrCode' ? -2 : -3,
                      type: 'addressSelect',
                      data: {
                        data,
                        source: 'edit',
                      },
                    },
                  }
                : null,
            );
          }
        },
      },
    };
  };

  return new Form(getConfig());
}

export const getPureSaveAddress = (address, list, org = 'receive') => {
  const formatItem = (data) =>
    extractData(data, [
      ['detail', `${org}_address`],
      ['city', `${org}_city`],
      ['county', `${org}_district`],
      ['mobile', `${org}_mobile`],
      ['name', `${org}_name`],
      ['province', `${org}_province`],
      ['save', `${org}_save`],
      ['company', `${org}_company`],
    ]);
  if (list) {
    return list.map((item) => {
      return extractData(item, [
        ['detail', `address`],
        'city',
        ['county', `district`],
        'mobile',
        'name',
        'province',
        'save',
        'company',
      ]);
    });
  }
  return [formatItem(address)];
};
export const saveAddress = (params) => {
  return new Promise((resolve) => {
    request({
      url: apiURLs[process.env.MODE_ENV != 'wkd' ? 'person' : 'company']['save'],
      data: { ...params },
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};
