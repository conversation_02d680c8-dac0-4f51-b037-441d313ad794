/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { addressList, getFormItem } from '@/components/_pages/order/_utils';
import apis from '@/utils/apis';
import check from '@base/utils/check';
import request from '@base/utils/request';
import rules from '@base/utils/rules';
import {
  extractData,
  filterSpecialCharacters,
  getStorage,
  getStorageSync,
  mergeBySpace,
  removeStorage,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isEqual from 'lodash/isEqual';
import isPlainObject from 'lodash/isPlainObject';
import isString from 'lodash/isString';

export const apiURLs = {
  company: {
    getList: '/g_wkd/v2/work/CustomerAddress/getListByCustomerId',
    setDefault: '/g_wkd/v2/work/CustomerAddress/topOrDefaultAddress',
    setTop: '/g_wkd/v2/work/CustomerAddress/topOrDefaultAddress',
    update: '/g_wkd/v2/work/CustomerAddress/upCustomerAddress',
    save: '/g_wkd/v2/work/CustomerAddress/addCustomerAddress',
    remove: '/g_wkd/v2/work/CustomerAddress/delCustomerAddress',
  },
  person: {
    getList: '/api/weixin/mini/address/record/history',
    setDefault: '/api/weixin/mini/address/record/setDefault',
    setTop:
      process.env.MODE_ENV == 'wkd'
        ? '/g_wkd/v2/UserAddress/setTop'
        : '/api/weixin/mini/address/record/setTop',
    update:
      process.env.MODE_ENV == 'wkd'
        ? '/v1/user_address/update'
        : '/api/weixin/mini/address/record/update',
    save:
      process.env.MODE_ENV == 'wkd'
        ? '/v1/user_address/add'
        : '/api/weixin/mini/address/record/create',
    remove: '/api/weixin/mini/address/record/delete',
  },
};

// type转化为receive转化为collect
export const fixType = (type) => {
  return type === 'send' ? type : 'collect';
};

export const addressKeys = [
  'name',
  'company',
  'mobile',
  'province',
  'city',
  'district',
  'address',
  'save',
];

//
export const excludeAddressKeys = ['save', 'company'];

// 地址key对应的内容
export const addressKeysMap = {
  all: '地址信息',
  whatever: '地址信息有误',
  name: '姓名',
  send_name: '寄件人姓名',
  receive_name: '收件人姓名',
  company: '公司',
  send_company: '寄件人公司',
  receive_company: '收件人公司',
  mobile: '联系电话',
  send_mobile: '寄件人联系电话',
  receive_mobile: '收件人联系电话',
  province: '省份',
  send_province: '寄件人省份',
  receive_province: '收件人省份',
  city: '城市',
  send_city: '寄件人城市',
  receive_city: '收件人城市',
  district: '地区',
  send_district: '寄件人地区',
  receive_district: '收件人地区',
  address: '详细地址',
  send_address: '寄件人详细地址',
  receive_address: '收件人详细地址',
  door: '门牌号',
  longitude: '经纬度',
  latitude: '经纬度',
  service: '增值服务',
  realName: '实名信息',
};

// 包含收发件前缀的地址key
export const addressKeysAll = () => {
  const arr = [],
    excludeArr = [];
  if (addressList && addressList.length > 0) {
    addressList.map((item) => {
      if (addressKeys && addressKeys.length > 0) {
        addressKeys.map((iitem) => {
          arr.push(`${item.key}_${iitem}`);
        });
      }
      if (excludeAddressKeys && excludeAddressKeys.length > 0) {
        excludeAddressKeys.map((iitem) => {
          excludeArr.push(`${item.key}_${iitem}`);
        });
      }
    });
  }
  return {
    addressKeys: arr,
    excludeAddressKeys: excludeArr,
  };
};

// 检查数据完整性
export function checkDataComplete(
  data,
  keys = addressKeys,
  excludeKeys = excludeAddressKeys,
  prefix = '',
) {
  let n_keys = keys.filter((key) => !excludeKeys.includes(key));
  const form = getFormItem({
    keys: keys,
    prefix: prefix,
  });
  let checkRes = check({ form, data }) || {};
  const errors = n_keys.filter((key) => !data || !data[key]);
  let error = errors[0] || '';
  if (!error && checkRes.code > 0) {
    error = 'whatever';
  }
  if (!error) {
    return {
      error,
      complete: true,
    };
  }
  if (errors.length === n_keys.length) {
    // 所有信息都不存在
    return {
      error: 'all',
      complete: false,
    };
  }
  return {
    error,
    complete: false,
  };
}

// 获取地址编辑提交按钮
export function getSubmitBars(action, fixFor = 'edit') {
  if (action === 'fix') {
    const bars = [
      {
        key: 'receive',
        label: '用于收件地址',
      },
    ];
    if (fixFor === 'edit') {
      bars.unshift({
        key: 'send',
        label: '用于寄件地址',
        type: 'secondary',
      });
    }
    return bars;
  }
  return [
    {
      key: 'save',
      label: '保存',
    },
  ];
}

// 检查列表中是否存在错误的地址信息
export function getErrorAddressIndex(list, toast = true) {
  const index = list.findIndex((item) => item.error);
  let errMsg = '';
  if (index >= 0) {
    const { error } = list[index];
    const others = ['whatever']; // 其他错误，不用补充地址缺少
    const fix = others.includes(error) ? '' : `地址缺少`;
    errMsg = `第${1 + index}条${fix + addressKeysMap[error]}`;
    toast &&
      Taro.kbToast({
        text: errMsg,
      });
  }
  return { index, errMsg };
}

// 本地收件人列表缓存key
export const receiveStorageKey = 'receiveList';
export const sendStorageKey = 'send';
export const defaultSendStorageKey = 'address';
// 提取公共地址部分
export function extractAddressInfo(data, merge = []) {
  if (process.env.MODE_ENV == 'wkd') {
    return extractData(data, [
      ['name', 'real_name'],
      'name',
      ['mobile', (item) => mergeBySpace(item.mobile, item.telephone_code, item.phone)],
      'province',
      ['province', 'address_province'],
      ['province', 'province_name'],
      'city',
      ['city', 'address_city'],
      ['city', 'city_name'],
      ['district', 'address_county'],
      ['district', 'county_name'],
      ['district', 'county'],
      ['address', 'address_detail'],
      ['address', 'detail'],
      ...merge,
    ]);
  }
  return extractData(data, [
    'name',
    ['mobile', (item) => mergeBySpace(item.mobile, item.tel)],
    'province',
    'city',
    ['district', 'county'],
    ['address', 'detail'],
    ...merge,
  ]);
}

// 批量解析地址
export function batchAnalysisAddress(text, opts) {
  return new Promise((resolve, reject) => {
    const { filter, ...api } = opts || {};
    request({
      ...api,
      url: apis['address.parse'],
      data: {
        [process.env.MODE_ENV === 'wkd' ? 'address' : 'text']: text,
      },
      onThen: ({ data, code, msg }) => {
        let list = isArray(data)
          ? data
              .filter(
                filter
                  ? filter
                  : (item) =>
                      item.name &&
                      item.detail &&
                      item[process.env.MODE_ENV === 'wkd' ? 'province_name' : 'province'],
              )
              .map((item) =>
                extractAddressInfo(item, [
                  'province_shortname',
                  'original',
                  'province_confidence',
                  'city_confidence',
                  ['district_confidence', 'county_confidence'],
                ]),
              )
          : [];
        if (list.length > 0) {
          list = list.map((item) => {
            const { name, mobile, province, province_shortname, city, district, address, ...rest } =
              item || {};
            return {
              name: filterSpecialCharacters(name),
              mobile: filterSpecialCharacters(mobile),
              province: filterSpecialCharacters(province),
              province_shortname: filterSpecialCharacters(province_shortname),
              city: filterSpecialCharacters(city),
              district: filterSpecialCharacters(district),
              address: filterSpecialCharacters(address),
              ...rest,
            };
          });
          resolve(list);
        } else {
          reject(new Error(code > 0 ? msg : '未识别出合法地址'));
        }
      },
    });
  });
}

// 比较地址列表，判断两组数据的地址信息是否相同
export const checkRepeatKeysBatch = [
  'send_name',
  'send_company',
  'send_mobile',
  'send_province',
  'send_city',
  'send_district',
  'send_address',
  'receive_name',
  'receive_company',
  'receive_mobile',
  'receive_province',
  'receive_city',
  'receive_district',
  'receive_address',
];
export const checkRepeatKeys = ['name', 'mobile', 'province', 'city', 'district', 'address'];
export function repeatSortAddressList(list, isRemoveRepeat, keys) {
  const addressKeys = keys || checkRepeatKeys;
  let repeat = 0;
  list.map((item, index) => {
    const repeatIndex = list.findIndex(
      (iitem, iindex) =>
        iindex < index && isEqual(extractData(item, addressKeys), extractData(iitem, addressKeys)),
    );
    if (repeatIndex >= 0) {
      if (!isRemoveRepeat) {
        list.splice(repeatIndex, 0, item);
        list.splice(index + 1, 1);
        repeat++;
      } else {
        list.splice(index, 1);
        // 重复执行可不用考虑是否为有序的重复数据；
        // 如果确定是有序的可考虑使用快慢指针优化；
        repeatSortAddressList(list, isRemoveRepeat, keys);
      }
    }
  });
  return repeat;
}

//小邮桶中提交订单时，打印类型的转换
export function formatXytPrintType(type) {
  let print = void 0;
  switch (type) {
    case 'pprint':
      print = '1';
      break;
    case 'print':
      print = '3';
      break;
  }
  return print;
}

/**
 *
 * @description 创建tab
 * @param {*} org send | receive
 * @param {*} action select
 * @param {*} customer_id 大客户id
 * @returns
 */
export function createTabList({ org, action, customer_id }) {
  const SEND = org === 'send';
  const RECEIVE = org === 'receive';
  const SELECT = action === 'select';

  const send = [{ title: '发件人地址', key: 'send' }];
  const receive = [{ title: '收件人地址', key: 'receive' }];
  const eCode = [{ title: '收到的快递码', key: 'ecode' }];
  const person = [{ title: '个人', key: 'person' }];
  const company = [{ title: '企业', key: 'company' }];

  if (SELECT) {
    if (SEND) {
      if (customer_id) {
        return [...company, ...person];
      }
      return send;
    }
    if (RECEIVE) {
      if (customer_id) {
        return [...company, ...person, ...eCode];
      }
      return [...receive, ...eCode];
    }
  } else {
    if (customer_id) {
      return [...send, ...receive];
    }
    return [...send, ...receive, ...eCode];
  }
}

/**
 * 处理文件上传接口差异
 */
export const getUploadFileApis = ({ filePath, name }) => {
  return {
    url:
      process.env.MODE_ENV == 'wkd' ? '/g_wkd/v2/Excel/preExec' : '/order/BatchOrder/getFileData',
    data: {
      filePath,
      name: 'file',
      fileType: 'file',
      excel: name,
    },
    formatResponse: ({ code, data, msg }) => {
      return {
        code,
        data:
          process.env.MODE_ENV == 'wkd'
            ? {
                path: data.file,
                data: data.list,
              }
            : data,
        msg,
      };
    },
  };
};
// 抽取有数量单位的数字
export const getNumberByText = (text) => {
  if (isString(text)) {
    const res = text.match(/^(\d+)/);
    return res ? Number(res[0]) : null;
  }
  return null;
};
/**
 * @description 处理文件解析数据接口差异
 */
export const execFileDataApis = ({ allType, list, file }) => {
  let typeMap = {
    商品名称: '物品类型',
    '全部信息（姓名、电话、地址）': '全部信息',
    不导入: '忽略此项',
  };
  return {
    url: process.env.MODE_ENV == 'wkd' ? '/g_wkd/v2/Excel/exec' : '/order/BatchOrder/execFileData',
    data: {
      info:
        process.env.MODE_ENV == 'wkd'
          ? JSON.stringify(
              list.map((item) => {
                const { type } = item;
                return type.includes(allType) ? '所有' : typeMap[type] || type;
              }),
            )
          : list.map((item) => {
              const { type } = item;
              return type.includes(allType) ? '所有' : type;
            }),
      file,
    },
    nonceKey: 'md5:file',
    formatResponse: ({ code, data, msg }) => {
      let n_data = data;
      if (process.env.MODE_ENV == 'wkd' && data.length > 0) {
        n_data = data.map((item) => {
          return extractData(item, [
            ['name', 'receiveName'],
            ['mobile', 'receiveMobile'],
            ['province', 'receiveProvince'],
            ['city', 'receiveCity'],
            ['county', 'receiveArea'],
            ['detail', 'receiveAddress'],
            'product',
            'weight',
            ['note', 'mark'],
            ['collection_amount', 'collection_delivery'],
            ['to_pay_amount', 'to_pay'],
          ]);
        });
      }
      return {
        code,
        data: n_data,
        msg,
      };
    },
  };
};

/**
 *
 * @description 获取地址前缀
 * @param {*} req
 * @returns
 */
export function getAddressPrefix(req) {
  let prefix = '';
  if (process.env.MODE_ENV === 'wkd') {
    const { extraData } = req;
    const extraDataItem = extraData && extraData[0];
    if (extraDataItem && extraDataItem !== 'save') {
      prefix = extraDataItem;
    }
  }
  return prefix;
}

/**
 *
 * @description 格式化编辑地址请求
 * @param {*} req
 * @param {*} param1
 * @returns
 */
export function formatRequestEdit(req, { params, page }) {
  const { org, id, type, customer_id, source } = params;
  const { is_default = 0 } = page.state;
  let address_info =
    source != 'list'
      ? req
      : {
          ...extractData(req, [
            ['name', `${org}_name`],
            ['mobile', `${org}_mobile`],
            ['company', `${org}_company`],
            ['province', `${org}_province`],
            ['city', `${org}_city`],
            ['county', `${org}_district`],
            ['detail', `${org}_address`],
          ]),
          is_default,
          customer_id,
          type: fixType(org),
        };
  if (process.env.MODE_ENV == 'wkd') {
    if (type === 'company') {
      const { ...rest } = address_info;
      rest && delete rest.extraData;
      // 企业地址薄
      return {
        address_info: { ...rest, customer_id, is_default },
        id,
      };
    }
    address_info = {
      ...extractData(address_info, [
        ['real_name', 'name'],
        ['mobile', 'mobile'],
        ['address_province', 'province'],
        ['address_city', 'city'],
        ['address_county', 'county'],
        ['address_detail', 'detail'],
      ]),
      is_default,
      address_type: fixType(getAddressPrefix(req) || org),
    };
  }

  return {
    ...address_info,
    id,
  };
}

/**
 *
 * @description 格式化表单数据
 * @param {*} data
 * @param {*} req
 * @returns
 */
export function formatFormDataByPrefix(data, req) {
  if (process.env.MODE_ENV === 'wkd') {
    const prefix = getAddressPrefix(req);
    if (prefix) {
      const newData = {};
      Object.keys(data).forEach((key) => {
        newData[key.replace(/(.*)(_.*)/, `${prefix}$2`)] = data[key];
      });
      return newData;
    }
    return data;
  } else {
    return data;
  }
}

/**
 *
 * @description 兼容支付宝与微信
 * @returns
 */
export const chooseAddress = () => {
  return new Promise((resolve, reject) => {
    if (process.env.PLATFORM_ENV !== 'alipay') {
      Taro.chooseAddress()
        .then((res) => {
          const {
            userName: name,
            provinceName: province,
            cityName: city,
            countyName: district,
            detailInfo: address,
            telNumber: mobile,
          } = res;

          resolve({
            name,
            province,
            city,
            district,
            address,
            mobile,
          });
        })
        .catch(reject);
    } else {
      if (!my.getAddress) {
        reject(new Error('支付宝版本过低无法使用该功能'));
        return;
      }
      my.getAddress({
        success: (res) => {
          const {
            fullname: name,
            mobilePhone: mobile,
            prov: province,
            city,
            area: district,
            address,
          } = res.result || {};
          resolve({
            name,
            province,
            city,
            district,
            address,
            mobile,
          });
        },
        fail: reject,
      });
    }
  });
};

/**
 * @description 格式化地址请求参数
 * @param type person | company
 * @param addressType send | receive
 * @param customer_id 大客户id
 * @param {Function} query 获取搜索参数
 *  */
export const formatReqParams = ({ type, addressType, customer_id }) => {
  let reqParams = {};
  if (!type || !addressType) {
    return {};
  }
  // 大客户请求参数
  if (type === 'company') {
    reqParams = {
      extra_info: { type: fixType(addressType) },
      customer_id,
    };
  } else {
    reqParams = {
      address_type: fixType(addressType),
      page_per: 30,
    };
  }
  return reqParams;
};

/**
 * @description 兼容微快递原有搜索参数和大客户搜索参数
 *  */
export const formatSearchParams = (searchValue = {}, type) => {
  const params = { ...searchValue };
  const { search, extra_info = {} } = params;
  const isPhone = rules.check('phone', search).code == 0;
  if (type === 'company') {
    if (search) {
      extra_info.mobile = isPhone ? search : undefined;
      extra_info.name = !isPhone ? search : undefined;
    } else {
      extra_info.mobile = undefined;
      extra_info.name = undefined;
    }
  } else {
    //兼容微快递参数
    search && (params.words = search);
  }
  // 删除多余参数
  delete params.search;
  delete params.province;

  return params;
};
// 防抖处理后的
export const getReceiveStorageList = (status = 'reload', data) => {
  return new Promise((resolve, reject) => {
    const list = data || Taro.kbGetGlobalDataOnce(receiveStorageKey);
    if (list) {
      // 如果全局有数据，从全局获取
      resolve(list);
    } else if (status === 'init') {
      // 初次加载才会使用本地缓存数据
      getStorage({
        key: receiveStorageKey,
      })
        .then((res) => {
          const { data: resData } = res.data || {};
          resolve(resData);
        })
        .catch(() => {
          reject();
        });
    }
  });
};
//获取本地缓存的寄件人地址---主要是类似粘贴板那种临时性质的地址
export const getSendStorageData = () => {
  return new Promise((resolve, reject) => {
    getStorage({
      key: sendStorageKey,
    })
      .then((res) => {
        const { data } = res.data;
        if (data) {
          //使用一次后清空
          removeStorage({ key: sendStorageKey });
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
};
export const checkLastStorageData = (storageKey) => {
  return new Promise((resolve, reject) => {
    if (!storageKey) reject();
    getStorage({
      key: storageKey,
    })
      .then((res) => {
        const { data } = res.data;
        if (data && data.send_name) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
};
// 获取默认发件人
export const getDefaultSendAddress = (storageKey) => {
  return new Promise((resolve) => {
    // 默认发件人
    const type = 'send';
    // 如果粘贴板上存在地址，将其设置为默认发件人
    getSendStorageData()
      .then((data) => {
        resolve(data);
      })
      .catch(() => {
        if (process.env.MODE_ENV === 'wkd') {
          checkLastStorageData(storageKey)
            .then()
            .catch(() => {
              request({
                url: '/v1/user_address/getlist',
                data: {
                  address_type: fixType(type),
                  page_per: 5,
                  page: 1,
                },
                toastLoading: false,
                onThen: ({ code, data }) => {
                  if (code == 0 && isPlainObject(data[0])) {
                    const {
                      real_name: name,
                      telephone_code: tel,
                      mobile,
                      address_province: province,
                      address_city: city,
                      address_county: district,
                      address_detail: address,
                    } = data[0] || {};
                    if (!name) return; // 无默认发件人地址时依然响应了空对象
                    resolve({
                      name,
                      mobile: mergeBySpace(mobile, tel),
                      province,
                      city,
                      district,
                      address,
                    });
                  }
                },
              });
            });
        } else {
          request({
            url: '/api/weixin/mini/address/record/getDefault',
            data: {
              type: fixType(type),
            },
            toastLoading: false,
            onThen: ({ code, data }) => {
              if (code == 0 && isPlainObject(data)) {
                const {
                  name,
                  tel,
                  mobile,
                  company,
                  province,
                  city,
                  county: district,
                  detail: address,
                } = data;
                if (!name) return; // 无默认发件人地址时依然响应了空对象
                const [name_ = '', company_] = name.split('##');
                resolve({
                  name: name_,
                  mobile: mergeBySpace(mobile, tel),
                  company: company || company_,
                  province,
                  city,
                  district,
                  address,
                });
              }
            },
          });
        }
      });
  });
};

// 对可信任度较低的区域做标记
export const labelRedFn = (confidence) => {
  if (!confidence && confidence !== 0) return '';
  return `${confidence < 3 ? 'kb-color__orange' : ''}`;
};

export const getReceiveInfo = () => {
  // 获取收件人信息
  return new Promise((resolve, reject) => {
    getStorage({
      key: receiveStorageKey,
    })
      .then((res) => {
        const { data } = res.data || {};
        resolve(data);
      })
      .catch(reject);
  });
};

/**
 * 检查是否有默认的收件人信息
 *  */
export const checkDefaultReceiver = () => {
  const { data } = getStorageSync(defaultSendStorageKey) || {};
  const { data: receiveList = [] } = getStorageSync('receiveList') || {};
  const {
    receive_address,
    receive_city,
    receive_district,
    receive_mobile,
    receive_name,
    receive_province,
  } = data || {};

  const hasAddress = Boolean(
    receive_address ||
      receive_city ||
      receive_district ||
      receive_mobile ||
      receive_name ||
      receive_province,
  );

  if (receiveList.length > 1) {
    return false;
  }
  return hasAddress;
};

/**
 * 检查是否包含发件人信息
 */
export const checkHasSender = (list = []) => {
  let res = false;
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    if (item && item.send_province) {
      res = true;
      break;
    }
  }
  return res;
};

/**
 * 批量查询实名状态
 */
export const batchCheckRealName = (opts) => {
  const { phone = [] } = opts || {};
  console.log('phone', phone);
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/realname/auth/batchGetIdCardId',
      data: {
        phone: phone,
      },
      onThen: (res) => {
        const { success = [] } = res.data || {};
        resolve(success);
      },
    });
  });
};
