/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '~base/utils/request';

export const createAddrCodeService = (params) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/address/record/createAddressCode',
      data: params,
      toastError: true,
      onThen: (res) => {
        resolve(res.code == 0);
      },
    });
  });
};

export const deleteAddrCodeService = (id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/address/record/deleteAddressCode',
      data: {
        id,
      },
      toastError: true,
      onThen: (res) => {
        resolve(res.code == 0);
      },
    });
  });
};
