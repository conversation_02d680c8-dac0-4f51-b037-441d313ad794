/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { extractData } from '@base/utils/utils';
import { getNumberByText } from '@/components/_pages/address/_utils';

// excel导出项
export const excelColumn_all = '全部信息';
export const excelColumn_no = '不导入';
export const to_pay_column = {
  text: '到付',
  value: '到付',
};
export const collection_column = {
  text: '代收货款',
  value: '代收货款',
};

export const createExcelColumn = (opt) => {
  const { importMode = 'normal', isFileHasSender = false } = opt || {};
  let range = [
    {
      text: isFileHasSender ? '收件人姓名' : '姓名',
      value: isFileHasSender ? '收件人姓名' : '姓名',
      hide: false,
    },
    {
      text: isFileHasSender ? '收件人电话' : '电话',
      value: isFileHasSender ? '收件人电话' : '电话',
      hide: false,
    },
    {
      text: isFileHasSender ? '收件人省市区' : '省市区',
      value: isFileHasSender ? '收件人省市区' : '省市区',
      hide: false,
    },
    {
      text: isFileHasSender ? '收件人详细地址' : '详细地址',
      value: isFileHasSender ? '收件人详细地址' : '详细地址',
      hide: false,
    },
    {
      text: isFileHasSender ? '收件人公司名称' : '公司名称',
      value: isFileHasSender ? '收件人公司名称' : '公司名称',
      hide: false,
    },
    {
      text: isFileHasSender ? '收件人全部信息（姓名、电话、地址）' : '全部信息',
      value: isFileHasSender ? '收件人全部信息' : '全部信息',
      hide: false,
    },
    {
      text: '物品类型',
      value: '物品类型',
      hide: false,
    },
    {
      text: '重量',
      value: '重量',
      hide: false,
    },
    {
      text: '备注',
      value: '备注',
      hide: false,
    },
    {
      text: '发件人姓名',
      value: '发件人姓名',
      hide: !isFileHasSender,
    },
    {
      text: '发件人电话',
      value: '发件人电话',
      hide: !isFileHasSender,
    },
    {
      text: '发件人省市区',
      value: '发件人省市区',
      hide: !isFileHasSender,
    },
    {
      text: '发件人详细地址',
      value: '发件人详细地址',
      hide: !isFileHasSender,
    },
    {
      text: '发件人公司名称',
      value: '发件人公司名称',
      hide: !isFileHasSender,
    },
    {
      text: '发件人全部信息（姓名、电话、地址）',
      value: '发件人全部信息',
      hide: !isFileHasSender,
    },
    {
      text: '不导入',
      value: '',
      hide: false,
    },
  ].filter((item) => !item.hide);
  if (importMode == 'to_pay') {
    range.splice(4, 0, to_pay_column);
  } else if (importMode == 'collection') {
    range.splice(4, 0, collection_column);
  }
  return range;
};

// 整理导出解析后的数据
const formatExtraInfo = (data) => {
  const {
    collection_delivery,
    weight: goods_weight,
    to_pay,
    product: goods_name,
    note: goods_remark,
  } = data;
  let state = {
    service: {},
  };
  let service = state.service;
  let collection = getNumberByText(collection_delivery);
  let arrive_pay = getNumberByText(to_pay);
  if (goods_weight) {
    state.goods_weight = goods_weight;
  }
  if (goods_name) {
    state.goods_name = goods_name;
  }
  if (goods_remark) {
    state.goods_remark = goods_remark;
  }
  if (collection) {
    service.collection = collection;
  }
  if (arrive_pay) {
    service.arrive_pay = arrive_pay;
    service.is_arrive_pay = 1;
  }
  return state;
};
export const formatList = (data) => {
  const list = extractData(data, [
    'name',
    'company',
    'mobile',
    'province',
    'city',
    ['district', 'county'],
    ['address', 'detail'],
    ['extraInfo', formatExtraInfo],
  ]);
  return list;
};

// 格式excel解析的地址
export const formatExcelAddress = (data) => {
  return extractData(data, [
    ['receive_name', 'name'],
    ['receive_company', 'company'],
    ['receive_mobile', 'mobile'],
    ['receive_province', 'province'],
    ['receive_city', 'city'],
    ['receive_district', 'county'],
    ['receive_address', 'detail'],
    'send_name',
    'send_company',
    'send_mobile',
    'send_province',
    'send_city',
    ['send_district', 'send_county'],
    ['send_address', 'send_detail'],
    ['extraInfo', formatExtraInfo],
  ]);
};
