/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { apiURLs, fixType, extractAddressInfo } from '@/components/_pages/address/_utils';
import KbLongList from '@base/components/long-list';
import KbCheckbox from '@base/components/checkbox';
import KbSwipeAction from '@base/components/swipe-action';
import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import classNames from 'classnames';
import qs from 'qs';
import './index.scss';

@connect(({ global }) => ({ loginData: global.loginData }))
class Index extends Component {
  static defaultProps = {
    isManager: false,
    remove: false,
    active: false,
    multiple: false,
    selectted: [],
    onClickItem: () => {},
    onGetted: () => {},
    onDelete: () => {},
    query: () => {},
  };
  static options = {
    addGlobalClass: true,
  };
  constructor(props) {
    super(props);
    const { org, type, onGetted, query } = props;
    this.indexs = [];
    // 列表配置
    const { getList } = apiURLs[type] || {};
    const page_size = 30;
    const data = {
      type: fixType(org),
    };
    if (type === 'company') {
      // 企业地址薄
      data.page_size = page_size;
    }
    this.listData = {
      pageKey: 'page_num',
      api: {
        url: getList,
        data,
        interceptAllStatus: true,
        onIntercept: (req) => {
          if (type === 'person') {
            // 个人地址薄，本地数据分页
            // 拉取第一页，且搜索条件不存在时重新从服务器拉取数据
            const { page_num, search, province: matchByProvince } = req;
            if (this.personAddressList && (page_num > 1 || search || matchByProvince)) {
              const start = (page_num - 1) * page_size;
              const list = this.personAddressList
                .filter((item) => {
                  // 按照搜索条件过滤
                  const {
                    name = '',
                    mobile = '',
                    province = '',
                    city = '',
                    district = '',
                    address = '',
                  } = item;
                  const isMatchedSearch =
                    !search ||
                    `${name}-${mobile}-${province}-${city}-${district}-${address}`.includes(search);
                  const isMatchedProvince =
                    !matchByProvince || province.indexOf(matchByProvince) === 0;
                  return isMatchedSearch && isMatchedProvince;
                })
                .slice(start, start + page_size);
              return list.length > 0
                ? {
                    code: 0,
                    data: {
                      list,
                    },
                  }
                : { data: void 0 };
            }
          }
        },
        formatRequest: (req) => {
          const data = {
            ...req,
            ...query(req),
          };
          // 兼容后端判断逻辑，以下情况不穿才能正常；
          const { search, province } = data;
          if (!search) {
            delete data.search;
          }
          if (!province || province === '全国') {
            delete data.province;
          }
          return data;
        },
        formatResponse: (res) => {
          const { data } = res;
          let list = data.list || data;
          const hasList = isArray(list) && list.length > 0;
          if (hasList) {
            list = list.map((item) => {
              const { name: name_, company: company_, is_default, is_top } = item;
              const [name = '', company = company_] = name_.split('##');
              return {
                ...this.extractAddressInfo(item, [
                  ['origin_mobile', 'mobile'],
                  ['origin_tel', 'tel'],
                ]),
                is_default,
                is_top,
                name,
                company,
              };
            });
            if (type === 'person') {
              // 截取第一页内容
              this.personAddressList = [...list];
              list = list.slice(0, page_size);
            }
            return {
              data: { list },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list, res, req) => {
          this.setState({
            list,
          });
          if (req.page > 1 && !res.data) return;
          onGetted(list, res);
        },
      },
    };
    // 操作配置
    this.swipeActionOptions = [
      {
        key: 'delete',
        text: '删除',
        style: {
          backgroundColor: '#FF5A7A',
        },
      },
    ];
    this.state = {
      scrollTop: 0,
      openedId: null,
      list: [],
    };
  }

  // 跳转
  onClickItem = (data) => {
    const { type, onClickItem } = this.props;
    const { openedId } = this.state;
    // 左滑开启，禁止触发点击
    if (openedId) return;
    this.onSwitchSwipeAction(null, () => {
      onClickItem({
        type,
        data,
      });
    });
  };

  // 滑动展开切换
  onSwitchSwipeAction = (openedId = null, then) => {
    this.setState({ openedId }, then);
  };

  // 删除
  onSwipeActionClick = (id) => {
    this.props.onDelete([id]);
  };

  // 切换默认
  onChangeDefault = (item, e) => {
    e.stopPropagation();
    const { id, is_default } = item;
    const { org, type } = this.props;
    request({
      url: apiURLs[type].setDefault,
      data: {
        id,
        is_default: is_default === '1' ? '0' : '1',
        type: fixType(org),
      },
      toastError: true,
      onThen: ({ code }) => {
        if (code == 0) {
          this.listIns.loader();
        }
      },
    });
  };

  // 置顶
  onChangeSticky = (item, e) => {
    e.stopPropagation();
    const { type, org } = this.props;
    const { id, is_top } = item;
    request({
      url: apiURLs[type].setTop,
      data: {
        id,
        is_top: is_top === '1' ? '0' : '1',
        type: fixType(org),
      },
      toastError: true,
      onThen: ({ code }) => {
        if (code == 0) {
          this.listIns.loader();
        }
      },
    });
  };

  onReady = (ins) => {
    const { onReady } = this.props;
    this.listIns = ins;
    if (onReady) {
      onReady(ins);
    }
  };

  // 提取成对应的key值
  extractAddressInfo = (data, keys = []) => extractAddressInfo(data, ['company', 'id', ...keys]);

  // 编辑
  onEdit = (item, e) => {
    e.stopPropagation();
    const { org, type, action, multiple, pageType = '' } = this.props;
    Taro.navigator({
      url: `address/edit?${qs.stringify({
        ...item,
        org,
        type,
        action: multiple === '1' ? 'edit' : action,
        source: 'list',
        pageType,
      })}`,
    });
  };

  render() {
    const { list, scrollTop, openedId } = this.state;
    const { isManager, selectted, remove, action, active, type, org, multiple } = this.props;
    const isCompany = type === 'company';
    // 可置顶
    const canSticky = true;
    // 可编辑
    const canEdit = isManager || type === 'person';
    // 可设置默认
    const canDefault = canEdit && org === 'send';

    const listCls = classNames('kb-list kb-list-checkbox', {
      'kb-list-checkbox__open': remove || multiple === '1',
    });
    return (
      <KbLongList
        noDataText='您还没有地址信息哦~'
        data={this.listData}
        active={active}
        scrollTop={scrollTop}
        onReady={this.onReady}
        enableMore
      >
        <View className={listCls}>
          {list.map((item, index) => {
            const isToped = item.is_top === '1';
            const isDefault = item.is_default === '1';
            const stickyBoxCls = classNames('kb-address__top--bar', {
              'kb-address__top--bar-active': isToped,
            });
            const stickyIconCls = classNames(
              'kb-icon-size__xs',
              `kb-color__${isToped ? 'brand' : 'grey'}`,
            );
            const tagCls = classNames('at-tag at-tag--small at-tag--primary', {
              'at-tag--active': isDefault,
            });
            return (
              <View className='kb-list__item--wrapper' key={`${item.id}-${index}`}>
                <KbSwipeAction
                  autoClose
                  disabled={remove || (isCompany && !isManager) || action === 'select'}
                  isOpened={openedId === item.id}
                  onOpened={this.onSwitchSwipeAction.bind(this, item.id)}
                  onClosed={this.onSwitchSwipeAction}
                  onClick={this.onSwipeActionClick.bind(this, item.id)}
                  options={this.swipeActionOptions}
                  className='kb-radius-tr'
                >
                  <View
                    className='kb-list__item'
                    hoverClass='kb-hover'
                    onClick={this.onClickItem.bind(this, item)}
                  >
                    <View className='item-checkbox'>
                      <KbCheckbox
                        checked={selectted.includes(item.id)}
                        onChange={this.onClickItem.bind(this, item)}
                      />
                    </View>
                    <View className='item-content'>
                      <View className='item-content__title'>
                        <View className='at-row at-row__align--center'>
                          <Text className='item-content__title--text'>{item.name}</Text>
                          <Text className='item-content__title--text'>{item.mobile}</Text>
                          {canDefault && (
                            <View
                              className={tagCls}
                              hoverStopPropagation
                              onClick={this.onChangeDefault.bind(this, item)}
                            >
                              默认
                            </View>
                          )}
                        </View>
                      </View>
                      <View className='item-content__desc at-row at-row__align--center'>
                        <Text className='item-content__title--text'>{item.company}</Text>
                      </View>
                      <View className='item-content__desc'>
                        <Text className='item-content__desc--text'>{item.province}</Text>
                        <Text className='item-content__desc--text'>{item.city}</Text>
                        <Text className='item-content__desc--text'>{item.district}</Text>
                        <Text className='item-content__desc--text'>{item.address}</Text>
                      </View>
                    </View>
                    {!remove && canEdit && (
                      <View>
                        <View className='at-row at-row__align--center at-row__justify--between kb-address__bar'>
                          <View>
                            <View className='at-row at-row__align--center'>
                              <View
                                hoverClass='kb-hover'
                                hoverStopPropagation
                                className='kb-address__edit--bar'
                                onClick={this.onEdit.bind(this, item)}
                              >
                                <AtIcon
                                  prefixClass='kb-icon'
                                  value='edit-square'
                                  className='kb-icon-size__base kb-color__grey'
                                />
                              </View>
                              {canSticky && (
                                <View
                                  hoverClass='kb-hover-opacity'
                                  hoverStopPropagation
                                  className={stickyBoxCls}
                                  onClick={this.onChangeSticky.bind(this, item)}
                                >
                                  <AtIcon
                                    prefixClass='kb-icon'
                                    value='up'
                                    className={stickyIconCls}
                                  />
                                </View>
                              )}
                            </View>
                          </View>
                        </View>
                      </View>
                    )}
                  </View>
                </KbSwipeAction>
              </View>
            );
          })}
        </View>
      </KbLongList>
    );
  }
}

export default Index;
