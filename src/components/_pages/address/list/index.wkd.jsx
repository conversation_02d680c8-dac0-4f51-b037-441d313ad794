/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import {
  apiURLs,
  extractAddressInfo,
  fixType,
  formatReqParams,
  formatSearchParams,
} from '@/components/_pages/address/_utils';
import KbLongList from '@base/components/long-list';
import KbCheckbox from '@base/components/checkbox';
import KbSwipeAction from '@base/components/swipe-action';
import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import classNames from 'classnames';
import qs from 'qs';
import './index.wkd.scss';

class Index extends Component {
  static defaultProps = {
    isManager: false,
    remove: false,
    active: false,
    multiple: false,
    selectted: [],
    onClickItem: () => {},
    onGetted: () => {},
    onDelete: () => {},
    query: () => {},
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    const { org: tabKey, type, onGetted, query, customer_id } = this.props;
    // 大客户请求url
    const { getList } = apiURLs[type] || {};
    // 个人请求url
    let url = '/v1/user_address/getlist';

    const reqParams = formatReqParams({
      type, // company | person
      addressType: tabKey, // send | receive | ecode
      customer_id,
    });

    if (type === 'company') {
      url = getList;
    }

    // 列表配置
    this.listData = {
      pageKey: 'page',
      api: {
        url,
        data: reqParams,
        interceptAllStatus: true,
        formatRequest: (req) => {
          const data = {
            ...req,
            ...query(req),
          };
          return formatSearchParams(data, type);
        },
        formatResponse: (res) => {
          const { data } = res;
          let list = data.list || data;
          const hasList = isArray(list) && list.length > 0;
          if (hasList) {
            list = list.map((item) => {
              const { is_default, is_top } = item;
              return {
                ...this.extractAddressInfo(item),
                is_default,
                is_top,
              };
            });
            return {
              data: { list },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list, res, req) => {
          this.setState({
            list,
          });
          if (req.page > 1 && !res.data) return;
          onGetted(list, res);
        },
      },
    };
    // 操作配置
    this.swipeActionOptions = [
      {
        key: 'delete',
        text: '删除',
        style: {
          backgroundColor: '#FF5A7A',
        },
      },
    ];
    this.state = {
      openedId: null,
      list: [],
    };
  }

  // 跳转
  onClickItem = (data) => {
    const { type, onClickItem } = this.props;
    const { openedId } = this.state;
    // 左滑开启，禁止触发点击
    if (openedId) return;
    this.onSwitchSwipeAction(null, () => {
      onClickItem({
        type,
        data,
      });
    });
  };

  // 滑动展开切换
  onSwitchSwipeAction = (openedId = null, then) => {
    this.setState({ openedId }, then);
  };

  // 删除
  onSwipeActionClick = (id) => {
    this.props.onDelete([id]);
  };

  // 切换默认
  onChangeDefault = (item, e) => {
    e.stopPropagation();
    const { id, is_default } = item;
    const { org, type, customer_id } = this.props;
    request({
      url: apiURLs[type].setDefault,
      data: {
        id,
        is_default: is_default === '1' ? '0' : '1',
        type: fixType(org),
        customer_id,
      },
      toastError: true,
      onThen: ({ code }) => {
        if (code == 0) {
          this.listIns.loader();
        }
      },
    });
  };

  // 置顶
  onChangeSticky = (item, e) => {
    e.stopPropagation();
    const { type, org, customer_id } = this.props;
    const { id, is_top } = item;
    request({
      url: apiURLs[type].setTop,
      data: {
        id,
        is_top: is_top === '1' ? '0' : '1',
        type: fixType(org),
        customer_id,
      },
      toastError: true,
      onThen: ({ code }) => {
        if (code == 0) {
          this.listIns.loader();
        }
      },
    });
  };

  onReady = (ins) => {
    this.listIns = ins;
  };

  // 提取成对应的key值
  extractAddressInfo = (data) => extractAddressInfo(data, ['company', 'id']);

  // 编辑
  onEdit = (item, e) => {
    e.stopPropagation();
    const { org, type, action, multiple, customer_id } = this.props;
    Taro.navigator({
      url: `address/edit?${qs.stringify({
        ...item,
        org,
        type,
        action: multiple === '1' ? 'edit' : action,
        customer_id,
        source: 'list',
      })}`,
    });
  };

  render() {
    const { list, openedId } = this.state;
    const {
      isManager,
      selectted,
      remove,
      action,
      active,
      type,
      multiple,
      org: tabKey,
    } = this.props;
    const isCompany = type === 'company';
    const isPerson = type === 'person';
    const isSend = tabKey === 'send';
    // 可置顶
    const canSticky = (isCompany && isManager) || isPerson;
    // 可编辑
    const canEdit = isPerson || (isCompany && isManager);
    // 是否提供默认功能（大客户）
    const canDefault = isManager && isCompany && tabKey === 'send';
    const listCls = classNames('kb-list kb-list-checkbox', {
      'kb-list-checkbox__open': remove || multiple === '1',
    });
    return (
      <KbLongList
        noDataText='您还没有地址信息哦~'
        data={this.listData}
        active={active}
        onReady={this.onReady}
        enableMore={isPerson}
      >
        <View className={listCls}>
          {list.map((item, index) => {
            const isToped = item.is_top === '1';
            const isDefault = item.is_default === '1';
            const stickyBoxCls = classNames('kb-address__top--bar', {
              'kb-address__top--bar-active': isToped,
            });
            const stickyIconCls = classNames(
              'kb-icon-size__xs',
              `kb-color__${isToped ? 'brand' : 'grey'}`,
            );
            const tagCls = classNames('at-tag at-tag--small at-tag--primary', {
              'at-tag--active': isDefault,
            });
            return (
              <View className='kb-list__item--wrapper' key={`${item.id}-${index}`}>
                <KbSwipeAction
                  autoClose
                  disabled={remove || (isCompany && !isManager) || action === 'select'}
                  isOpened={openedId === item.id}
                  onOpened={this.onSwitchSwipeAction.bind(this, item.id)}
                  onClosed={this.onSwitchSwipeAction.bind(this, '')}
                  onClick={this.onSwipeActionClick.bind(this, item.id)}
                  options={this.swipeActionOptions}
                  className='kb-radius-tr'
                >
                  <View
                    className='kb-list__item'
                    hoverClass='kb-hover'
                    onClick={this.onClickItem.bind(this, item)}
                  >
                    <View className='item-checkbox'>
                      <KbCheckbox
                        checked={selectted.includes(item.id)}
                        onChange={this.onClickItem.bind(this, item)}
                      />
                    </View>
                    <View className='item-content'>
                      <View className='item-content__title'>
                        <View className='at-row at-row__align--center'>
                          <Text className='item-content__title--text'>{item.name}</Text>
                          <Text className='item-content__title--text'>{item.mobile}</Text>
                          {canDefault && (
                            <View
                              className={tagCls}
                              hoverStopPropagation
                              onClick={this.onChangeDefault.bind(this, item)}
                            >
                              默认
                            </View>
                          )}
                        </View>
                      </View>
                      <View className='item-content__desc'>
                        <Text className='item-content__desc--text'>{item.province}</Text>
                        <Text className='item-content__desc--text'>{item.city}</Text>
                        <Text className='item-content__desc--text'>{item.district}</Text>
                        <Text className='item-content__desc--text'>{item.address}</Text>
                      </View>
                      {isManager && isCompany && isSend && isDefault && (
                        <View className={classNames(tagCls, 'kb-address__default')}>
                          企业统一寄件人
                        </View>
                      )}
                    </View>
                    {!remove && canEdit && (
                      <View>
                        <View className='at-row at-row__align--center at-row__justify--between kb-address__bar'>
                          <View>
                            <View className='at-row at-row__align--center'>
                              <View
                                hoverClass='kb-hover'
                                hoverStopPropagation
                                className='kb-address__edit--bar'
                                onClick={this.onEdit.bind(this, item)}
                              >
                                <AtIcon
                                  prefixClass='kb-icon'
                                  value='edit-square'
                                  className='kb-icon-size__base kb-color__grey'
                                />
                              </View>
                              {canSticky && (
                                <View
                                  hoverClass='kb-hover-opacity'
                                  hoverStopPropagation
                                  className={stickyBoxCls}
                                  onClick={this.onChangeSticky.bind(this, item)}
                                >
                                  <AtIcon
                                    prefixClass='kb-icon'
                                    value='up'
                                    className={stickyIconCls}
                                  />
                                </View>
                              )}
                            </View>
                          </View>
                        </View>
                      </View>
                    )}
                  </View>
                </KbSwipeAction>
              </View>
            );
          })}
        </View>
      </KbLongList>
    );
  }
}

export default Index;
