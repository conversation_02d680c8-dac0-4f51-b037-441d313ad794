/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect, useRef, Fragment } from '@tarojs/taro';
import KbModal from '@base/components/modal';
import { AtButton } from 'taro-ui';
import KbAddressEdit from '@/components/_pages/order/address-edit';
import isArray from 'lodash/isArray';
import { formatAddress } from '@/components/_pages/order/_utils';
import isFunction from 'lodash/isFunction';

/**
 *
 * @description 为保证弹窗这招问题
 * @param {*} props
 */

const SendReceive = (props) => {
  const { data, onConfirm, type = 'bar', actionRef } = props;
  const [isOpened, setIsOpened] = useState(false);
  const [addressData, setAddressData] = useState(null);
  const ref = useRef({ addressData });

  // 弹窗关闭
  const handleClose = () => setIsOpened(false);
  // 按钮点击
  const handleClick = () => {
    actionRef.current.open(data);
  };

  const handleSwitch = (a) => {
    ref.current.addressData = a;
  };

  useEffect(() => {
    ref.current.addressData = addressData;
  }, [addressData]);

  const handleCOnfirm = () => {
    if (isFunction(onConfirm)) {
      onConfirm(ref.current.addressData);
    }
    handleClose();
  };

  // 为弹窗绑定方法
  useEffect(() => {
    if (type === 'modal') {
      actionRef.current = {
        open: (data) => {
          if (isArray(data)) {
            // 收发件地址
            const [send, receive] = data.slice(0, 2);
            setAddressData({
              ...formatAddress(send, 'send', { reverse: true }).data,
              ...formatAddress(receive, 'receive', { reverse: true }).data,
            });
          }
          setIsOpened(true);
        },
      };
    } else if (actionRef.current) {
    }
  }, [type]);

  return (
    <Fragment>
      {type === 'bar' ? (
        <AtButton type='secondary' circle onClick={handleClick}>
          以发件收件人提交
        </AtButton>
      ) : (
        <KbModal
          onConfirm={handleCOnfirm}
          onCancel={handleClose}
          onClose={handleClose}
          isOpened={isOpened}
        >
          {addressData && (
            <KbAddressEdit
              switchAddress
              showClipborard={false}
              lib={false}
              storageKey=''
              data={addressData}
              onSwitch={handleSwitch}
            />
          )}
        </KbModal>
      )}
    </Fragment>
  );
};

SendReceive.options = {
  addGlobalClass: true,
};

export default SendReceive;
