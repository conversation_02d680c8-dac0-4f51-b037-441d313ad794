/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { dateCalendar } from '@base/utils/utils';

export function createInvoiceDetail(data, status) {
  let list = [
    {
      label: '申请时间',
      key: 'createAt',
      render: (value) => dateCalendar(value, { timer: true }),
    },
    {
      label: '电子发票',
      key: 'status',
      color: 'brand',
    },
    {
      label: '开票方',
      key: 'drawer',
    },
    {
      label: '抬头类型',
      key: 'type',
      render: (value) => {
        return value === 'company' ? '企业抬头' : '个人抬头';
      },
    },
    {
      label: '发票抬头',
      key: 'title',
    },
    {
      label: '公司税号',
      key: 'taxNumber',
      render: (value, { type }) => {
        if (type !== 'company') return null;
        return value;
      },
    },
    {
      label: '发票备注',
      key: 'remark',
      defaultValue: '暂无备注',
    },
    {
      label: '电子邮箱',
      key: 'email',
    },
  ];

  if (status === 'preview') {
    //   预览
    const filterKeys = ['title', 'taxNumber', 'email'];
    list = list.filter((item) => filterKeys.includes(item.key));
    list.unshift({
      label: '发票类型',
      defaultValue: '电子发票',
      key: 'invoice_type',
    });
  }

  return list
    .map(({ key, defaultValue, render = (val) => val, ...rest }) => ({
      ...rest,
      key,
      value: render(data[key] || defaultValue, data),
    }))
    .filter((item) => !!item.value);
}

export function createInvoiceExtra(data) {
  const list = [
    {
      label: '公司地址',
      key: 'company_address',
      value: data.company_address,
    },
    {
      label: '公司电话',
      key: 'company_tel',
      value: data.company_tel,
    },
    {
      label: '开户行',
      key: 'bank_name',
      value: data.bank_name,
    },
    {
      label: '银行账号',
      key: 'bank_account',
      value: data.bank_account,
    },
  ];
  return list.filter((item) => item.value);
}
