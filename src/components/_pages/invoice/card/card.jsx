/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCheckbox from '@base/components/checkbox';
import { dateCalendar } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { useMemo } from '@tarojs/taro';
import './index.scss';

const Index = (props) => {
  const { selected, mode, data } = props;
  const isChecked = useMemo(() => {
    if (selected && data) {
      const { orderId } = data;
      return selected.includes(orderId);
    }
    return false;
  }, [selected, data]);

  const handleChange = () => {
    if (mode !== 'select') return;
    props.onSelect(data);
  };

  return (
    <View
      className='kb-list__item--wrapper kb-margin-md-t'
      onClick={handleChange}
      hoverClass={mode === 'select' ? 'kb-hover' : 'none'}
    >
      <View className='kb-invoice-card__title'>
        <View className='at-row at-row__align--center at-row__justify--between'>
          <Text className='kb-color__grey'>
            {dateCalendar(data.dateTime || data.orderTime, { timer: true })}
          </Text>
          <Text className='kb-size__xl'>¥ {data.price}</Text>
        </View>
      </View>
      <View className='kb-list__item'>
        <View className='item-content'>
          <View className='item-content__title'>
            <View className='at-row at-row__align--center'>
              <Text>订单编号：{data.orderId}</Text>
              <View className='kb-invoice-card__tag'>{data.brand}</View>
            </View>
          </View>
          <View className='item-content__title kb-invoice-card__item--send'>
            {data.sendAddress}
          </View>
          <View className='item-content__title kb-invoice-card__item--receive'>
            {data.receiverAddress}
          </View>
        </View>
        {mode === 'select' && (
          <View className='item-checkbox'>
            <KbCheckbox checked={isChecked} onChange={handleChange} />
          </View>
        )}
      </View>
    </View>
  );
};
Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  mode: 'select',
  onSelect: () => {},
  data: {},
  selected: [],
};
export default Index;
