/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbLoginAuthAndBind from '@base/components/login/authAndBind';
import KbEmpty from '@base/components/empty';

function Index() {
  return (
    <View
      style={{
        marginTop: '50px',
      }}
    >
      <KbEmpty description='报销是需要手机号的哟' />
      <View className='kb-margin-md-t'>
        <KbLoginAuthAndBind />
      </View>
    </View>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
