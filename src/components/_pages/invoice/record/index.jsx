/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbList from '@base/components/long-list';
import KbCheckboxAll from '@base/components/checkbox/all';
import KbListCard from '@/components/_pages/invoice/card/card';
import { View, Text } from '@tarojs/components';
import isArray from 'lodash/isArray';
import { noop } from '@base/utils/utils';
import './index.scss';

class Index extends Component {
  static defaultProps = {
    selectedAll: false,
    active: false,
    onReady: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    this.state = { money: 0, list: [], selected: [] };
    this.listData = {
      api: {
        url: '/g_wkd/v2/Invoice/getInvoiceOrders',
        data: { limit: 50 },
        formatResponse: (res) => {
          const { data: list } = res;
          if (isArray(list) && list.length > 0) {
            return {
              data: { list },
            };
          } else {
            return {
              data: void 0,
            };
          }
        },
        onThen: (list, _, req) => {
          this.setState({
            list,
          });
          if (req.page === 1) {
            // 刷新页面，清空选择
            this.setState({
              selected: [],
            });
          }
        },
      },
    };
  }

  handleReady = (ins) => this.props.onReady(ins);

  // 计算金额
  countMoney = (selected) => {
    const { list } = this.state;
    const money = selected
      .map((orderId) => {
        const { price } = list.find((item) => item.orderId === orderId);
        return 1 * (price || 0);
      })
      .reduce((total, cur) => total + cur, 0);
    this.setState({ money: 1 * money.toFixed(2) });
  };

  handelSelected = (item) => {
    const { orderId } = item;
    const { selected } = this.state;
    const index = selected.findIndex((item) => item === orderId);
    if (index >= 0) {
      selected.splice(index, 1);
    } else {
      selected.push(orderId);
    }
    this.setState({ selected: [...selected] });
    this.countMoney(selected);
  };

  // 开票下一步
  handleNext = () => {
    const { selected, money, list } = this.state;
    if (selected.length > 0) {
      Taro.navigator({
        url: 'invoice/edit',
        key: 'invoiceInfo',
        options: {
          money,
          list: selected.map((selectedOrderId) => {
            const { orderId, brandType } = list.find((item) => item.orderId === selectedOrderId);
            return { orderId, brandType };
          }),
        },
        onArrived: () => {
          console.log('通过postMessage传递大量数据');
        },
      });
    }
  };
  handleCheckboxAllChange = () => {
    const { list, selected: oldSelected } = this.state;
    let selected = [];
    if (list.length !== oldSelected.length) {
      selected = list.map((item) => item.orderId);
    }
    this.setState({ selected });
    this.countMoney(selected);
  };

  render() {
    const { active } = this.props;
    const { money, list, selected } = this.state;
    return (
      <KbList
        noDataText='呀，空空如也～'
        enableMore
        data={this.listData}
        onReady={this.handleReady}
        active={active}
      >
        <View className='kb-list'>
          {list.map((item) => (
            <KbListCard
              data={item}
              onSelect={this.handelSelected}
              key={item.orderId}
              selected={selected}
            />
          ))}
        </View>
        <View className='kb-invoice-record__checkbox'>
          <View className='kb-invoice-record__checkbox--fixed'>
            <KbCheckboxAll
              confirmText='下一步'
              total={list.length}
              count={selected.length}
              onConfirm={this.handleNext}
              onChange={this.handleCheckboxAllChange}
              renderLabel={
                <View className='kb-size__base'>
                  <Text>订单，共</Text>
                  <Text className='kb-color__brand'>{money}</Text>
                  <Text>元</Text>
                </View>
              }
            />
          </View>
        </View>
      </KbList>
    );
  }
}

export default Index;
