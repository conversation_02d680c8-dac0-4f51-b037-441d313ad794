/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Image } from '@tarojs/components';
import './index.scss';

const Index = (props) => {
  const { step } = props;
  return (
    <View className='kb-guide-welfare'>
      {step === 1 ? (
        <Image
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/step1.png'
          mode='widthFix'
          className='kb-image__610x530'
        />
      ) : step == 2 ? (
        <View>
          <Image
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/step21.png'
            mode='widthFix'
            className='kb-image__440x160 kb-guide-welfare--img2'
          />
          <Image
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/step22.png'
            mode='widthFix'
            className='kb-image__500x470 kb-guide-welfare--img3'
          />
        </View>
      ) : null}
    </View>
  );
};

Index.defaultProps = {
  step: 1,
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
