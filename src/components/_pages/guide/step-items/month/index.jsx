/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Image } from '@tarojs/components';
import './index.scss';

const Index = (props) => {
  const { step } = props;

  return (
    <View className='kb-guide-month kb-size__sm kb-color__white'>
      {step == 1 ? (
        <View className='kb-guide-month-1 kb-spacing-lg-l'>
          <View>
            <View>1、本次活动分四周达成目标，</View>
            <View>每周第一次进入活动即有机会领现金红包呦~</View>
          </View>
          <View className='at-row at-row__align--center at-row__justify--center kb-spacing-lg-t'>
            <Image
              lazyLoad
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/month/guide/guide.png'
              mode='widthFix'
              className='kb-image__86x80'
            />
          </View>
          <View className='at-row at-row__align--center at-row__justify--center kb-spacing-lg-t'>
            <Image
              lazyLoad
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/month/guide/step1.png'
              mode='widthFix'
              className='kb-image__650x130'
            />
          </View>
        </View>
      ) : step == 2 ? (
        <View className='kb-guide-month-2'>
          <View>
            <View>2、自主选择极兔品牌成功寄件，或分享个人小程序码</View>
            <View>邀请好友选择极兔品牌成功寄件，</View>
            <View>即有机会领取现金红包</View>
          </View>
          <View className='at-row at-row__align--center at-row__justify--center kb-spacing-lg-t'>
            <Image
              lazyLoad
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/month/guide/guide.png'
              mode='widthFix'
              className='kb-image__86x80'
            />
          </View>
          <View className='at-row at-row__align--center at-row__justify--center kb-spacing-lg-t'>
            <Image
              lazyLoad
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/month/guide/step2.png'
              mode='widthFix'
              className='kb-image__696x140'
            />
          </View>
        </View>
      ) : step == 3 ? (
        <View className='kb-guide-month-3'>
          <View className='at-row at-row__align--center mb-50'>
            <Image
              lazyLoad
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/month/guide/step32.png'
              mode='widthFix'
              className='kb-image__270x90'
            />
          </View>
          <View>
            <View>3、活动期间所有现金红包都会暂存在你的</View>
            <View>“小猪存钱罐”等待开奖提现呦~</View>
          </View>
          <View className='at-row at-row__align--center at-row__justify--center kb-spacing-lg-t'>
            <Image
              lazyLoad
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/month/guide/guide.png'
              mode='widthFix'
              className='kb-image__86x80'
            />
          </View>
          <View className='at-row at-row__align--center at-row__justify--end kb-spacing-lg-t'>
            <Image
              lazyLoad
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/month/guide/step3.png'
              mode='widthFix'
              className='kb-image__136x150'
            />
          </View>
        </View>
      ) : null}
    </View>
  );
};

Index.defaultProps = {
  step: 1,
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
