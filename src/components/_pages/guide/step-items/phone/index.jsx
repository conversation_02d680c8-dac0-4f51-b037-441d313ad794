/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import './index.scss';

const Index = () => {
  return (
    <View className='kb-guide-phone kb-color__white'>
      <View>
        <Image
          class='kb-guide-phone--img kb-image__484x210'
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/phone_tip.png'
          mode='widthFix'
        />
      </View>
    </View>
  );
};

Index.defaultProps = {};
Index.options = {
  addGlobalClass: true,
};

export default Index;
