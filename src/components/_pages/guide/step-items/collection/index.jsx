/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import './index.scss';

const Index = () => {
  return (
    <View className='kb-guide-collection'>
      <Image
        src='https://cdn-img.kuaidihelp.com/wkd/miniApp/images/guide.png'
        mode='widthFix'
        className='kb-image__100x171 kb-guide-collection--img'
      />
      <View className='kb-guide-collection--text'>
        <View>添加到桌面</View>
        <View>查看订单更方便呦~</View>
      </View>
      <View className='kb-guide-collection--btn' hoverClass='kb-hover'>
        知道啦
      </View>
    </View>
  );
};

Index.defaultProps = {};
Index.options = {
  addGlobalClass: true,
};

export default Index;
