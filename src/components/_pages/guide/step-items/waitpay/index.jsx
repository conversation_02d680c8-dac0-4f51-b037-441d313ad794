/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Image } from '@tarojs/components';
import './index.scss';

const Index = () => {
  return (
    <View className='kb-guide-waitpay'>
      <Image
        lazyLoad
        class='kb-guide-waitpay--img kb-image__452x354'
        src='https://cdn-img.kuaidihelp.com/wkd/miniApp/yjkd/tips.png'
        mode='widthFix'
      />
    </View>
  );
};

Index.defaultProps = {};
Index.options = {
  addGlobalClass: true,
};

export default Index;
