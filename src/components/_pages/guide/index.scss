/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$z-index-guide: 9000;

.kb-guide {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $z-index-guide + 1;
  color: #fff;

  &-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index-guide;
    background: rgba(0, 0, 0, 0.65);
    transform: scale3d(1, 1, 0);
    opacity: 0;
    transition: all 0.3s ease-in-out;
    pointer-events: none;

    &__show {
      transform: scale3d(1, 1, 1);
      opacity: 1;
      pointer-events: all;
    }
  }
}
