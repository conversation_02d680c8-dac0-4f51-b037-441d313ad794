/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getSystemInfoSync } from '@base/utils/utils';

// 创建缓存key
export const createStorageKey = (type) => {
  return type === 'month' ? 'kb-guide-month' : `isHasGuide--${type}-1`;
};

/**
 *
 * @description 获取总步数
 * @param {*} type
 * @returns
 */
export const getTotalStep = (type) => {
  const stepMap = {
    month: 3,
    welfare: 2,
  };
  return stepMap[type] || 1;
};

/**
 *
 * @description 检查禁止引导的类型：仅安卓系统支持加到桌面
 * @param {*} type
 * @returns
 */
export const checkDisabledGuide = (type) => {
  return type === 'collection' && getSystemInfoSync().platform !== 'android';
};
