/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useUpdate } from '@base/hooks/page';
import { getStorage, noop, setStorage } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import { Fragment, useEffect, useState } from '@tarojs/taro';
import classNames from 'classnames';
import './index.scss';
import KbCollection from './step-items/collection';
import KbMonth from './step-items/month';
import KbPhone from './step-items/phone';
import KbWaitpay from './step-items/waitpay';
import KbWelfare from './step-items/welfare';
import { checkDisabledGuide, createStorageKey, getTotalStep } from './_utils';

const Index = (props) => {
  const { type } = props;
  const { isVip } = useSelector((state) => state.global);
  const [isOpened, updateIsOpened] = useState(false);
  const [step, updateStep] = useState(1);
  const maskCls = classNames('kb-guide-mask', {
    'kb-guide-mask__show': isOpened,
  });

  const storageKey = createStorageKey(type);
  const total = getTotalStep(type);

  // 打开引导，并记录缓存
  const openAndRecord = () => {
    updateIsOpened(true);
    setStorage({
      key: storageKey,
      data: true,
    });
  };

  // 已经引导过，或首次结束引导
  const handleEnd = () => props.onEnd();

  useUpdate(
    (data) => {
      if (!type || !data.logined) return;
      if (checkDisabledGuide(type)) {
        return;
      }
      if (process.env.PLATFORM_ENV === 'alipay') {
        // 支付宝暂时屏蔽福利中心
        if (type === 'welfare') return;
      }
      getStorage({ key: storageKey })
        .then((res) => {
          const { data = false } = res.data || {};
          if (data) {
            handleEnd();
            return;
          }
          openAndRecord();
        })
        .catch(openAndRecord);
    },
    [type],
  );

  useEffect(() => {
    //  福利中心：vip快递员隐藏引导
    if (isVip && type === 'welfare') {
      updateIsOpened(false);
    }
  }, [isVip, type]);

  // 下一步
  const handleNext = () => {
    const nextStep = 1 + step;
    if (nextStep > total) {
      updateIsOpened(false);
      handleEnd();
      return;
    }
    updateStep(nextStep);
  };

  return process.env.MODE_ENV === 'wkd' ? (
    <Fragment>
      {isOpened && (
        <View class='kb-guide' onClick={handleNext}>
          {type === 'collection' && <KbCollection step={step} />}
          {type === 'month' && <KbMonth step={step} />}
          {type === 'waitpay' && <KbWaitpay step={step} />}
          {type === 'welfare' && <KbWelfare step={step} />}
          {type === 'phone' && <KbPhone step={step} />}
        </View>
      )}
      <View className={maskCls} />
    </Fragment>
  ) : (
    <Fragment>
      {isOpened && (
        <View class='kb-guide' onClick={handleNext}>
          {type === 'phone' && <KbPhone step={step} />}
        </View>
      )}
      <View className={maskCls} />
    </Fragment>
  );
};

Index.defaultProps = {
  type: '',
  onEnd: noop,
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
