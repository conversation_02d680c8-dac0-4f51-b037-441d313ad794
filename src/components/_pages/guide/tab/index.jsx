/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { getPage, getStorage, setStorage } from '@base/utils/utils';
import { setGuideTab } from '@/actions/guide-tab';
import { useDispatch, useSelector } from '@tarojs/redux';
import './index.scss';

const Index = (props) => {
  const { tips } = props;
  const { guideOpened = false } = useSelector((state) => state.global);
  const [isOpened, updateIsOpened] = useState(false);
  const storageKey = 'guide-tab';
  const dispatch = useDispatch();
  const tabs = ['query/index'];

  const handleClose = () => {
    updateIsOpened(false);
    dispatch(setGuideTab(false));
  };

  useEffect(() => {
    const page = getPage(-1);
    const { $router: { path = '' } = {} } = page || {};
    const isInTabs = !!tabs.find((item) => path.includes(item));
    updateIsOpened(isInTabs);

    if (path.includes('user/index')) {
      handleClose();
    } else {
      if (guideOpened) return;
      getStorage({
        key: storageKey,
      })
        .then((res) => {
          const { data = false } = res.data || {};
          if (data) return;
          dispatch(setGuideTab(true));
        })
        .catch(() => dispatch(setGuideTab(true)));
    }

    setStorage({
      key: storageKey,
      data: true,
    });
  }, []);

  return isOpened && guideOpened ? (
    <View className='kb-guide-tab position-rb' onClick={handleClose}>
      <Text className='kb-icon kb-icon-cancel' />
      {tips.map((item) => (
        <View key='*this'>{item}</View>
      ))}
    </View>
  ) : (
    <Fragment />
  );
};

Index.defaultProps = {
  tips: [],
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
