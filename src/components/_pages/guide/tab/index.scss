/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-guide-tab {
  position: fixed;
  z-index: 999;
  padding: $spacing-v-md $spacing-h-lg;
  color: $color-white;
  background-color: rgba($color: #000000, $alpha: 0.7);
  border-radius: $border-radius-md;
  animation: jump 1s ease-in-out infinite;

  &::after {
    position: absolute;
    right: 55px;
    bottom: -13px;
    border: 15px solid rgba($color: #000000, $alpha: 0.7);
    border-right-color: transparent;
    border-bottom: 0;
    border-left-color: transparent;
    content: '';
  }

  &.position-rb {
    right: $spacing-h-md;
    bottom: $spacing-v-md;
  }

  & .kb-icon {
    position: absolute;
    top: $spacing-v-sm;
    right: $spacing-h-xs;
    font-size: $icon-font-size-base;
  }
}

@keyframes jump {
  0% {
    transform: translateY(-10px);
  }

  50% {
    transform: translateY(10px);
  }

  100% {
    transform: translateY(-10px);
  }
}
