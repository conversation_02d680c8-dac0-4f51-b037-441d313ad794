/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Button } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import './index.scss';

const KbCopyRight = (props) => {
  const { service } = props;
  const handleToService = (e) => {
    e.stopPropagation();
  };
  return process.env.MODE_ENV !== 'third.post' && process.env.MODE_ENV !== 'wkd' ? (
    <View className='kb-copy-right' hoverStopPropagation onClick={handleToService}>
      快宝驿站提供{service}服务
      {process.env.PLATFORM_ENV === 'weapp' && (
        <Fragment>
          ，
          <Button
            // openType="contact"
            onClick={Taro.navigateToDocument.bind(null, 6)}
            className='kb-copy-right__button'
            hoverClass='kb-hover-opacity'
          >
            联系客服
            <AtIcon
              prefixClass='kb-icon'
              value='service'
              className='kb-icon-size__base kb-color__brand-light'
            />
          </Button>
        </Fragment>
      )}
    </View>
  ) : (
    <Fragment />
  );
};

KbCopyRight.defaultProps = {
  service: '寄件',
};

KbCopyRight.options = {
  addGlobalClass: true,
};

export default KbCopyRight;
