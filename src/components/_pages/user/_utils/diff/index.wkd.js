/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { CreditConfig } from '@/components/_pages/order/_utils/order.credit-pay';

/**
 *
 * @description 创建bars
 * @returns
 */
export const createBars = () => {
  // 微快递
  let bars = [
    {
      key: 'wxpay',
      value: CreditConfig.text,
      url: 'order/credit-pay',
      iconInfo: {
        prefixClass: 'kb-icon',
        value: CreditConfig.icon,
        color: process.env.PLATFORM_ENV == 'alipay' ? '#1677ff' : '#099dff',
      },
    },
    {
      key: 'bind',
      value: '绑定手机号',
      url: 'user/relation',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'mobile',
      },
    },
    {
      key: 'wallet',
      value: '我的钱包',
      url: 'user/wallet',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'wallet',
      },
    },
    {
      key: 'rights-card',
      value: '我的优惠券',
      url: 'order/card',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'rights-card',
      },
    },
    {
      key: 'address',
      value: '常用地址',
      url: 'address',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'gps',
      },
    },
    {
      key: 'price',
      value: '运费比价',
      url: 'user/price',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'price',
      },
    },
    {
      key: 'postage',
      value: '包装费查询',
      url: 'postage',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'goods',
      },
    },
    {
      key: 'message',
      value: '消息中心',
      url: 'user/message',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'message',
      },
    },
    {
      key: 'service',
      value: '客服服务',
      url: 'user/service',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'service-2',
      },
    },
    {
      key: 'yhj_bill',
      value: '商家优寄账单',
      url: 'ws/bill',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'bill',
      },
    },
    {
      key: 'ecode',
      value: '我的快递码',
      url: 'user/ecode',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'ecode',
      },
    },
    {
      key: 'stopArea',
      value: '停发区查询',
      url: 'pages/closedArea/index',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'tfqy',
      },
    },
    {
      key: 'realName',
      value: '实名认证',
      url: 'realnameList',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'realname1',
      },
    },
  ];

  let removeKeys = [];
  if (process.env.PLATFORM_ENV === 'swan') {
    removeKeys = ['wxpay', 'yhj_bill', 'rights-card', 'wallet', 'price', 'stopArea'];
  }
  bars = bars.filter((item) => !removeKeys.includes(item.key));

  return bars;
};

/**
 * @description 我的订单
 * @returns
 */
export const createOrderBars = () => {
  let bars = [
    {
      key: 'o-send',
      value: '我寄出的',
      url: 'order?type=send',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'o-send',
      },
    },
    {
      key: 'o-receive',
      value: '我收到的',
      url: 'order?type=receive',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'o-receive',
      },
    },
    {
      key: 'o-tcjs',
      value: '同城急送/拉货',
      url: 'order?type=tcjs',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'o-tcjs',
      },
    },
  ];
  let removeKeys = [];
  if (process.env.PLATFORM_ENV === 'swan') {
    removeKeys = ['o-tcjs'];
  }
  bars = bars.filter((item) => !removeKeys.includes(item.key));
  return bars;
};
