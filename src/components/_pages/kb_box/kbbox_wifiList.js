/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Input, View } from "@tarojs/components";
import Taro, { useState, useEffect, useCallback } from "@tarojs/taro";
import "./index.scss";

const Index = props => {
  const { onChange } = props;
  const [wifiList, setWifiList] = useState([]);
  const [netData, setNetData] = useState({});
  useEffect(() => {
    stopWifi(getWifiList);
  }, []);
  useEffect(() => {
    onChange("netData", netData);
  }, [netData]);
  const stopWifi = (then = () => {}) => {
    wx.stopWifi({
      success: then
    });
  };
  const getWifiList = useCallback(() => {
    wx.getSystemInfo({
      success(res) {
        const isIOS = res.platform === "ios";
        if (isIOS) {
          Taro.showModal({
            title: "操作提示",
            content:
              "点击将跳转到系统设置,请手动切换到系统设置WiFi列表页,加载到网络列表后再点击左上角返回小程序",
            showCancel: false,
            success(res) {
              if (res.confirm) {
                startWifi();
              }
            }
          });
          return;
        }
        startWifi();
      }
    });
  }, []);
  const startWifi = () => {
    wx.startWifi({
      success: _getWifiList,
      fail(err) {
        console.error(err);
      }
    });
  };
  const _getWifiList = () => {
    wx.getWifiList({
      success: () => {
        wx.onGetWifiList(res => {
          const wifiList = res.wifiList
            .sort((a, b) => b.signalStrength - a.signalStrength)
            .map(wifi => {
              const strength = Math.ceil(wifi.signalStrength * 4);
              return Object.assign(wifi, {
                strength
              });
            })
            .filter(item => item.SSID);
          setWifiList(wifiList);
          onChange("active", true);
          stopWifi();
        });
      },
      fail(err) {
        if (err.errCode == 12005) {
          Taro.kbToast({ text: "无线网络没有打开" });
          stopWifi();
        }
        if (err.errCode == 12006) {
          Taro.kbToast({ text: "手机系统限制，请打开定位功能" });
          stopWifi();
        }
      }
    });
  };
  const onPickerClick = useCallback(() => {
    if (wifiList.length == 0) {
      Taro.showModal({
        title: "系统提示",
        content: "当前Wi-Fi列表为空，是否刷新网络？",
        confirmText: "刷新网络",
        success: res => {
          res.confirm && getWifiList();
        }
      });
    }
  }, [wifiList]);

  const onPickerChange = useCallback(
    event => {
      const { value } = event.detail;
      setNetData({
        ...netData,
        ssid: wifiList[value].SSID
      });
    },
    [wifiList, netData]
  );

  const passWordChange = useCallback(
    event => {
      const { value } = event.detail;
      setNetData({
        ...netData,
        password: value
      });
    },
    [netData]
  );

  return (
    <View className="kbbox_wifiList">
      <Picker
        mode="selector"
        range={wifiList}
        rangeKey="SSID"
        onChange={onPickerChange}
        onClick={onPickerClick}
      >
        <View className="list">
          <View className="title">网络</View>
          <Input
            placeholder={"请选择Wi-Fi(不支持中文Wi-Fi名称)"}
            value={netData.ssid || ""}
            onChange={e => {}}
            className="input"
            placeholderStyle={{ fontSize: "26rpx" }}
            disabled
          ></Input>
        </View>
      </Picker>
      <View className="list border-t">
        <View className="title">密码</View>
        <Input
          placeholder={"请输入密码"}
          value={netData.password || ""}
          onInput={passWordChange}
          className="input"
          placeholderStyle={{ fontSize: "26rpx" }}
        ></Input>
        <View className="refresh" onClick={getWifiList}>
          刷新网络
        </View>
      </View>
    </View>
  );
};

Index.defaultProps = {
  onChange: () => {}
};

export default Index;
