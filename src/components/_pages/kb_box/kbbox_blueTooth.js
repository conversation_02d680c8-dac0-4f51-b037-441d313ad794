/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Input, View } from "@tarojs/components";
import Taro, { useState, useEffect, useCallback, useRef } from "@tarojs/taro";
import { AtIcon, AtButton } from "taro-ui";
import "./index.scss";
const stopBluetoothDevicesDiscovery = (then = () => {}) => {
  wx.stopBluetoothDevicesDiscovery({
    complete: then
  });
  // wx.offBluetoothDeviceFound();
};
const closeBluetoothAdapter = (then = () => {}) => {
  wx.closeBluetoothAdapter({
    complete: then
  });
};

const closeBLEConnection = (deviceId='', then = () => {}) => {
  wx.closeBLEConnection({
    deviceId,
    complete: then
  });
};

const openBluetoothAdapter = (then = () => {}) => {
  wx.openBluetoothAdapter({
    success: then,
    fail: res => {
      if (res.errCode === 10001) {
        Taro.showModal({
          title: "错误",
          content: "error 5001 未找到蓝牙设备, 请打开蓝牙后重试。",
          showCancel: false
        });
        wx.onBluetoothAdapterStateChange(res => {
          res.available && then();
        });
      }
    }
  });
};

const startBluetoothDevicesDiscovery = (then = () => {}) => {
  stopBluetoothDevicesDiscovery(() => {
    wx.startBluetoothDevicesDiscovery({
      allowDuplicatesKey: true,
      services: ["00010000-89BD-43C8-9231-40F6E305F96D"],
      success: res => {
        wx.onBluetoothDeviceFound(res => {
          let list = [];
          res.devices.map(item => {
            const index = list.findIndex(
              iitem => iitem.localName === item.localName
            );
            if (index < 0 && item.localName) {
              list.push({
                ...item,
                guid: item.localName.replace("kbprinter", "")
              });
            }
          });
          then(list);
        });
      },
      fail(res) {
        Taro.showToast({
          title: "error 5002" + res.errMsg || "",
          icon: "none"
        });
      }
    });
  });
};
// ArrayBuffer转16进度
const ab2hex = buffer => {
  const hexArr = Array.prototype.map.call(new Uint8Array(buffer), function(
    bit
  ) {
    return ("00" + bit.toString(16)).slice(-2);
  });
  return hexArr.join("");
};
// 16进度转字符串
const hexCharCodeToStr = hexCharCodeStr => {
  var trimedStr = hexCharCodeStr.trim();
  var rawStr =
    trimedStr.substr(0, 2).toLowerCase() === "0x"
      ? trimedStr.substr(2)
      : trimedStr;
  var len = rawStr.length;
  if (len % 2 !== 0) {
    return "";
  }
  var curCharCode;
  var resultStr = [];
  for (var i = 0; i < len; i = i + 2) {
    curCharCode = parseInt(rawStr.substr(i, 2), 16); // ASCII Code Value

    resultStr.push(String.fromCharCode(curCharCode));
  }
  return resultStr.join("");
};
// 字符串ArrayBuffer
const string2buffer = str => {
  // 首先将字符串转为16进制
  let val = "";
  for (let i = 0; i < str.length; i++) {
    if (val === "") {
      val = str.charCodeAt(i).toString(16);
    } else {
      val += "," + str.charCodeAt(i).toString(16);
    }
  }
  // 将16进制转化为ArrayBuffer
  return new Uint8Array(
    val.match(/[\da-f]{2}/gi).map(function(h) {
      return parseInt(h, 16);
    })
  ).buffer;
};

const bufWifiData = (data = {}) => {
  console.info("122 ====>", data);
  const wifi = {
    cmd: "setWireless",
    data: {
      ssid: data.ssid || "",
      password: data.password || ""
    }
  };
  return string2buffer(JSON.stringify(wifi));
};

const searchTime = 10000; // 搜索时间
const contactTime = 120; // 连接时间 s
const Index = props => {
  const { active, netData = {} } = props;
  const timeoutRef = useRef({});
  const [searchStatus, setSearchStatus] = useState(false);
  const [devicesList, setDevicesList] = useState([]);
  const [searchContent, setSearchContent] = useState("蓝牙正在初始化...");
  const [contactTimeCount, setContactTimeCount] = useState(contactTime);
  const [contactStatus, setContactStatus] = useState("");
  const [failToast, setFailToast] = useState("");

  useEffect(() => {
    active && initBluetooth();
  }, [active]);
  useEffect(() => {
    if (searchStatus) {
      setSearchContent("正在搜索可用设备...");
      setDevicesList([]);
      startBluetoothDevicesDiscovery((list = []) => {
        setSearchContent(`共找到 ${list.length || 0} 台快宝盒子`);
        setDevicesList(list);
      });
      clearTimeout(timeoutRef.current.searchTimeout);
      timeoutRef.current.searchTimeout = setTimeout(() => {
        clearTimeout(timeoutRef.current.searchTimeout);
        setSearchStatus(false);
      }, searchTime);
    } else {
      clearTimeout(timeoutRef.current.searchTimeout);
      stopBluetoothDevicesDiscovery();
      wx.offBluetoothDeviceFound();
      devicesList.length == 0 && setSearchContent("暂无可用设备");
    }
  }, [searchStatus]);

  useEffect(() => {
    if (contactTimeCount && contactStatus && contactStatus != "stop") {
      let dice = "";
      switch (contactStatus) {
        case "contact":
          dice = "正在建立连接...";
          setFailToast("建立连接失败");
          break;
        case "getServices":
          dice = "正在获取信息...";
          setFailToast("未获取到设备蓝牙服务，请重启设备后重试-。");
          break;
        case "write":
          dice = "正在设置盒子...";
          setFailToast("当前连接时间过长，请检查wifi信息是否正确");
          break;
        default:
          break;
      }
      clearTimeout(timeoutRef.current.contactTimeCount);
      timeoutRef.current.contactTimeCount = setTimeout(() => {
        Taro.showLoading({ title: `${dice}  ${contactTimeCount}s` });
        setContactTimeCount(contactTimeCount - 1);
      }, 1000);
    }
    if (contactStatus == "stop" || contactTimeCount == 0) {
      Taro.hideLoading();
      setContactStatus("");
      setContactTimeCount(contactTime);
      clearTimeout(timeoutRef.current.contactTimeCount);
      failToast &&
        Taro.showToast({ title: failToast, icon: "none", duration: 3000 });
    }
  }, [contactStatus, contactTimeCount]);

  const initBluetooth = () => {
    console.info("204 ==>");
    closeBluetoothAdapter(() => {
      openBluetoothAdapter(() => setSearchStatus(true));
    });
  };

  const onRefresh = useCallback(() => {
    console.info("searchStatus 212", searchStatus);
    console.info("active 212", active);
    if (searchStatus) return;
    if (!active) {
      Taro.kbToast({
        text: "初始化未完成，请先设置Wi-Fi网络"
      });
      return;
    }
    setSearchStatus(true);
  }, [searchStatus, active]);

  const onSetBox = useCallback(
    item => {
      console.info("验证wifi信息是否完整", netData);
      console.info("item", item);
      const { ssid, password } = netData;
      if (!ssid || !password) {
        Taro.showToast({
          title: `${!ssid ? "Wi-Fi名称缺失" : "Wi-Fi密码"}缺失`
        });
        return;
      }
      const { deviceId, guid } = item;
      Taro.showLoading({ title: `开始设置...` });
      setSearchStatus(false);
      setContactStatus("contact");
      triggerCreateBLEConnection(deviceId, (data = {}) => {
        onWriteBLECharacteristicValue({ deviceId, guid, ...data });
      });
    },
    [netData]
  );
  const triggerCreateBLEConnection = (deviceId, then = () => {}) => {
    console.info("triggerCreateBLEConnection==>", deviceId);
    closeBLEConnection(deviceId, () => {
      console.info("232===>", deviceId);
      wx.createBLEConnection({
        deviceId,
        success: () => {
          console.info("236===>");
          getBLEDeviceServices(deviceId, then);
        },
        fail(res) {
          if (res.errMsg.includes("already connect")) {
            getBLEDeviceServices(deviceId, then);
          } else if (res.errCode == 10003) {
            setFailToast("建立连接失败，请尝试重启设备后重新连接");
            setContactStatus("stop");
          } else {
            setFailToast("fail 354" + res.errMsg);
            setContactStatus("stop");
          }
        }
      });
    });
  };

  const getBLEDeviceServices = (deviceId, then = () => {}) => {
    console.info("259===>", deviceId);
    setContactStatus("getServices");
    wx.getBLEDeviceServices({
      deviceId,
      success: res => {
        console.info("266===>");
        let serviceData = res.services.filter(
          item =>
            item.isPrimary &&
            item.uuid === "00010000-89BD-43C8-9231-40F6E305F96D"
        )[0];
        var serviceId = serviceData.uuid;
        wx.getBLEDeviceCharacteristics({
          deviceId,
          serviceId,
          success: res => {
            if (!res) {
              return false;
            }
            then({
              serviceId,
              characteristicId: "00010002-89BD-43C8-9231-40F6E305F96D"
            });
          }
        });
      },
      fail(res) {
        setFailToast("fail 393" + res.errMsg);
        setContactStatus("stop");
      }
    });
  };

  const onWriteBLECharacteristicValue = data => {
    const { serviceId, characteristicId, deviceId, guid } = data;
    console.info("303 ==>", data);
    setContactStatus("write");
    // 开启监听写入特征值变化
    wx.notifyBLECharacteristicValueChange({
      deviceId,
      serviceId,
      characteristicId,
      state: true
    });
    console.info("305===>", netData);
    wx.writeBLECharacteristicValue({
      deviceId: deviceId,
      serviceId: serviceId,
      characteristicId,
      value: bufWifiData(netData),
      success: () => {
        // kuaidihelp_AP
        wx.onBLECharacteristicValueChange(res => {
          if (!res) {
            setFailToast(
              "fail 315 配置无线网络失败，请检查网络是否正常或重新输入密码"
            );
            setContactStatus("stop");
            return;
          }

          console.info("res 323====>", res.value);
          var wirelessName = hexCharCodeToStr(ab2hex(res.value));
          var curWifi = netData.ssid;
          console.info("wirelessName 321====>", wirelessName);
          console.info("curWifi 327====>", curWifi);
          // kuaibao666*
          if (wirelessName && curWifi.includes(wirelessName)) {
            setFailToast("设置成功");
            setContactStatus("stop");
            Taro.showModal({
              title: "设置成功",
              content: "设置成功之后引导跳转，调取接口通知后台"
            });
            // that.serve.dialog({
            //   attribute: {
            //     title: "操作成功",
            //     layout: "template_kbBoxTip",
            //     button: [
            //       {
            //         name: "关闭",
            //         action: "hide"
            //       },
            //       {
            //         action: "submit",
            //         name: "确定"
            //       }
            //     ]
            //   },
            //   cb: (evtype, action) => {
            //     if (evtype == "hide") {
            //       this.serve.tools.onNavigator({
            //         url: "index/personal/personal"
            //       });
            //     }
            //   }
            // });
            // that.serve.request({
            //   url: "/api/weixin/mini/cloudPrint/PrinterBox/pushMsg",
            //   isAllToast: false,
            //   data: {
            //     agent_guid: guid
            //   },
            //   success: res => {}
            // });
          } else {
            setFailToast(
              "fail 360 配置无线网络失败，请检查网络是否正常或重新输入密码"
            );
            setContactStatus("stop");
          }
        });
      },
      fail(res) {
        setFailToast("fail 367" + res.errMsg);
        setContactStatus("stop");
      }
    });
  };

  return (
    <View className="kbbox_wifiList">
      <View class="list">
        <View className="title">{searchContent}</View>
        <View
          className={`${searchStatus && "refresh_button"} refreshIcon`}
          onClick={onRefresh}
        >
          <AtIcon prefixClass="kb-icon-xyt" value="refresh" size="20" />
        </View>
      </View>
      {devicesList.map(item => (
        <View key={item.guid} className="list border-t">
          <View className="kbname">{item.localName}</View>
          <View className="setnet" onClick={onSetBox.bind(null, item)}>
            设置网络
          </View>
        </View>
      ))}
    </View>
  );
};

export default Index;
