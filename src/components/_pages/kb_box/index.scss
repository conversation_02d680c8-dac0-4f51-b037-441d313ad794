/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kbbox_wifiList{
    background-color: #fff;
    margin: 0 20rpx;
    .list{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100rpx;
        padding: 0 20rpx;
        .title{
            color: #333;
            font-size: 30rpx;
            margin-right: 40rpx;
            font-weight: bold;
            text-align: left;
        }
        .input{
            flex: 1;
            &::placeholder{
                color: #999 !important;
                font-size: 26rpx !important;
            }
        }
        .refresh{
            color: #39b54a;
            font-size: 30rpx;
            margin-left: 20rpx;
        }
        .refreshIcon{
            color: #39b54a;
        }
        .kbname{
            color: #333;
            font-size: 30rpx;
        }
        .setnet{
            color: #39b54a;
            font-size: 30rpx;
        }
    }
    .border-t{
        border-top: $width-base solid #e6e6e6;
    }
    .refresh_button {
        animation: spinner 1s infinite linear;
    }

    @keyframes spinner {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
}
