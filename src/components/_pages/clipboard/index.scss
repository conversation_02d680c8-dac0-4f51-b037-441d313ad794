/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-clipboard {
  overflow: hidden;
  border-radius: $border-radius-xl;
  &-title {
    height: 110px;
    font-weight: bold;
    font-size: $font-size-xl;
    line-height: 110px;
    text-align: center;
    background: #ebf9ff;
  }
  &-body {
    box-sizing: border-box;
    max-height: 350px;
    padding: $spacing-h-lg 45px;
    &__scrollView {
      height: 350px;
      color: $color-grey-1;
      font-size: $font-size-lg;
    }
    &__card {
      padding: $spacing-h-md;
      font-size: $font-size-lg;
      .title {
        width: 140px;
        color: $color-grey-2;
      }
      .content {
        max-height: 200px;
        overflow: hidden;
      }
    }
  }
  &-ai {
    padding: $spacing-h-md;
    padding-top: 0;
    background: #ebf9ff;
    &_btn {
      width: 240px;
      margin-left: -370px;
      padding-top: 10px;
    }
  }
  &-action {
    margin: $spacing-h-md 0;
    padding: $spacing-h-md;
    &--item {
      flex: 1;
      box-sizing: border-box;
      margin: $spacing-h-md;
      padding: 0 $spacing-h-lg;
      color: $color-white;
      font-size: $font-size-base;
      line-height: 70px;
      text-align: center;
      background: $color-brand;
      border-radius: 50px;
      &__plain {
        color: $color-brand;
        background: transparent;
        border: $width-base solid $color-brand;
      }
      &__h {
        height: 76px;
        margin: $spacing-h-md $spacing-h-xl;
        line-height: 76px;
      }
    }
    &__h {
      display: flex;
      justify-content: space-around;
      .kb-clipboard-action--item {
        width: 260px;
      }
    }
  }
}
