/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbTextarea from '@base/components/textarea';
import { batchAnalysisAddress, labelRedFn } from '@/components/_pages/address/_utils';
import { getEcodeInfo } from '@/components/_pages/ecode/_utils';
import { createByAddressEdit } from '@/components/_pages/order/_utils';
import { jumpToExpressInfo, matchWaybillAndBrand } from '@/components/_pages/query/_utils';
import { useDidShowCom, useUpdate } from '@base/hooks/page';
import { check } from '@base/utils/rules';
import { checkInRouters, debounce, getPage } from '@base/utils/utils';
import { ScrollView, Text, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { useCallback, useEffect, useState } from '@tarojs/taro';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import { AtIcon } from 'taro-ui';
import './index.scss';

const addressAllow = ['order/edit'];
const companyAllow = ['query/index'];
const allowable = [...addressAllow, ...companyAllow]; // 这里的页面允许获取粘贴板

// 根据手机号或单号解析单号信息 或者解析地址信息
function getApiByWord(word) {
  if (check('waybill_num', word).code == 0) {
    return 'no'; // 单号
  }
  if (process.env.MODE_ENV === 'wkd') {
    if (check('wkdTextLink', word).code == 0) {
      return 'ecode'; // 快递码
    }
  }
  return 'address'; // 地址
}

// 检查页面对应type是否允许获取剪贴板
function verifyUserPathByType(type, path) {
  if (type === 'ecode') return true;
  if (type === 'address') {
    return checkInRouters(path, addressAllow);
  }
  return checkInRouters(path, companyAllow);
}

// 创建contents
function createContentsAndButtons(type, list, path) {
  const contents = [];
  const buttons = [];
  const key = type.split('-')[1];
  console.log('key', key);
  const isBatch = list.length > 1 && key !== 'send';
  let n_list = key !== 'send' ? list : [list[0]];
  let action = 'edit';
  if (process.env.MODE_ENV === 'wkd') {
    if (path.includes('ws/order/edit-yhj')) {
      action = 'edit-yhj';
    } else if (path.includes('ws/order/edit')) {
      action = 'edit-ws';
    }
  }

  n_list.map((item) => {
    const { name, mobile, province, city, district, address, note, notice } = item;
    // 组装展示内容
    if (isBatch) {
      let item = `${name} ${mobile} ${province}${city}${district} ${address}`;
      if (note) {
        item = `${item}【备注：${note}】`;
      }
      contents.push(item);
    } else {
      contents.push(
        `姓名：${name}`,
        `电话：${mobile}`,
        `所在区域：${province}-${city}-${district}`,
        `详细地址：${address}`,
      );
      if (note) {
        contents.push(`备注：${note}`);
      }
      if (notice) {
        contents.push(`注意：${notice}`);
      }
    }
  });

  //组装操作按钮
  if (type.includes('address')) {
    switch (key) {
      case 'send':
        buttons.push({
          type: 'send',
          action,
          label: '用于发件地址',
        });
        break;
      case 'receive':
        buttons.push({
          type: 'receive',
          action,
          label: isBatch ? '用于收件地址去批量寄件' : '用于收件地址',
        });
        break;
      default:
        if (!isBatch) {
          if (action === 'edit') {
            buttons.push({
              type: 'send',
              action,
              label: '用于发件地址',
            });
          }
        }
        buttons.push({
          type: 'receive',
          action,
          label: isBatch ? '用于收件地址去批量寄件' : '用于收件地址',
        });
        break;
    }

    if (isBatch && action == 'edit') {
      buttons.push({
        type: 'receive',
        action,
        label: '完善信息',
        typeSuffix: 'modify',
        plain: true,
      });
    }
  } else {
    if (process.env.MODE_ENV === 'wkd') {
      buttons.push(
        { type: 'ecode-save', action, label: '保存到收件地址' },
        { type: 'receive', action, label: '立即寄件' },
      );
    }
  }

  return { contents, buttons };
}

// 识别并处理地址类型信息
function parseAddress(word, opt) {
  let { path = getPage(-1, false).route, single = false, org } = opt || {};
  let type = org || 'address';
  return new Promise((resolve, reject) => {
    batchAnalysisAddress(word, {
      toastLoading: false,
      filter: (item) => {
        const { name, mobile, phone, province_name, city_name, county_name, detail } = item;
        const list = [name, mobile || phone, province_name && city_name && county_name, detail];
        // 只要2项存在即可
        return list.filter((val) => !!val).length >= 2;
      },
    })
      .then((list) => {
        if (single && list && list.length > 1) {
          list = [list[0]];
        }
        const { buttons, contents } = createContentsAndButtons(type, list, path);
        resolve({
          type,
          word,
          list,
          buttons,
          contents,
          title: '是否使用如下地址寄件？',
        });
      })
      .catch(reject);
  });
}

const parseAddressDebounce = debounce(
  (resolve, reject, word, opt) => {
    parseAddress(word, opt).then(resolve).catch(reject);
  },
  1000,
  {
    leading: false,
    trailing: true,
  },
);

const parseAddressDebounceFn = (word, opt) => {
  return new Promise((resolve, reject) => {
    parseAddressDebounce(resolve, reject, word, opt);
  });
};

// 获取剪贴板数据
const getClipboardDataDebounce = debounce((resolve, reject, path, org) => {
  Taro.getClipboardData({
    success() {
      Taro.hideToast();
    },
  })
    .then(({ data: word }) => {
      const wordLength = `${word}`.length;
      const isAddressOrg = !!org && org.includes('address');
      const allowReuse = !!isAddressOrg;
      const orgKey = org && org.split('-')[1] == 'send';
      if (wordLength > 5 && wordLength < 500 && (Taro.usedClipboard !== word || allowReuse)) {
        const type = getApiByWord(word);
        if (!verifyUserPathByType(type, path)) {
          reject(new Error('不符合使用页'));
          return;
        }
        Taro.usedClipboard = word;
        const typeRes = (isAddressOrg && type === 'address') || !org ? type : ''; //地址模式获取粘贴板只拿地址类型word
        switch (typeRes) {
          case 'no': // 单号
            matchWaybillAndBrand(word)
              .then(({ data: list }) => {
                isArray(list) && list.length > 0
                  ? resolve({ type, word, list })
                  : reject(new Error('未识别出有效信息'));
              })
              .catch(reject);
            break;
          case 'address': // 地址
            parseAddress(word, { path, org, single: !!orgKey }).then(resolve).catch(reject);
            break;
          case 'ecode': // 快递码
            if (process.env.MODE_ENV === 'wkd') {
              getEcodeInfo({ content: word }, { toastLoading: false })
                .then((data) => {
                  const list = [data];
                  const { contents, buttons } = createContentsAndButtons(type, list, path);
                  resolve({
                    type,
                    word,
                    list,
                    contents,
                    buttons,
                    title: `识别到快递码${data.express_code ? '(' + data.express_code + ')' : ''}`,
                  });
                })
                .catch(reject);
            }
            break;

          default:
            reject();
            break;
        }
      } else {
        reject(new Error('剪贴板内容不可用：无内容|已使用|长度超出范围'));
      }
    })
    .catch(reject);
});

// 过滤获取方案
const getClipboardDataFilter = (org) => {
  return new Promise((resolve, reject) => {
    const path = getPage(-1, false).route; //获取页面路由
    if (!checkInRouters(path, allowable)) {
      reject(new Error('不允许读取粘贴板'));
      return;
    }
    // 其他系统版本或者下单页面正常执行读取操作
    getClipboardDataDebounce(resolve, reject, path, org);
  });
};

const Index = (props) => {
  const [data, updateData] = useState(null);
  const [isOpened, updateIsOpened] = useState(false);
  const [content, updateContent] = useState();
  const [focus, updateFocus] = useState(false);
  const { loginData = {} } = useSelector((state) => state.global);
  const { mode, actionRef } = props;

  useEffect(() => {
    if (!actionRef || !Object.keys(actionRef).includes('current')) return;
    actionRef.current = { triggerGetData };
  }, [actionRef]);

  const triggerGetData = useCallback(
    (org) => {
      const { logined } = loginData;
      if (!logined) return Promise.reject();
      return getClipboardDataFilter(org)
        .then((data) => {
          const { type, word } = data;
          // console.log("剪切板-格式化数据", data);
          if (type === 'no') {
            Taro.kbModal({
              title: '查询剪贴板中识别的运单号',
              content: word,
              confirmText: '确定',
              onConfirm: () => jumpToExpressInfo({ ...data }),
              className: ['kb-color__green1', 'kb-size__bold'],
              centered: true,
              subscribe: true,
            });
          } else {
            updateData(data);
            updateIsOpened(true);
            updateContent(data.word);
          }
          return Promise.resolve();
        })
        .catch((err) => {
          console.log(err.message);
          return Promise.reject(err);
        });
    },
    [loginData],
  );

  useUpdate(() => {
    if (mode == 'auto') {
      triggerGetData();
    }
  });

  useDidShowCom(() => {
    if (mode == 'auto') {
      triggerGetData();
    }
  });

  // 弹窗关闭
  const handleClose = useCallback(() => updateIsOpened(false));

  // 点击剪贴板按钮
  const handleClick = (item) => {
    handleClose();
    const { list } = data;
    createByAddressEdit(item, list);
  };

  // 智能识别输入框内容
  const onAiAction = (word) => {
    const { type } = data || {};
    updateContent(word);
    if (word && word.length > 5) {
      parseAddressDebounceFn(word, { single: true, org: type })
        .then((data) => {
          updateData(data);
          updateIsOpened(true);
        })
        .catch((err) => console.log(err.message));
    }
  };

  const onAiActionFocus = (ev) => {
    ev && ev.type == 'tap' ? updateFocus(true) : updateFocus(false);
  };

  const { list, type } = data || {};
  let listData = null; //单条数据
  if (list && list.length == 1) {
    listData = list[0];
  }
  let listLen = list && isArray(list) ? list.length : 0;
  let actionCls = classNames('kb-clipboard-action', {
    'kb-clipboard-action__h': listLen <= 1,
  });
  let modalCls = classNames('at-modal', {
    'at-modal--active': isOpened,
  });

  return (
    <View className={modalCls}>
      <View className='at-modal__overlay' onClick={handleClose} />
      <View className='at-modal__container'>
        {isOpened && (
          <View className='kb-clipboard'>
            <View className='kb-clipboard-title'>{data.title}</View>
            <View className='kb-clipboard-content'>
              {type && type.includes('address') && listLen == 1 && (
                <View className='kb-clipboard-ai'>
                  <KbTextarea
                    className='kb-size__base kb-color__greyer'
                    customStyle='background:#fff;'
                    height='150'
                    focus={focus}
                    keep
                    fixed
                    maxLength={-1}
                    placeholder='输入或粘贴文本信息，如：张三1386668888上海市长宁区通协路269号6号楼6楼 '
                    value={content}
                    onChange={onAiAction}
                    onBlur={onAiActionFocus}
                    renderBars={
                      <View
                        className='kb-clipboard-ai_btn kb-color__brand kb-icon-size__sm'
                        onClick={onAiActionFocus}
                        hoverClass='kb-hover-opacity'
                      >
                        <AtIcon
                          prefixClass='kb-icon'
                          value='edit'
                          className='kb-icon-size__sm kb-margin-sm-r'
                        />
                        点击修改粘贴内容
                      </View>
                    }
                  />
                </View>
              )}
              <View className='kb-clipboard-body'>
                {listData ? (
                  <View className='kb-clipboard-body__card'>
                    <View className='at-row kb-margin-sm-b'>
                      <View className='title'>姓名</View>
                      <View className='at-col content'>
                        {listData.name ? listData.name : '---'}
                      </View>
                    </View>
                    <View className='at-row kb-margin-sm-b'>
                      <View className='title'>电话</View>
                      <View className='at-col content'>
                        {listData.mobile ? listData.mobile : '---'}
                      </View>
                    </View>
                    <View className='at-row kb-margin-sm-b'>
                      <View className='title'>所在地区</View>
                      <View className='at-col content'>
                        <Text className={labelRedFn(listData.province_confidence)}>
                          {listData.province}-
                        </Text>
                        <Text className={labelRedFn(listData.city_confidence)}>
                          {listData.city}-
                        </Text>
                        <Text className={labelRedFn(listData.district_confidence)}>
                          {listData.district}
                        </Text>
                      </View>
                    </View>
                    <View className='at-row kb-margin-sm-b'>
                      <View className='title'>详细地址</View>
                      <View className='at-col content'>
                        {listData.address ? listData.address : '---'}
                      </View>
                    </View>
                  </View>
                ) : listLen > 0 ? (
                  <ScrollView scrollY className='kb-clipboard-body__scrollView'>
                    {list.map((item) => {
                      return (
                        <View className='kb-margin-md-b' key={item.name}>
                          {item.name} {item.mobile}{' '}
                          <Text className={labelRedFn(item.province_confidence)}>
                            {item.province}
                          </Text>
                          <Text className={labelRedFn(item.city_confidence)}>{item.city}</Text>
                          <Text className={labelRedFn(item.district_confidence)}>
                            {item.district}
                          </Text>
                          {item.address}
                        </View>
                      );
                    })}
                  </ScrollView>
                ) : null}
              </View>
            </View>
            <View className={actionCls}>
              {data.buttons.map((item) => {
                let actionItemCls = classNames('kb-clipboard-action--item', {
                  'kb-clipboard-action--item__plain': item.plain,
                  'kb-clipboard-action--item__h': !!(!listData && listLen > 1),
                });
                return (
                  <View
                    key={item.type}
                    className={actionItemCls}
                    onClick={handleClick.bind(null, item)}
                    hoverClass='kb-hover'
                  >
                    {item.label}
                  </View>
                );
              })}
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

Index.options = { addGlobalClass: true };
Index.defaultProps = {
  mode: 'auto',
};

export default Index;
