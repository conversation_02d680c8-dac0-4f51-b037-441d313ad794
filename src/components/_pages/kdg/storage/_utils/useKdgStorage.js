/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useUpdate } from '@base/hooks/page';
import Taro, { useState } from '@tarojs/taro';
import { useCabinetWebsocket } from './useCabinetWebsocket';
import request from '@base/utils/request';
import { scanAction } from '@/utils/scan';

export const UNIT_TYPE = {
  hour: '小时',
  day: '天',
};

export const CABINET_ORDER_GOODS = 'CABINET_ORDER_GOODS';

export function useKdgStorage(props) {
  const { dak_id } = props;

  const [pageData, setPageData] = useState({
    dakInfo: {}, // 快递柜信息
    gridCount: {}, // 格口数量信息
    activeGrid: '', // 当前选择的格口
    gridConfig: [], // 格口配置
    agreementStatus: false,
    goods: '文件',
    dak_id: '',
  });

  // 更新信息
  const updatePageData = (params) => {
    setPageData((v) => ({
      ...v,
      ...params,
    }));
  };

  // 接收消息
  const getMessage = (type, data) => {
    switch (type) {
      case 'get_cabinet_count':
        updatePageData({
          gridCount: data || {},
        });
        break;
      default:
        console.info('未知类型');
        break;
    }
  };

  // websocket
  const { sendMessage, updateWebsocketOptions } = useCabinetWebsocket({
    getMessage,
  });

  const getCabinetConfig = (id = pageData.dak_id) => {
    return new Promise((resolve) => {
      request({
        url: '/api/weixin/mini/minpost/CabinetStorage/cabinetCalcFeeRules',
        data: { dak_id: id },
        toastError: true,
        // mastHasMobile: false,
      }).then(({ code, data, msg }) => {
        const { gridConf = [], name, phone, ban, device_id, ...rest } = data;
        let errorMsg = msg;
        if (`${code}` === '0' && gridConf.length) {
          resolve({
            gridConfig: gridConf.map((i) => ({
              ...i,
              index: i.size + '',
            })),
            agreementInfo: ban,
            device_id,
            dakInfo: {
              dak_id,
              inn_name: name,
              inn_phone: phone,
              ...rest,
            },
          });
          return;
        } else {
          if (!gridConf.length) {
            errorMsg = '未配置格口信息，请联系老板';
          }
          if (code != '0') {
            errorMsg = msg;
          }
          Taro.kbModal({
            top: false,
            content: errorMsg,
            className: ['kb-color__green1', 'kb-size__bold'],
            centered: true,
            closeOnClickOverlay: false,
            closable: false,
            onConfirm: () => {
              return Promise.reject();
            },
          });
          updatePageData({
            bindMobile: true,
            errorMsg,
          });
        }
      });
    });
  };

  // init,获取柜子信息，获取格口数量信息
  const initDakInfo = async (id = dak_id || pageData.dak_id) => {
    // 3473684 德沃公司Test01柜
    // 6666773 代管test徐
    // https%3A%2F%2Fkbydy.cn%2Fkdg%3Fdak_id%3D3473684
    const { gridConfig, agreementInfo, device_id, dakInfo } = await getCabinetConfig(id);
    updatePageData({
      dakInfo: dakInfo,
      gridConfig,
      pageReady: true,
      dak_id: id,
      agreementInfo,
      device_id,
      bindMobile: true,
    });
    if (device_id) {
      updateWebsocketOptions({ device_id }, () => {
        sendMessage({
          type: 'get_cabinet_count',
        });
      });
    }
  };

  useUpdate(({ logined, userInfo }) => {
    if (logined) {
      const { mobile } = userInfo || {};
      scanAction()
        .then(({ dak_id }) => {
          updatePageData({
            dak_id,
            mobile,
          });
          initDakInfo(dak_id);
        })
        .catch(() => {
          updatePageData({
            mobile,
          });
          initDakInfo();
        });
    }
  });

  return {
    pageData,
    updatePageData,
  };
}
