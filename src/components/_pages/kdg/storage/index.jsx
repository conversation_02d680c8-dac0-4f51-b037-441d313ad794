/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { useKdgStorage } from './_utils/useKdgStorage';
import KdgStorageGrids from './components/grids';
import KdgStorageFooter from './components/footer';
import KdgStorageHeader from './components/header';
import KdgStorageGoods from './components/goods';
import Taro, { Fragment } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbLoginAuthAndBind from '@base/components/login/authAndBind';
import KdgStorageMobile from './components/mobile';

const KdgStorage = (props) => {
  const { pageData, updatePageData } = useKdgStorage(props);

  const { bindMobile } = pageData;

  return (
    <KbPage
      {...props}
      renderHeader={
        <Fragment>
          {!bindMobile ? (
            <View className='kb-login__auth--box kb-spacing-md-t'>
              <KbLoginAuthAndBind className='kb-button__middle' />
            </View>
          ) : (
            <KdgStorageHeader {...pageData} />
          )}
        </Fragment>
      }
      renderFooter={<KdgStorageFooter {...pageData} updatePageData={updatePageData} />}
    >
      <KdgStorageGrids {...pageData} updatePageData={updatePageData} />
      <KdgStorageGoods {...pageData} updatePageData={updatePageData} />
      <KdgStorageMobile {...pageData} updatePageData={updatePageData} />
    </KbPage>
  );
};

KdgStorage.options = {
  addGlobalClass: true,
};

export default KdgStorage;
