/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import KbLoader from '@base/components/loader';
import Taro, { useMemo } from '@tarojs/taro';
import './index.scss';
import classNames from 'classnames';

const KdgStorageGrids = (props) => {
  const { gridCount, activeGrid, updatePageData, gridConfig = [], bindMobile } = props;

  const columns_ = useMemo(() => {
    const list_ = gridConfig.map((item) => ({
      ...item,
      count: (gridCount[item.index] || 0) * 1,
    }));

    const findCountGrid = (list_.find((i) => i.count > 0) || {}).index;
    updatePageData({
      activeGrid: findCountGrid,
    });
    return list_;
  }, [gridCount, gridConfig]);

  const onHandle = ({ index, count }) => {
    if (!count) {
      Taro.kbToast({
        text: '格口已满,请选择其他格口',
      });
      return;
    }
    if (index == activeGrid) return;
    updatePageData({
      activeGrid: index,
    });
  };

  return (
    <View className='kdg_grids'>
      <View className='kdg_grids__title'>格口大小</View>
      {columns_.length && bindMobile ? (
        <View className='kdg_grids__content'>
          {columns_.map((item) => (
            <View
              key={item.index}
              className={classNames('kdg_grids__item', {
                active_grid: activeGrid == item.size,
                disabled_gird: item.count != undefined && item.count < 1,
              })}
              onClick={onHandle.bind(null, item)}
              hoverClass='kb-hover'
            >
              <View className='kdg_grids__item__title'>{item.size_desc}</View>
              <View>{item.desc}</View>
            </View>
          ))}
        </View>
      ) : (
        <KbLoader loadingText='获取配置信息...' size='small' />
      )}
    </View>
  );
};
KdgStorageGrids.defaultProps = {
  updatePageData: () => {},
};

export default KdgStorageGrids;
