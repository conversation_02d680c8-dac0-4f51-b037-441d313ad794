/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro, { useEffect } from '@tarojs/taro';

import './index.scss';
import { getStorage } from '@base/utils/utils';
import { CABINET_ORDER_GOODS } from '../../_utils/useKdgStorage';

const KdgStorageGoods = (props) => {
  const { goods, updatePageData = () => {} } = props;

  const columns = [
    {
      key: '1',
      title: '文件',
    },
    {
      key: '2',
      title: '手机数码',
    },
    {
      key: '3',
      title: '行李背包',
    },
    {
      key: '4',
      title: '衣物',
    },
    {
      key: '5',
      title: '生活用品',
    },
    {
      key: '6',
      title: '其他',
    },
  ];

  useEffect(() => {
    getStorage({ key: CABINET_ORDER_GOODS }).then((result) => {
      const { data } = result.data;
      if (data) {
        updatePageData({
          goods: data,
        });
      }
    });
  }, []);

  return (
    <View className='kdg_goods'>
      <View className='kdg_goods__title'>物品类型</View>
      <View className='kdg_goods__content'>
        {columns.map((item) => (
          <View
            key={item.key}
            className={`kdg_goods__item ${goods == item.title ? 'active_grid' : ''}`}
            onClick={updatePageData.bind(null, { goods: item.title })}
            hoverClass='kb-hover'
          >
            {item.title}
          </View>
        ))}
      </View>
    </View>
  );
};

export default KdgStorageGoods;
