/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kdg_goods {
  margin: $spacing-v-md;
  padding: 0 $spacing-v-md;
  background-color: $color-white;
  border-radius: $border-radius-md;
  &__title {
    padding: $spacing-v-md * 1.5 0;
    border-bottom: $border-lightest;
  }
  &__content {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-v-md 0;
  }
  &__item {
    display: flex;
    flex-direction: column;
    gap: $spacing-v-sm;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 210px;
    height: 80px;
    color: $color-grey-1;
    font-size: $font-size-lg;
    text-align: center;
    border: $border-lightest;
    border-radius: $border-radius-md;
  }
}
.active_grid {
  color: $color-brand !important;
  background-color: #f2fbfa;
  border-color: $color-brand !important;
}
