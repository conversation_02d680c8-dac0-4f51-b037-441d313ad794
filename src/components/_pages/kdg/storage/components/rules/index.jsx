/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import {
  // AtButton,
  AtFloatLayout,
} from 'taro-ui';
import { useState, Fragment } from '@tarojs/taro';
import './index.scss';
import { UNIT_TYPE } from '../../_utils/useKdgStorage';

const KdgStorageRules = (props) => {
  const { gridConfig = [] } = props;

  const [isOpened, setIsOpened] = useState(false);

  return (
    <Fragment>
      <View className='kb-size__base kb-color__grey' onClick={setIsOpened.bind(null, true)}>
        {'收费规则 >'}
      </View>
      <AtFloatLayout title='收费规则' isOpened={isOpened} onClose={setIsOpened.bind(null, false)}>
        <View className='kdg_s_rules'>
          <View className='kdg_s_rules__content'>
            <View className='kdg_s_rules__content__item border-none'>
              <View className='kdg_s_rules__content__item__label'>格口大小</View>
              <View className='kdg_s_rules__content__item__content'>
                <View>用户支付费用=基础费+超时费</View>
                <View className='kb-size__xs'>未超时则没有超时费，超时不满1小时按1小时计算</View>
              </View>
            </View>
            {gridConfig.map((item) => {
              const { fee } = item;
              const { unit, start, step, num } = fee || {};
              const unit_ = UNIT_TYPE[unit];
              return (
                <View className='kdg_s_rules__content__item' key={item.index}>
                  <View className='kdg_s_rules__content__item__label'>{item.size_desc}</View>
                  <View className='kdg_s_rules__content__item__content kdg_s_rules__content__item__content__flex'>
                    <View className='kdg_s_rules__content__item__content__flex__item'>
                      <View>基础费用：</View>
                      <View>
                        {num}
                        {unit_}内，收费{start}元
                      </View>
                    </View>
                    <View className='kdg_s_rules__content__item__content__flex__item'>
                      <View>超出后：</View>
                      <View>
                        每多1{unit_}，加收{step}元
                      </View>
                      <View>上不封顶</View>
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
          {/* <AtButton circle type='primary' size='small' onClick={setIsOpened.bind(null, false)}>
            我知道了
          </AtButton> */}
        </View>
      </AtFloatLayout>
    </Fragment>
  );
};

KdgStorageRules.options = {
  addGlobalClass: true,
};

export default KdgStorageRules;
