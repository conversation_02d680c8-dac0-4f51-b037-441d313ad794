/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import './index.scss';
import KdgStorageAgreement from '../agreement';
import KdgStorageRules from '../rules';
import { useKdgStorageFooter } from './useKdgStorageFooter';

// dakId=6666710
const KdgStorageFooter = (props) => {
  const { bindMobile } = props;
  const { curPrice, onSubmit } = useKdgStorageFooter(props);

  return (
    <View>
      {bindMobile && <KdgStorageAgreement {...props} />}
      <View className='kdg_s_footer'>
        <View>
          <View className='kdg_s_footer__price'>¥ {curPrice || '--'}</View>
          {bindMobile && <KdgStorageRules {...props} />}
        </View>
        <View className='kdg_s_footer__button'>
          <AtButton type='primary' size='small' onClick={onSubmit}>
            立即下单
          </AtButton>
        </View>
      </View>
    </View>
  );
};

KdgStorageFooter.options = {
  addGlobalClass: true,
};

export default KdgStorageFooter;
