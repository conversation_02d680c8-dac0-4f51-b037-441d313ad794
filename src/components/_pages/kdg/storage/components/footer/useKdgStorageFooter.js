/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { setStorage } from '@base/utils/utils';
import Taro, { useMemo } from '@tarojs/taro';
import { CABINET_ORDER_GOODS } from '../../_utils/useKdgStorage';

export function useKdgStorageFooter(props) {
  const {
    activeGrid,
    gridConfig = [],
    agreementStatus,
    goods,
    dakInfo,
    device_id,
    bindMobile,
    errorMsg,
    mobile,
  } = props;

  const { curPrice, curStorageTime, fee, grid_desc } = useMemo(() => {
    if (!activeGrid || !gridConfig.length) {
      return {};
    }
    const { fee = {}, size_desc: grid_desc } = gridConfig.find((i) => i.index == activeGrid) || {};
    const time = fee.unit == 'hour' ? fee.num * 60 * 60 : fee.num * 60 * 60 * 24; // 秒

    return {
      curPrice: fee.start + '',
      curStorageTime: time,
      fee,
      grid_desc,
    };
  }, [activeGrid, gridConfig]);

  const checkParams = () => {
    return new Promise((resolve, reject) => {
      let msg = '';
      if (!agreementStatus) {
        msg = '请先阅读并同意储物规则';
      }

      if (!activeGrid) {
        msg = '格口信息为空';
      }
      if (!bindMobile) {
        msg = '请先绑定手机号';
      }
      if (errorMsg) {
        msg = errorMsg;
      }

      if (msg) {
        Taro.kbToast({
          text: msg,
        });
        reject();
        return;
      }

      // 'cabinet_id' : 柜子ID
      // 'goods'      : 物品名字,
      // 'storage_duration' : 存储时长 100,
      // 'grid_type'       : 格口类型标识 1,

      resolve({
        goods,
        storage_duration: curStorageTime,
        cabinet_id: dakInfo.id,
        grid_type: activeGrid,
        grid_desc,
        mobile,
      });
    });
  };

  const onSubmit = async () => {
    const params = await checkParams();
    request({
      url: '/api/weixin/mini/minpost/CabinetStorage/createOrder',
      data: {
        data: {
          order_info: params,
        },
      },
      toastError: true,
      onThen: ({ code, data }) => {
        if (`${code}` === '0') {
          setStorage({
            key: CABINET_ORDER_GOODS,
            data: goods,
          });
          Taro.navigator({
            url: 'kdg/storage/pay',
            options: {
              fee: JSON.stringify(fee),
              curStorageTime,
              curPrice,
              device_id,
              dakInfo: JSON.stringify(dakInfo),
              order_id: data.order_id,
              size: activeGrid,
              mobile,
            },
          });
        }
      },
    });
  };

  return {
    curPrice,
    onSubmit,
  };
}
