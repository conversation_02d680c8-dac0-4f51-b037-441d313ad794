/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect } from '@tarojs/taro';

export function useKdgStorageAgreement(props) {
  const { agreementStatus, updatePageData, agreementInfo = [] } = props;

  const onChange = () => {
    updatePageData({
      agreementStatus: !agreementStatus,
    });
  };

  const onOpenAgreement = () => {
    if (!agreementInfo.length) {
      Taro.kbToast({
        text: '未配置信息',
      });
      return;
    }

    Taro.kbModal({
      top: false,
      title: [
        {
          text: '禁存物品说明',
          className: 'kb-color__green1',
        },
      ],
      content: agreementInfo,
      confirmText: '知晓并同意',
      closable: false,
      onConfirm: () => {
        updatePageData({
          agreementStatus: true,
        });
      },
      className: ['kb-text__left'],
      centered: true,
    });
  };

  useEffect(() => {
    if (agreementInfo.length) {
      onOpenAgreement();
    }
  }, [agreementInfo.length]);

  return {
    agreementStatus,
    onChange,
    onOpenAgreement,
  };
}
