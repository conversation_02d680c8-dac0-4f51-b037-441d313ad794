/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbRadio from '@base/components/radio';
import { View } from '@tarojs/components';
import './index.scss';
import { useKdgStorageAgreement } from './useKdgStorageAgreement';

const KdgStorageAgreement = (props) => {
  const { agreementStatus, onChange, onOpenAgreement } = useKdgStorageAgreement(props);

  return (
    <View className='kdg_s_Agreement'>
      <KbRadio checked={agreementStatus} onChange={onChange} isCancel label='我已阅读并同意' />
      <View className='kb-color__brand' onClick={onOpenAgreement}>
        {'<<储物规则>>'}
      </View>
    </View>
  );
};

KdgStorageAgreement.options = {
  addGlobalClass: true,
};

export default KdgStorageAgreement;
