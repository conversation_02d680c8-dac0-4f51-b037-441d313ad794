/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import KbLoader from '@base/components/loader';

import './index.scss';

const KdgStorageHeader = (props) => {
  const { dakInfo, errorMsg } = props;

  const { inn_name } = dakInfo || {};

  return (
    <View className='kdg_s_header'>
      <View className='kdg_s_header__page'>
        {errorMsg ? (
          <View className='kdg_s_header__page__error'>{errorMsg}</View>
        ) : inn_name ? (
          <Fragment>
            <Image
              className='kdg_s_header__page__image'
              src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_cabinet.png?v=20240712'
            />
            <View className='kdg_s_header__page__name'>{inn_name}</View>
          </Fragment>
        ) : (
          <KbLoader size='small' loadingText='正在获取快递柜信息' />
        )}
      </View>
    </View>
  );
};

export default KdgStorageHeader;
