/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kdg_s_header {
  padding: $spacing-v-md;
  padding-bottom: 0;
  &__page {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: flex-start;
    height: 100px;
    padding: 0 $spacing-v-md;
    background-color: $color-white;
    border-radius: $border-radius-md;
    &__image {
      width: 60px;
      height: 60px;
    }
    &__name {
      color: $color-grey-0;
      font-size: $font-size-xl;
    }
    &__error {
      color: $color-grey-1;
      font-size: $font-size-base2;
    }
  }
}
