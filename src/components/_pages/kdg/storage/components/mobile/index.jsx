/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import { check } from '@base/utils/rules';

import './index.scss';

const KdgStorageMobile = (props) => {
  const { mobile, updatePageData } = props;
  const onOpenModal = () => {
    Taro.kbModal({
      top: false,
      title: '设置取件手机号',
      template: [
        {
          tag: 'at-input',
          placeholder: '请输入手机号码',
          value: mobile,
          circle: true,
          border: false,
          maxLength: 11,
          name: 'mobile',
        },
      ],
      onConfirm: (e, close) => {
        const {
          data: { mobile: mobile_ },
        } = e;
        const { code, msg } = check('phone_star', mobile_);
        if (code == 0) {
          updatePageData({
            mobile: mobile_,
          });
          close();
        } else {
          Taro.kbToast({
            text: msg,
          });
        }
        return true;
      },
    });
  };

  return (
    <View className='kdg_mobile'>
      <View className='kdg_mobile__title'>取件手机号</View>
      <View className='kdg_mobile__content' onClick={onOpenModal} hoverClass='kb-hover'>
        <Text>{mobile}</Text>
        <AtIcon prefixClass='kb-icon' value='edit' className='kb-size__lg kb-color__greyer' />
      </View>
    </View>
  );
};

KdgStorageMobile.options = {
  addGlobalClass: true,
};

export default KdgStorageMobile;
