/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-pickupResult {
  &-box {
    box-sizing: border-box;
    margin: $spacing-h-md;
    padding: 50px 20px;
    background: #fff;
    border-radius: 8px;
    &__options {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 30px;
      &-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 300px;
        height: 80px;
        margin-right: $spacing-h-md;
        color: $color-brand;
        font-size: 32px;
        background: #fff;
        border: $width-base solid $color-brand;
        border-radius: 40px;
        &:last-child {
          margin-right: 0;
        }
      }
      .btn-primary {
        color: #fff;
        background: $color-brand;
      }
    }
  }
}
