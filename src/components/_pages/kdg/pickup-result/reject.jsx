/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import { get } from '~/actions/brands';
import { connect } from '@tarojs/redux';
import CabinetOpen from '../cabinetOpen';
import CabinetOpenRejectionContent from '../cabinetOpen/components/rejection/content';
import { useReject } from '../cabinetOpen/components/rejection/utils';
import './index.scss';

const KbRejectResult = (props) => {
  const { data = {}, mode, dakInfo } = props;
  const {
    step,
    checked,
    brands,
    status,
    rejectionInfo,
    setStep,
    handleChoose,
    handleCancel,
    handleConfirm,
    handleOpen,
    cabinetPickupRef,
    updateRejectionData,
  } = useReject(props);

  const cancelText = step == 1 ? (status ? '重开柜门' : '再次开柜') : step == 2 ? '取消' : '';
  const confirmText = step == 1 ? (status ? '投柜完成' : '继续取件') : step == 2 ? '继续退回' : '';

  return (
    <View className='kb-pickupResult-box'>
      <CabinetOpenRejectionContent
        rejectionInfo={rejectionInfo}
        step={step}
        brands={brands}
        checked={checked}
        setStep={setStep}
        handleChoose={handleChoose}
        handleOpen={handleOpen}
        isPage
      />

      <View className='kb-pickupResult-box__options'>
        {cancelText && (
          <View
            className='kb-pickupResult-box__options-btn'
            onClick={handleCancel}
            hoverClass='kb-hover-opacity'
          >
            {cancelText}
          </View>
        )}
        {confirmText && (
          <View
            className='kb-pickupResult-box__options-btn btn-primary'
            onClick={handleConfirm}
            hoverClass='kb-hover-opacity'
          >
            {confirmText}
          </View>
        )}
      </View>
      <CabinetOpen
        mode={mode}
        dakInfo={dakInfo}
        data={data}
        actionRef={cabinetPickupRef}
        updateRejectionData={updateRejectionData}
        // updateList={updateList}
      />
    </View>
  );
};

KbRejectResult.options = {
  addGlobalClass: true,
};

export default connect(({ global }) => ({ brands: global.brands }), {
  dispatchGet: get,
})(KbRejectResult);
