/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useRef } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { REFRESH_KEY_APPOINTMENT_LIST, refreshControl } from '~/utils/refresh-control';
import CabinetOpenSuccessContent from '../cabinetOpen/components/success/content';
import CabinetOpen from '../cabinetOpen';
import './index.scss';

const KbPickupResult = (props) => {
  const { openSuccessInfo, baseData, data = {}, mode, dakInfo } = props;
  const { otherCabinetPackage } = openSuccessInfo || {};
  const cabinetPickupRef = useRef({});

  const handleContinue = () => {
    refreshControl(REFRESH_KEY_APPOINTMENT_LIST);
    cabinetPickupRef.current.handleContinue();
  };

  const handleOpenCabinet = () => {
    refreshControl(REFRESH_KEY_APPOINTMENT_LIST);
    cabinetPickupRef.current.handleOpenCabinet({
      ...baseData,
      second_open: true,
      ...(otherCabinetPackage
        ? {
            device_id: otherCabinetPackage.device_id,
          }
        : {}),
      mode,
    });
  };

  return (
    <View className='kb-pickupResult-box'>
      <View className='kb-cabinetOpenModal'>
        <CabinetOpenSuccessContent openSuccessInfo={openSuccessInfo} baseData={baseData} />
      </View>
      <View className='kb-pickupResult-box__options'>
        <View
          className='kb-pickupResult-box__options-btn'
          onClick={handleOpenCabinet}
          hoverClass='kb-hover-opacity'
        >
          再次开柜
        </View>
        <View
          className='kb-pickupResult-box__options-btn btn-primary'
          onClick={handleContinue}
          hoverClass='kb-hover-opacity'
        >
          继续取件
        </View>
      </View>
      <CabinetOpen
        mode={mode}
        dakInfo={dakInfo}
        data={data}
        actionRef={cabinetPickupRef}
        // updateList={updateList}
      />
    </View>
  );
};

KbPickupResult.options = {
  addGlobalClass: true,
};

export default KbPickupResult;
