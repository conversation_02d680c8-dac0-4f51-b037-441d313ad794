/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import { useCabinetWebsocket } from '../storage/_utils/useCabinetWebsocket';
import { useUpdate } from '@base/hooks/page';
import { makePhoneCall } from '@base/utils/utils';
import { padZero } from '../_utils';
import { cancelCabinetOrder } from '../../order/detail-cabinet/_utils/utils';
import { useSelector } from '@tarojs/redux';

export function useKdgStorageResult(props) {
  const { order_id, device_id, size, dakInfo = {} } = props || {};
  const { inn_phone, id: ecId, inn_name } = JSON.parse(dakInfo || '{}');

  const { loginData = {} } = useSelector((state) => state.global);
  const { userInfo = {} } = loginData || {};
  const mobile = props.mobile || userInfo.mobile || '';

  const [girdInfo, setGirdInfo] = useState({});
  const [isReadyComplete, setIsReadyComplete] = useState(false); // 上报状态

  // 取消订单
  const onCancelOrder = async () => {
    const status = await cancelCabinetOrder(order_id);
    if (status) {
      Taro.navigator();
    }
  };

  // 接收socket回执
  const getMessage = (type, data) => {
    switch (type) {
      case 'customer_open_window_receipt':
        setGirdInfo({
          ...data,
          girdCode: data.number || padZero(data.cabinet_code) + padZero(data.grid_code),
        });
        break;
      case 'customer_post_goods_exception':
        if (data.message) {
          Taro.kbToast({
            text: data.message,
          });
        }
        break;
      // 取消成功
      case 'customer_cancel_post_success':
        onCancelOrder();
        break;
      // 取消异常
      case 'customer_cancel_post_fail':
        Taro.kbToast({
          text: data.message,
        });
        break;
      default:
        break;
    }
  };

  const { sendMessage, updateWebsocketOptions } = useCabinetWebsocket({
    getMessage,
  });

  // 发送信息整合
  const sendCabinetMessage = (params) => {
    const { type, data = {} } = params;
    updateWebsocketOptions(
      {
        device_id,
      },
      () => {
        const params = {
          operate_type: 'storage_goods',
          size: size,
          ecId: ecId,
          waybill_no: order_id,
          ...data,
        };
        if (mobile) {
          params.phone = mobile;
        }
        sendMessage({
          type: type,
          data: params,
        });
      },
    );
  };

  // 开柜
  const openCabinet = () => {
    sendCabinetMessage({
      type: 'post_deliver_open',
    });
  };

  useUpdate(() => {
    openCabinet();
  });

  // 存柜完成上报
  const onComplete = () => {
    return new Promise((resolve) => {
      sendCabinetMessage({
        type: 'post_deliver_complete',
        data: {
          gridId: girdInfo.gridId,
        },
      });
      setIsReadyComplete(true);
      setTimeout(() => {
        resolve();
      }, 1500);
    });
  };

  // 跳转订单详情
  const onNavigatorToDetail = async () => {
    await onComplete();
    Taro.navigator({
      url: 'order/detail-cabinet',
      target: 'self',
      options: {
        order_id,
      },
    });
  };

  // 返回
  const onBack = async () => {
    await onComplete();
    Taro.navigator();
  };

  // 重新开门
  const onReopenCabinet = () => {
    // 重新开门没有回传，直接展示成功
    Taro.kbToast({
      text: '重新开门成功',
    });
    sendCabinetMessage({
      type: 'post_deliver_reopen',
    });
  };

  // 取消订单时释放格口
  const onCancel = () => {
    sendCabinetMessage({
      type: 'post_deliver_cancel',
      data: {
        have_package: '1',
        gridId: girdInfo.gridId,
      },
    });
  };

  const onConnect = () => {
    if (inn_phone) {
      makePhoneCall(inn_phone);
    } else {
      Taro.kbToast({
        text: '未配置信息',
      });
    }
  };

  useEffect(() => {
    return () => {
      console.info('走之前要上报======>', isReadyComplete);
      if (!isReadyComplete) {
        // onComplete();
      }
    };
  }, [isReadyComplete]);

  return {
    mobile,
    inn_name,
    girdInfo,
    onConnect,
    onReopenCabinet,
    onCancel,
    onBack,
    onNavigatorToDetail,
  };
}
