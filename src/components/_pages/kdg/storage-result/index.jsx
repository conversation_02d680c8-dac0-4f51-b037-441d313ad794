/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { Text, View } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';

import './index.scss';
import KdgStorageResultHandleModal from './handleModal';
import { useKdgStorageResult } from './useKdgStorageResult';
import KbLoader from '@base/components/loader';

const KdgStorageResult = (props) => {
  const {
    mobile,
    inn_name,
    girdInfo,
    onNavigatorToDetail,
    onConnect,
    onReopenCabinet,
    onCancel,
    onBack,
  } = useKdgStorageResult(props);

  const { girdCode } = girdInfo || {};

  return (
    <KbPage
      {...props}
      navProps={{
        title: '提交结果',
        hideIcon: true,
      }}
      renderNavLeft={
        <View hoverClass='kb-hover' onClick={onBack}>
          <AtIcon
            prefixClass='kb-icon'
            value='arrow'
            className='kb-color__white kb-icon-size__md kb-icon__direction-left'
          />
        </View>
      }
    >
      <View className='kbStoResult'>
        <View className='kbStoResult__item'>
          <AtIcon value='success' prefixClass='kb-icon' className='kb-color__brand' size={60} />
          {!girdCode ? (
            <KbLoader loadingText='正在开柜...' size='small' />
          ) : (
            <View className='kbStoResult__item__gird'>
              <Text>柜门已开</Text>
              <Text className='kb-color__brand'>（{girdCode}格口）</Text>
            </View>
          )}
          <View className='kbStoResult__item__tips'>将包裹放入柜中后，请关柜门</View>
          <View className='kbStoResult__item__mobile'>系统已将取件短信发送至手机号：{mobile}</View>
          <View className='kbStoResult__item__action'>
            <View className='kbStoResult__item__action__item'>
              <AtButton type='secondary' circle size='normal' onClick={onBack}>
                返回
              </AtButton>
            </View>
            <View className='kbStoResult__item__action__item'>
              <AtButton type='primary' circle onClick={onNavigatorToDetail}>
                查看订单
              </AtButton>
            </View>
          </View>
          <KdgStorageResultHandleModal
            inn_name={inn_name}
            girdInfo={girdInfo}
            onConnect={onConnect}
            onReopenCabinet={onReopenCabinet}
            onCancel={onCancel}
          />
        </View>
      </View>
    </KbPage>
  );
};

KdgStorageResult.options = {
  addGlobalClass: true,
};

export default KdgStorageResult;
