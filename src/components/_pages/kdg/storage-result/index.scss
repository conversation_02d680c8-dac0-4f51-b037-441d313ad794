/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kbStoResult {
  padding: $spacing-v-md;
  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-v-md * 3 $spacing-v-md $spacing-v-md * 2;
    background-color: $color-white;
    border-radius: $border-radius-md;
    &__gird {
      margin-top: $spacing-v-md;
      color: $color-grey-0;
      font-size: $font-size-xl;
    }
    &__tips {
      color: $color-grey-2;
      font-size: $font-size-base2;
    }
    &__mobile {
      width: 100%;
      height: 100px;
      margin: $spacing-v-md * 2 0;
      color: $color-grey-1;
      font-size: $font-size-base2;
      line-height: 100px;
      text-align: center;
      background-color: #f5f5f5;
      border-radius: $border-radius-sm;
    }
  }
}
