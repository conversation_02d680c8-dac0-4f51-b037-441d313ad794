/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb_sto_handle {
  position: absolute;
  top: $spacing-v-md;
  right: 0;
  height: 60px;
  padding: 0 $spacing-v-md;
  color: #ff6000;
  font-size: $font-size-base2;
  line-height: 60px;
  background-color: #fff5cc;
  border-radius: 30px 0 0 30px;
}

.kb_handleModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &__dakName {
    color: $color-grey-0;
    font-size: $font-size-lg;
  }
  &__grid {
    margin-top: $spacing-v-sm;
    margin-bottom: $spacing-v-md;
    color: $color-brand;
    font-size: $font-size-xl;
  }
  &__action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    height: 100px;
    margin-top: $spacing-v-md;
    padding: 0 $spacing-v-md;
    color: $color-grey-0;
    font-size: $font-size-base2;
    background-color: #f5f5f5;
    border-radius: $border-radius-md;
    &__button {
      width: 200px;
      height: 60px;
      color: $color-white;
      font-size: $font-size-lg;
      line-height: 60px;
      text-align: center;
      background-color: $color-brand;
      border-radius: 30px;
    }
  }
}
