/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import KbModal from '@base/components/modal';

import './index.scss';
import { useKdgStorageResultHandleModal } from './useKdgStorageResultHandleModal';

const KdgStorageResultHandleModal = (props) => {
  const { girdInfo, inn_name } = props;
  const { girdCode } = girdInfo;
  const { columns, isOpened, onChangeModalStatus } = useKdgStorageResultHandleModal(props);

  return (
    <View>
      <View className='kb_sto_handle' hoverClass='kb-hover' onClick={onChangeModalStatus}>
        柜门问题? 点此
      </View>
      <KbModal
        top={false}
        closable={false}
        confirmText=''
        onClose={onChangeModalStatus}
        isOpened={isOpened}
        action={[
          {
            label: '关闭',
            key: 'cancel',
            onHandle: onChangeModalStatus,
          },
        ]}
      >
        <View className='kb_handleModal'>
          <View className='kb_handleModal__dakName'>{inn_name}</View>
          <View className='kb_handleModal__grid'>{girdCode}格口</View>
          {columns.map((item) => (
            <View key={item.key} className='kb_handleModal__action'>
              <View>{item.label}</View>
              <View
                onClick={item.onHandle}
                hoverClass='kb-hover'
                className='kb_handleModal__action__button'
              >
                {item.buttonText}
              </View>
            </View>
          ))}
        </View>
      </KbModal>
    </View>
  );
};

KdgStorageResultHandleModal.options = {
  addGlobalClass: true,
};

export default KdgStorageResultHandleModal;
