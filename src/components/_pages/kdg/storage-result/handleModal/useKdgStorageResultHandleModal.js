/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
// import { useCabinetWebsocket } from '../../storage/_utils/useCabinetWebsocket';

export function useKdgStorageResultHandleModal(props) {
  const { onConnect, onReopenCabinet, onCancel } = props;

  const [isOpened, setIsOpened] = useState(false);
  const onChangeModalStatus = () => setIsOpened((v) => !v);

  // 重新开贵
  const onReopen = () => {
    onReopenCabinet();
    setIsOpened(false);
  };

  // 更换格口方法
  const onChangeGrid = () => {
    onChangeModalStatus();
    Taro.kbModal({
      top: false,
      closable: false,
      title: '温馨提示',
      content: '更换格口，本单将自动取消，支付费用原路返还，重新提交订单，确认是否更换？',
      cancelText: '取消',
      onConfirm: () => {
        onCancel();
      },
    });
  };

  const columns = [
    {
      label: '柜门未开',
      buttonText: '重开柜门',
      key: 'reOpen',
      onHandle: onReopen,
    },
    {
      label: '格口不合适',
      buttonText: '更换格口',
      key: 'cancel',
      onHandle: onChangeGrid,
    },
    {
      label: '柜门故障，远程开门',
      buttonText: '联系老板',
      key: 'connect',
      onHandle: onConnect,
    },
  ];

  return {
    isOpened,
    columns,
    onChangeModalStatus,
  };
}
