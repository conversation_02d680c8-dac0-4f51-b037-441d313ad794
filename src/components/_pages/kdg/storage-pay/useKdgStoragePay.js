/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';
import { payCabinetOrder } from '../../order/detail-cabinet/_utils/utils';

export function useKdgStoragePay(props) {
  const { curPrice, fee, curStorageTime, order_id, dakInfo, device_id, size, mobile } = props;

  const {
    step,
    unit,
    free: isFree,
  } = useMemo(() => {
    const { step, unit, start } = fee ? JSON.parse(fee) : {};
    const free = start * 1 == 0;
    return {
      step,
      unit,
      free,
    };
  }, [fee]);

  const onPay = async () => {
    if (!isFree) {
      await payCabinetOrder(order_id);
    }
    Taro.navigator({
      url: 'kdg/storage/result',
      target: 'self',
      options: {
        dakInfo,
        device_id,
        order_id,
        size,
        mobile,
      },
    });
  };

  return {
    curPrice,
    curStorageTime,
    step,
    unit,
    isFree,
    onPay,
  };
}
