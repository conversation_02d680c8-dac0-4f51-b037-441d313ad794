/* eslint-disable import/first */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react-hooks/exhaustive-deps */
import Taro, { useMemo, useState, useEffect, useRef } from '@tarojs/taro';
import { noop } from '@base/utils/utils';
import { formatSecondDate } from '@/components/_pages/order/detail-cabinet/_utils/utils';
import { requestPayment } from '~/utils/qy';
import { getPackageInfo } from '../../_utils';
import { useCabinetWebsocket } from '../../storage/_utils/useCabinetWebsocket';
import { getBatchPaySign, handleCabinetPay } from './utils';
import isFunction from 'lodash/isFunction';
import { OPENEDPACKAGE, CABINET_SUB_KEY } from '~/components/_pages/pickup/_utils';
import request from '~base/utils/request';

export function useCabinetOpen(props) {
  const {
    type = 'click', // input | storage | click
    data = {},
    dakInfo = data,
    cabinet_info = dakInfo.cabinet_info || {},
    device_id,
    actionRef,
    handleOperate = noop,
    updateList = noop,
    handleRemove = noop,
    handleOpenSuccess = noop,
    handleNavigatorPay,
    isBatchPay,
    mode,
    ...restProps
  } = props;

  // 页面中所有数据
  const [pageData, setPageData] = useState({
    currentVisible: '', // 当前打开的弹窗,此字段控制弹窗显示 confirm | success | shelf
    curPackageInfo: {}, // 当前包裹信息
    openSuccessInfo: {}, // 开柜成功后的格口信息
    shelfPackageInfo: {}, // 货架包裹信息
    interceptInfo: {}, // 拦截信息
    rejectionInfo: {}, // 拒收信息
  });

  const out_intercept = useRef(); // 出库拦截，1 需要走check指令
  const batchPayParams = useRef(); // 批量支付参数

  // 添加ref存储最新值
  const pageRef = useRef({});
  const latestBaseDataRef = useRef();
  const latestDataRef = useRef();

  const otherCabinetPackageRef = useRef(); // 其他代管柜子信息

  // 更新页面数据
  const updatePageData = (params) => {
    setPageData((v) => ({
      ...v,
      ...params,
    }));
  };

  // 格式化当前驿站数据，统一驿站名称，取件码，格口，柜子等信息。
  const baseData = useMemo(() => {
    const inn_name = data.inn_name || data.dakName || dakInfo.dakName || '';
    const deviceId = device_id || data.device_id || cabinet_info.device_id || '';
    const pickup_code = data.pickup_code || data.pickupCode || '';
    const isStorage = data.source == 'storage'; // 是否为暂存柜包裹
    const waybill_no = data.waybill_no || data.order_id;

    return {
      inn_name, // 驿站名称
      device_id: deviceId, // 柜子信息
      pickup_code, // 取件码
      suspected: data.suspected || '', // 是否为疑似包裹
      batch_no: data.batch_no || '',
      isStorage,
      waybill_no,
      order_id: waybill_no,
      enable_cross_pick: data.enable_cross_pick == '1',
    };
  }, [data, cabinet_info, device_id]);

  // 更新ref中的最新值
  useEffect(() => {
    latestBaseDataRef.current = baseData;
  }, [baseData]);

  useEffect(() => {
    latestDataRef.current = data;
  }, [data]);

  // message方法回调
  const getMessage = (type, result, resultMsg) => {
    console.info('getMessage====>58', type, result);
    // 使用ref获取最新值
    const currentBaseData = latestBaseDataRef.current;

    switch (type) {
      case 'customer_pickup_success':
        console.log('柜门已开==>');

        onPickSuccess(result);
        handleOpenSuccess(result);
        break;

      case 'customer_pickup_out_of_date_payoff':
        if (isFunction(handleNavigatorPay)) {
          navigatorPay();
          return;
        }
        if (batchPayParams.current) {
          batchPay(batchPayParams.current);
          return;
        }
        Taro.kbToast({ text: '超时待支付' });
        handleCabinetPay(result.timeoutInfo.payData).then(() => {
          setTimeout(() => {
            console.log('------支付完成', currentBaseData);
            handlePayed(currentBaseData);
            // handleOpenCabinet({ ...currentBaseData, second_open: true });
          }, 1000);
        });
        // 支付功能挪走了
        break;

      // 出库拦截
      case 'customer_pickup_intercept_check':
        const { result: interceptInfo = {} } = result || {};
        const { code } = interceptInfo;
        out_intercept.current = null;
        if (code == 0) {
          // 所有支付都走customer_pickup_out_of_date_payoff响应
          // if (isFunction(handleNavigatorPay)) {
          //   navigatorPay();
          //   return;
          // }
          // if (batchPayParams.current) {
          //   batchPay(batchPayParams.current);
          //   return;
          // }
          handleOpenCabinet();
        } else if (code == 10000 || code == 10001) {
          updatePageData({
            currentVisible: 'intercept',
            interceptInfo: {
              ...interceptInfo,
              batchPay: batchPayParams.current,
              handleNavigatorPay: isBatchPay ? null : navigatorPay,
            },
          });
        } else {
          Taro.kbToast({ text: interceptInfo.msg || '操作失败' });
        }
        break;

      // 获取格口包裹列表
      // 拒收确认
      case 'customer_pickup_get_waybill_list':
        // ^_^
        // Tower 任务: 快递柜部分业务设置功能会影响到小程序页面展示 ( https://tower.im/teams/258300/todos/114474 )
        if (isFunction(props.updateRejectionData)) {
          props.updateRejectionData({ ...result });
        } else {
          updatePageData({
            currentVisible: 'rejection',
            rejectionInfo: { ...pageData.rejectionInfo, ...result },
          });
        }
        break;
      case 'customer_rejection_handle':
        if (result && result.status) {
          if (isFunction(props.updateRejectionData)) {
            props.updateRejectionData({ ...result });
          } else {
            updatePageData({
              currentVisible: 'rejection',
              rejectionInfo: { ...pageData.rejectionInfo, ...result },
            });
          }
        } else {
          Taro.kbToast({ text: result.message || '操作失败' });
        }
        break;

      // 柜子查询不到该件
      case 'customer_pickup_fail':
      case 'customer_pickup_out_of_date':
        const {
          shelfPackageList = [],
          message,
          lightBarVisible,
          otherCabinetPackage,
        } = result || {};

        // 如果是货架包裹，就不用toast,会有弹窗接管
        const isShelfPackage = type == 'customer_pickup_fail' && !!shelfPackageList.length;
        if (isShelfPackage) {
          updatePageData({
            currentVisible: 'shelf',
            shelfPackageInfo: {
              message,
              lightBarVisible,
              waybill: shelfPackageList[0].waybill_no,
              dak_id: shelfPackageList[0].dak_id,
            },
          });
        } else if (otherCabinetPackage && otherCabinetPackage.enable_cross_pick == '1') {
          // 其他代管柜子 手动触发开柜
          handleOpenCabinet({
            ...currentBaseData,
            device_id: otherCabinetPackage.device_id,
          });
          otherCabinetPackageRef.current = otherCabinetPackage;
        } else {
          otherCabinetPackageRef.current = null;
          Taro.kbToast({ text: result.message || '操作失败' });
        }
        break;

      default:
        console.info('未知操作类型');
        Taro.kbToast({ text: `${type}-${resultMsg}未知操作类型` });
        break;
    }
  };

  const { sendMessage } = useCabinetWebsocket({
    getMessage,
    wsOptions: baseData,
  });

  /**
   * 开柜流程
   * 1.handleOpen => 获取包裹信息，是否需二次弹窗
   * 2.handleOpenCabinet => 开柜动作，发送msg
   * 3.接收柜子返回信息，处理开柜成功/失败
   */

  // 二次确认前处理信息
  const handleOpen = async (event, val = baseData) => {
    if (event && event.stopPropagation) {
      event.stopPropagation();
    }

    // if (baseData.isStorage) {
    //   handleStorageConfirm();
    //   return;
    // }

    // 疑似包裹,直接跳走
    if (baseData.suspected > 0) {
      Taro.navigator({
        url: 'pickup/code',
        options: {
          data: JSON.stringify(dakInfo),
        },
      });
      return;
    }
    handleOperate();
    // 清空弹窗
    clearPageData();
    const params = {
      cabinet_id: val.device_id || baseData.device_id || '',
      pickup_code: val.pickup_code || baseData.pickup_code || '',
      batch_no: val.batch_no || baseData.batch_no || '',
      order_id: val.order_id || baseData.waybill_no,
      type: baseData.isStorage ? 'cabinet_storage' : '',
    };

    const result = await getPackageInfo(params);

    out_intercept.current = result.out_intercept;
    Taro.kbSetGlobalData('cabinet_grid_info', result);

    // batchPayParams.current = batchPayValue;

    // 列表（超时） ---- 调整为先与柜子交互
    // if (isFunction(handleNavigatorPay)) {
    //   handleOpenCabinet(params);
    //   return;
    // }
    // 支付页
    // if (batchPayValue) {
    //   if (result.out_intercept == 1) {
    //     handleOpenCabinet({
    //       ...baseData,
    //       type: 'check',
    //     });
    //   } else {
    //     batchPay(batchPayValue);
    //   }
    //   return;
    // }

    if (baseData.isStorage) {
      const { wait_pay_price, order_over_second, order_id } = data || {};
      updatePageData({
        currentVisible: 'confirm',
        curPackageInfo: {
          ...data,
          outTimeInfo: {
            outTime: order_over_second ? formatSecondDate(order_over_second) : '',
            fee: wait_pay_price,
            order_id: order_id,
          },
          isStorage: true,
        },
      });
      return;
    }
    // 多包裹
    // 或者需支付
    const appSetting = !!(val && result.privacy_grid_tip == 1); //  App 设置，是否可以展示取件码

    const showConfirm = !!(appSetting || !val || result.multi > 1 || data.outTimeInfo);

    if (showConfirm) {
      if (data.outTimeInfo) {
        data.outTimeInfo.device_id = data.device_id;
      }
      updatePageData({
        currentVisible: 'confirm',
        curPackageInfo: {
          ...data,
          ...result,
        },
      });
    } else {
      // 不需要二次确认，直接开柜动作
      handleOpenCabinet(params);
    }
  };

  // 开柜动作
  // 如直接操作开柜，参数中需带type,device_id,pickup_code三项参数就可直接开柜
  const handleOpenCabinet = (data = baseData) => {
    if (data && data.mode && pageRef) {
      pageRef.current.mode = data.mode;
    }
    const TYPES = {
      click: 'customer_pickup_list_deliver_open',
      input: 'customer_pickup_deliver_open',
      storage: 'customer_pickup_list_deliver_open',
      check: 'customer_pickup_intercept_check',
      getRejectionList: 'customer_pickup_get_waybill_list',
      rejection: 'customer_rejection_handle',
      rejectReopen: 'customer_reopen_handle',
    };

    const type_ =
      out_intercept.current == 1 && !batchPayParams.current ? 'check' : data.type || type || '';

    const action = TYPES[type_] || '';

    console.info('type_======>', type_);
    console.info('action======>', action);
    console.info('data======>', data);

    delete data.type;
    if (!action) {
      Taro.KbToast({
        text: '类型缺失 160',
      });
      return;
    }
    if (type_ == 'click' && !data.pickup_code) {
      Taro.kbToast({
        text: '取件码缺失 161',
      });
      return;
    }

    type_ != 'rejectReopen' &&
      Taro.kbToast({
        status: 'loading',
        text: type_ == 'getRejectionList' ? '查询中...' : '正在开柜...',
      });
    // 传了storage_goods 是存储物品，不传默认是寄件
    const _data = {
      type: action,
      data: {
        operate_type: type_ == 'storage' || type == 'storage' ? 'storage_goods' : '',
        ...data,
      },
    };

    // 扫码进入主屏不携带is_sub, 主屏为2，副屏，列表开柜开副屏,暂存只开主屏
    const is_sub_cabinet = cabinet_info.is_sub == '1' || dakInfo.sub_cabinet == '1';
    const is_sub = Taro.kbGetGlobalData(CABINET_SUB_KEY);
    if (is_sub_cabinet) {
      _data.data.is_sub = _data.data.operate_type == 'storage_goods' ? '2' : is_sub || '1';
    }
    sendMessage(_data);
  };

  // 继续取件
  const handleContinue = () => {
    if (actionRef) {
      Taro.navigator();
    } else {
      handleCloseSuccess();
    }
  };
  // 取件成功弹窗关闭
  const handleCloseSuccess = () => {
    if (actionRef) {
      Taro.kbSetGlobalData(OPENEDPACKAGE, data);
      Taro.navigator();
    } else {
      handleRemove(data);
      updateList();
      clearPageData();
    }
  };

  const handleBatchPay = async (p) => {
    // handleOpen(null, baseData, p);
    batchPay(p);
  };

  const batchPay = (p = batchPayParams.current) => {
    getBatchPaySign(p).then((res) => {
      batchPayParams.current = null;
      if (res.code == 0) {
        requestPayment(res.data)
          .then(() => {
            Taro.kbToast({ text: '支付成功' });
            console.log('------支付完成', baseData);
            handlePayed(baseData);
            // handleOpenCabinet({ ...baseData, second_open: true });
          })
          .catch((err) => {
            console.log(err);
            Taro.kbToast({ text: '支付失败' });
          });
      } else {
        Taro.kbToast({ text: res.msg });
      }
    });
  };

  const clearPageData = () => {
    updatePageData({
      currentVisible: '',
      curPackageInfo: {},
      openSuccessInfo: {},
      interceptInfo: {},
      rejectionInfo: {},
      shelfPackageInfo: {},
    });
    otherCabinetPackageRef.current = null;
  };

  const navigatorPay = () => {
    clearPageData();
    isFunction(handleNavigatorPay) && handleNavigatorPay();
  };

  const onPickSuccess = (result) => {
    const currentData = latestDataRef.current;
    const currentBaseData = latestBaseDataRef.current;

    const openSuccessInfo = {
      direction: currentData.direction,
      ...result,
      baseData: currentBaseData,
      otherCabinetPackage: otherCabinetPackageRef.current,
    };
    if (mode === 'appointment' || pageRef.current.mode === 'appointment') {
      // 预约取件列表-开柜成功跳转页面(https://tower.im/teams/258300/todos/113595/)
      if (pageRef.current.mode === 'appointment') {
        Taro.kbToast({
          text: '再次开柜成功',
        });
        return;
      }
      // 清空弹窗
      clearPageData();
      Taro.navigator({
        url: 'kdg/pickup-result',
        key: 'routerParamsChange',
        options: {
          ...props,
          baseData,
          openSuccessInfo,
          isReject: result.rejection == 1 && !baseData.isStorage,
        },
        onArrived: () => {},
      });
      updatePageData({
        openSuccessInfo,
      });
    } else {
      updatePageData({
        currentVisible: 'success',
        openSuccessInfo,
      });
    }
  };

  const handlePayed = () => {
    const {
      ec_id,
      waybill,
      waybill_no = waybill,
    } = Taro.kbGetGlobalData('cabinet_grid_info') || {};
    if (!ec_id || !waybill_no) {
      Taro.kbToast({ text: !ec_id ? '柜子id缺失' : '单号缺失' });
      return;
    }
    request({
      url: '/api/weixin/mini/DakMini/Grid/getGridInfo',
      toastLoading: false,
      mastHasMobile: false,
      data: {
        ec_id,
        waybill_no,
      },
      onThen: (res) => {
        console.log(res, '-----getGridInfo----');
        if (res.code == 0) {
          onPickSuccess({
            ...res.data,
            cabinet_code: res.data.row,
            grid_code: res.data.col,
          });
        }
      },
    });
  };

  useEffect(() => {
    if (actionRef && actionRef.current) {
      actionRef.current = {
        handleOpen: (v) => handleOpen(null, v), // 正常开柜流程
        handleOpenCabinet: handleOpenCabinet, // 直接开柜
        closeModal: clearPageData, //关闭弹窗
        openSuccessInfo: pageData.openSuccessInfo,
        handleBatchPay,
        handleContinue,
        handleCloseSuccess,
      };
    }
  }, [actionRef, pageData.openSuccessInfo]);

  return {
    restProps,
    baseData,
    pageData,
    updatePageData,
    handleOpenCabinet,
    handleOpen,
    handleContinue,
    handleCloseSuccess,
    batchPay,
  };
}
