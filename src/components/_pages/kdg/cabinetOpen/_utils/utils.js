/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { requestPayment } from '@/utils/qy';
import request from '@base/utils/request';
import Taro from '@tarojs/taro';

export function handleCabinetPay(data) {
  return new Promise((resolve, reject) => {
    const params = {
      ...data,
      out_trade_no: data.order_number,
      pay_method: 'wechat',
    };
    request({
      url: '/api/weixin/mini/DakMini/Record/cabinetPackageTimeoutPay',
      data: params,
    }).then((res) => {
      if (res.code == 0) {
        requestPayment(res.data)
          .then(() => {
            Taro.kbToast({ text: '支付成功' });
            resolve();
          })
          .catch(() => {
            Taro.kbToast({ text: '支付失败' });
            reject();
          });
      } else {
        reject();
        Taro.kbToast({ text: res.msg });
      }
    });
  });
}

export const getBatchPaySign = (data) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/DakMini/Record/cabinetPackageTimeoutPay',
      mastHasMobile: false,
      data,
    }).then(resolve);
  });
};

export const getStoragePaySign = (data) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/minpost/CabinetStorage/payOverdueFeeSign',
      mastHasMobile: false,
      data,
    }).then(resolve);
  });
};
