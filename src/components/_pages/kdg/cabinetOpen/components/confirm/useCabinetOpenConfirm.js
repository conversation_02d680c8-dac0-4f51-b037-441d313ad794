/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { requestPayment } from '@/utils/qy';
import request from '@base/utils/request';
import Taro from '@tarojs/taro';

export function useCabinetOpenConfirm(props) {
  const { curPackageInfo, updatePageData, handleOpenCabinet } = props;

  const { outTimeInfo, source, order_id, ...restPackageInfo } = curPackageInfo || {};

  const isNeedPay = outTimeInfo && !!parseFloat(outTimeInfo.fee || 0);

  const getPaySign = (data) => {
    const url =
      source == 'storage'
        ? '/api/weixin/mini/minpost/CabinetStorage/payOverdueFeeSign'
        : '/api/weixin/mini/DakMini/Record/cabinetPackageTimeoutPay';
    return new Promise((resolve) => {
      const { device_id, waybill_no } = data;
      const params = {
        source: 'batch_pay',
        device_id,
        waybill_no,
        order_id: order_id || '',
        current: waybill_no,
      };
      request({
        url,
        data: params,
        toastError: true,
      }).then(({ code, data, msg }) => {
        if (`${code}` === '0') {
          resolve(data);
        } else {
          Taro.kbToast({ text: msg });
        }
      });
    });
  };

  // 支付
  const handlePay = (data) =>
    new Promise(async (resolve, reject) => {
      Taro.kbToast({
        status: 'loading',
      });
      const sign = await getPaySign(data);
      requestPayment(sign)
        .then(() => {
          Taro.kbToast({ text: '支付成功' });
          resolve();
        })
        .catch(() => {
          Taro.kbToast({ text: '支付失败' });
          reject();
        });
    });

  const onConfirm = async () => {
    // 支付前需要走一次交互 仅存柜
    if (isNeedPay && source == 'storage') {
      await handlePay(outTimeInfo);
    }
    handleOpenCabinet();
  };

  const onCancel = () => {
    updatePageData({
      currentVisible: '',
    });
  };

  return {
    onConfirm,
    onCancel,
    isNeedPay,
    outTimeInfo: outTimeInfo || {},
    restPackageInfo,
  };
}
