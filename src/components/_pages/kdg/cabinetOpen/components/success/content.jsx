/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import { padZero } from '../../../_utils';

// 取件成功弹窗

const CabinetOpenSuccessContent = (props) => {
  const { openSuccessInfo, baseData } = props;

  const { direction, number, grid_number, cabinet_code, grid_code, otherCabinetPackage } =
    openSuccessInfo || {};

  return (
    <View>
      {direction ? (
        <View className='kb-directory at-row at-row__align--center'>
          {direction == 'left' && (
            <Image
              className='kb-directory__img kb-directory__img-revert kb-margin-md-r'
              src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
            />
          )}
          <View className='kb-size__lg'>显示屏{direction == 'left' ? '左侧' : '右侧'}</View>
          {direction == 'right' && (
            <Image
              className='kb-directory__img kb-margin-md-l'
              src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
            />
          )}
        </View>
      ) : (
        <View className='at-row at-row__justify--center'>
          <Image
            className='avatar-img'
            src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2023/06/01/6478142d5caae/<EMAIL>'
          />
        </View>
      )}
      {(otherCabinetPackage || baseData.enable_cross_pick) && (
        <View className='kb-color__black kb-spacing-md-t'>
          前往
          <Text className='kb-size__xl'>
            {otherCabinetPackage ? otherCabinetPackage.name : baseData.inn_name}
          </Text>
        </View>
      )}
      {openSuccessInfo && (
        <View className='kb-color__black kb-spacing-md-tb'>
          {number && grid_number == 1 ? (
            <Text className='kb-size__48'>
              （格口
              <Text className='kb-number'>{number}</Text> ）
            </Text>
          ) : (
            <View className='kb-size__48 at-row at-row__align--baseline at-row__justify--center'>
              <Text className='kb-number'>{padZero(cabinet_code)}</Text>
              号柜 <Text className='kb-number'>{padZero(grid_code)}</Text>
              格口
            </View>
          )}
        </View>
      )}
      <View className='kb-color__grey kb-size__lg kb-padding-40-b'>
        柜门已开，请取走包裹并关上柜门
      </View>
    </View>
  );
};

CabinetOpenSuccessContent.defaultProps = {
  openSuccessInfo: '',
};

CabinetOpenSuccessContent.options = {
  addGlobalClass: true,
};

export default CabinetOpenSuccessContent;
