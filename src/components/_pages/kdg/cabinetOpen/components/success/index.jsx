/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import { View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import CabinetOpenSuccessContent from './content';

// 取件成功弹窗

const CabinetOpenSuccess = (props) => {
  const {
    successVisible,
    handleContinue,
    handleCloseSuccess,
    handleOpenCabinet,
    openSuccessInfo,
    baseData,
  } = props;

  const { otherCabinetPackage } = openSuccessInfo || {};

  return (
    <KbModal
      centered
      top={false}
      closable={false}
      closeOnClickOverlay={false}
      isOpened={successVisible}
      cancelText='再次开柜'
      confirmText='继续取件'
      cancelButtonProps={{ type: 'secondary' }}
      onConfirm={handleContinue}
      onCancel={() =>
        handleOpenCabinet({
          ...baseData,
          second_open: true,
          ...(otherCabinetPackage
            ? {
                device_id: otherCabinetPackage.device_id,
              }
            : {}),
        })
      }
      onClose={handleCloseSuccess}
      rootClass='kb-cabinetOpenModalWrap'
      footerClass='kb-cabinetOpenModalFooter'
    >
      <View className='kb-cabinetOpenModal'>
        <View className='kb-close' hoverClass='kb-hover-opacity' onClick={handleCloseSuccess}>
          <AtIcon prefixClass='kb-icon' value='closed' className='kb-size__x kb-color__grey' />
        </View>
        <CabinetOpenSuccessContent openSuccessInfo={openSuccessInfo} baseData={baseData} />
      </View>
    </KbModal>
  );
};

CabinetOpenSuccess.defaultProps = {
  handleOpenCabinet: () => {},
  handleContinue: () => {},
  handleCloseSuccess: () => {},
  openSuccessInfo: '',
  successVisible: false,
};

CabinetOpenSuccess.options = {
  addGlobalClass: true,
};

export default CabinetOpenSuccess;
