/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '~base/components/modal';
import { Text, View } from '@tarojs/components';

// 货架包裹弹窗

const CabinetOpenShelfPackage = (props) => {
  const { interceptInfo, updatePageData, handleOpenCabinet, batchPay, visible } = props;

  const confirmText =
    interceptInfo.code === 10001 ? (interceptInfo.batchPay ? '确认支付' : '开柜取件') : '';

  const onClose = () => {
    updatePageData({
      currentVisible: '',
      interceptInfo: {},
    });
  };

  const onConfirm = () => {
    handleOpenCabinet();
    return;
    if (interceptInfo.batchPay) {
      if (interceptInfo.handleNavigatorPay) {
        interceptInfo.handleNavigatorPay();
        return;
      }
      batchPay(interceptInfo.batchPay);
    } else {
    }
  };

  return (
    <KbModal
      title={[{ text: '温馨提示', className: 'kb-color__brand' }]}
      centered
      top={false}
      closable={false}
      isOpened={visible}
      cancelText='我知道了'
      cancelButtonProps={{ type: 'secondary' }}
      confirmText={confirmText}
      onConfirm={onConfirm}
      onCancel={onClose}
      onClose={onClose}
    >
      <View className='kb-cabinetModal'>
        <View className='kb-size__lg kb-color__black kb-spacing-md-tb'>
          <Text>{interceptInfo.msg}</Text>
        </View>
      </View>
    </KbModal>
  );
};

CabinetOpenShelfPackage.defaultProps = {
  updatePageData: () => {},
  interceptInfo: {},
  visible: false,
};

CabinetOpenShelfPackage.options = {
  addGlobalClass: true,
};

export default CabinetOpenShelfPackage;
