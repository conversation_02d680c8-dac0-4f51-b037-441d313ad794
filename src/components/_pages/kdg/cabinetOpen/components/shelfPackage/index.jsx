/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import { Text, View } from '@tarojs/components';
import ExpressLightsBar from '@/components/_pages/query/lights-bar';

// 货架包裹弹窗

const CabinetOpenShelfPackage = (props) => {
  const { shelfPackageInfo, updatePageData, visible } = props;

  const onClose = () => {
    updatePageData({
      currentVisible: '',
      shelfPackageInfo: {},
    });
  };

  return (
    <KbModal
      title='温馨提示'
      centered
      top={false}
      closable={false}
      isOpened={visible}
      confirmText='我知道了'
      onConfirm={onClose}
      onCancel={onClose}
      onClose={onClose}
    >
      <View className='kb-cabinetModal'>
        <View className='kb-size__lg kb-color__black kb-spacing-md-tb'>
          <Text>{shelfPackageInfo.message}</Text>
        </View>
        {shelfPackageInfo.lightBarVisible && (
          <ExpressLightsBar
            waybill={shelfPackageInfo.waybill}
            dak_id={shelfPackageInfo.dak_id}
            source='3'
            shelves
          />
        )}
      </View>
    </KbModal>
  );
};

CabinetOpenShelfPackage.defaultProps = {
  updatePageData: () => {},
  shelfPackageInfo: {},
  visible: false,
};

CabinetOpenShelfPackage.options = {
  addGlobalClass: true,
};

export default CabinetOpenShelfPackage;
