/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, ScrollView, Text, View } from '@tarojs/components';
import { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import isUndefined from 'lodash/isUndefined';
import KbCheckbox from '~base/components/checkbox';
import KbButton from '~base/components/button';
import { padZero } from '../../../_utils';

const CabinetOpenRejectionContent = (props) => {
  const { rejectionInfo, step, brands, checked, setStep, handleChoose, handleOpen } = props;
  const {
    direction,
    number,
    grid_number,
    cabinet_code,
    grid_code,
    waybillList = [],
    status,
  } = rejectionInfo || {};

  const getBrandName = (item) => {
    return item.brand == 'qita'
      ? '其他'
      : (brands[item.brand] && brands[item.brand].name) || item.brand;
  };

  return (
    <View className='kb-cabinetOpenModal'>
      {step == 1 ? (
        <Fragment>
          {direction ? (
            <View className='kb-directory at-row at-row__align--center'>
              {direction == 'left' && (
                <Image
                  className='kb-directory__img kb-directory__img-revert kb-margin-md-r'
                  src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
                />
              )}
              <View className='kb-size__lg'>显示屏{direction == 'left' ? '左侧' : '右侧'}</View>
              {direction == 'right' && (
                <Image
                  className='kb-directory__img kb-margin-md-l'
                  src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
                />
              )}
            </View>
          ) : (
            <View className='at-row at-row__justify--center'>
              <Image
                className='avatar-img'
                src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2023/06/01/6478142d5caae/<EMAIL>'
              />
            </View>
          )}
          {rejectionInfo && (
            <View className='kb-color__black kb-spacing-md-tb'>
              {number && grid_number == 1 ? (
                <Text className='kb-size__48'>
                  （格口
                  <Text className='kb-number'>{number}</Text> ）
                </Text>
              ) : (
                <View className='kb-size__48 at-row at-row__align--baseline at-row__justify--center'>
                  <Text className='kb-number'>{padZero(cabinet_code)}</Text>
                  号柜 <Text className='kb-number'>{padZero(grid_code)}</Text>
                  格口
                </View>
              )}
            </View>
          )}
          <View className='kb-color__grey kb-size__sm kb-padding-40-b'>
            柜门已开，
            {isUndefined(status) ? '请取走包裹并关上柜门' : '请将包裹放回柜中，关上柜门'}
          </View>
          {isUndefined(status) && (
            <View
              className='kb-color__brand kb-padding-40-b'
              hoverClass='kb-hover-opacity'
              onClick={() => setStep(2)}
            >
              拒收退回 {'>'}
            </View>
          )}
        </Fragment>
      ) : step == 2 ? (
        <Fragment>
          <View className='kb-size__bold kb-color__brand kb-size__xl'>拒收退回说明</View>
          <View className='kb-margin-xl-t kb-text__left kb-margin-40-b'>
            确保您当前取出的包裹未拆开，可选择拒收退回，已拆包裹不允许拒收退回!
          </View>
        </Fragment>
      ) : step == 3 ? (
        waybillList && Array.isArray(waybillList) ? (
          <Fragment>
            <Image
              className='kb-rejection_img'
              src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_open.png'
            />
            {waybillList.length > 1 ? (
              <Fragment>
                <View className='kb-size__bold kb-color__black kb-size__xl kb-text__left kb-spacing-xl-tb'>
                  一格口多包裹，请选择退回包裹
                </View>
                <ScrollView
                  scrollY
                  className={classNames('kb-rejection-list', {
                    'kb-rejection-list-page': props.isPage,
                  })}
                >
                  {waybillList.map((item, index) => (
                    <View
                      key={item.waybill_no}
                      className={classNames('kb-spacing-md-tb kb-rejection-item', {
                        'kb-border-t': index > 0,
                      })}
                      hoverClass='kb-hover-opacity'
                      onClick={() => handleChoose(item)}
                    >
                      <View className='at-row at-row__align--center at-row__justify--between'>
                        <Text>{getBrandName(item)} </Text>
                        <Text>{item.waybill_no}</Text>
                        <KbCheckbox
                          checked={checked.findIndex((v) => v.waybill_no == item.waybill_no) > -1}
                          onChange={() => handleChoose(item)}
                        />
                      </View>
                    </View>
                  ))}
                </ScrollView>
              </Fragment>
            ) : (
              <Fragment>
                <View className='kb-size__bold kb-color__black kb-size__48'>确认退回</View>
                <View className='kb-color__black kb-size__xl'>
                  <Text>{waybillList && waybillList[0] && waybillList[0].waybill_no}</Text>
                </View>
              </Fragment>
            )}
          </Fragment>
        ) : null
      ) : null}
      {step == 3 && (
        <Fragment>
          <KbButton
            className='kb-button__middle kb-margin-xl-tb kb-w300'
            circle
            type='primary'
            onClick={handleOpen}
          >
            确认并打开柜门
          </KbButton>
        </Fragment>
      )}
    </View>
  );
};

CabinetOpenRejectionContent.options = {
  addGlobalClass: true,
};

export default CabinetOpenRejectionContent;
