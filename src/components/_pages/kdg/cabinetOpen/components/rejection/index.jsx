/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import { get } from '~/actions/brands';
import { AtIcon } from 'taro-ui';
import CabinetOpenRejectionContent from './content';
import { useReject } from './utils';

// 取件成功弹窗

const CabinetOpenRejection = (props) => {
  const {
    visible,
    step,
    checked,
    brands,
    status,
    rejectionInfo,
    setStep,
    handleChoose,
    handleCancel,
    handleConfirm,
    handleOpen,
    handleCloseSuccess,
  } = useReject(props);

  return (
    <KbModal
      centered
      top={false}
      closable={false}
      closeOnClickOverlay={false}
      isOpened={visible}
      cancelText={step == 1 ? (status ? '重开柜门' : '再次开柜') : step == 2 ? '取消' : ''}
      confirmText={step == 1 ? (status ? '投柜完成' : '继续取件') : step == 2 ? '继续退回' : ''}
      cancelButtonProps={{ type: 'secondary' }}
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      onClose={handleCloseSuccess}
      className='kb-cabinetOpenModalContent_rejection'
      rootClass='kb-cabinetOpenModalWrap'
      footerClass='kb-cabinetOpenModalFooter'
    >
      <View className='kb-cabinetOpenModal'>
        <View className='kb-close' hoverClass='kb-hover-opacity' onClick={handleCloseSuccess}>
          <AtIcon prefixClass='kb-icon' value='closed' className='kb-size__x kb-color__grey' />
        </View>
        <CabinetOpenRejectionContent
          openSuccessInfo={rejectionInfo}
          step={step}
          brands={brands}
          checked={checked}
          setStep={setStep}
          handleChoose={handleChoose}
          handleOpen={handleOpen}
        />
      </View>
    </KbModal>
  );
};

CabinetOpenRejection.defaultProps = {
  handleOpenCabinet: () => {},
  handleContinue: () => {},
  handleCloseSuccess: () => {},
  rejectionInfo: {},
};

CabinetOpenRejection.options = {
  addGlobalClass: true,
};

export default connect(({ global }) => ({ brands: global.brands }), {
  dispatchGet: get,
})(CabinetOpenRejection);
