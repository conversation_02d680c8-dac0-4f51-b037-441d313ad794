/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 激励视频/插屏广告
// idsMap
export const adIdsMap = {
  0: {
    id: 'adunit-e8c9b02e1e3ccfc4',
    label: '我的更多推荐',
    type: 'rewardedVideo',
  },
  1: {
    id: 'adunit-0b6279a459673f34',
    label: '下单结果页',
    type: 'interstitial',
  },
  welfare: {
    id: 'adunit-dc28a833c9cab0f2',
    label: '福利中心',
    type: 'interstitial',
  },
  lotteryList: {
    id: 'adunit-564efa7d10aa40a7',
    label: '抽奖列表',
    type: 'interstitial',
  },
  lotteryDetails: {
    id: 'adunit-6ccd7d30cde9dca8',
    label: '抽奖详情',
    type: 'interstitial',
  },
  'query.detail': {
    id: 'adunit-8b89abbd1fdec509',
    label: '查件详情',
    type: 'interstitial',
  },
  lotteryTask: {
    id: 'adunit-26ef0e1c1b8c2498',
    label: '抽奖详情任务',
    type: 'rewardedVideo',
  },
};

export default {
  0: 'adunit-0af5ecc84d40c4fd.custom', // 预约取件位置：
  1: 'adunit-c2b4027267c614a0.custom', //物流详情位置：58be93eea119ad4d
  2: 'adunit-531bf0302fdadc90.custom', //首页取件位置：
  'courier.detail': 'adunit-0140ea84811d06c2', // 快递员详情页
  'order.result': 'adunit-99e86c8079afc0f0.custom', // 下单结果页
  'order.detail': 'adunit-67188e7a2bcf2e9e.custom', // 订单详情页 374f6702634c873b
  'order.pay': 'adunit-3ef843737937b18b.custom', // 订单待支付页
  query: 'adunit-531bf0302fdadc90.custom', // 物流详情页
  'query.detail': 'adunit-c2b4027267c614a0.custom', // 物流详情页
  yiqing: 'adunit-cfba8b1296abab2c.custom', // 疫情查询页
};
