/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { formatParamsOut } from '@base/components/_utils/index';
import request from '@base/utils/request';
import { check } from '@base/utils/rules';
import { getPage, getStorage, noop, reportAnalytics, setStorage } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import isEmpty from 'lodash/isEmpty';
import isFunction from 'lodash/isFunction';
import idsMap, { adIdsMap } from '../mapAdId';
import { getLastUseRelation } from '../../store-card/_utils';
import { checkIsShieldAd } from './shield';

/**
 *
 * @description 汇总上报数据统计
 * @param {*} status
 * @param {*} type
 * @param {*} opts
 */
function triggerReportAnalyticsByType(status, type, opts) {
  const typesMap = {
    interstitial: {
      load: 'cpad_onload',
      show: 'cpad_errormsg',
    },
    rewardedVideo: {
      error: 'rewarded_video_ad',
      show: 'rewarded_video_ad',
    },
  };
  const key = typesMap[type][status];
  if (key) {
    reportAnalytics({
      key,
      ...opts,
    });
  }
}
/**
 * @description 生成激励视屏或者插屏广告
 * @param {*} opts
 * @returns
 */
const adInsMap = {};
function getCurrentPagePath() {
  const { route: path = '' } = getPage(-1, false);
  return path;
}
export const AD_STORAGE_KEY = 'AD';
let dynamicAdMaps = null;
export const getCityConfigAdId = (key) => {
  return new Promise((resolve, reject) => {
    let config = adIdsMap[key];
    if (!process.env.MODE_ENV.includes('third')) {
      if (config) return resolve(config);
      reject();
    } else if (dynamicAdMaps) {
      if (dynamicAdMaps[key]) {
        resolve(
          formatParamsOut({
            data: dynamicAdMaps[key],
            keys: [
              ['pop_up', 'id'],
              ['banner', 'banner'],
              ['type', () => 'interstitial'], //默认此项
            ],
          }),
        );
        return;
      }
      reject();
    } else {
      if (process.env.MODE_ENV === 'wkd') {
        reject();
        return;
      }
      request({
        url: '/api/weixin/mini/Ad/showFlowAd',
        mastHasMobile: false,
        onThen: (res) => {
          const { code, data } = res || {};
          if (code === 0 && data) {
            dynamicAdMaps = formatParamsOut({
              data,
              keys: [
                ['check_piece_page', 'query'],
                ['order_details_page', 'order.detail'],
                ['logistics_details_page', 'query.detail'],
                ['subscribe_get_page', 'query.appointment'],
                ['order_submit_page', 'order.result'],
                ['stage_home_page', 'order.station'],
              ],
            });
            if (dynamicAdMaps[key]) {
              resolve(
                formatParamsOut({
                  data: dynamicAdMaps[key],
                  keys: [
                    ['pop_up', 'id'],
                    ['banner', 'banner'],
                    ['type', () => 'interstitial'], //默认此项
                  ],
                }),
              );
            } else {
              reject();
            }
          } else {
            reject();
          }
        },
      });
    }
  });
};

// 插屏广告包装
export async function createInterstitialAdWrap(callback, closeShield) {
  if (process.env.PLATFORM_ENV === 'weapp') {
    const shield = await checkIsShieldAd(closeShield ? '-1' : '1');
    if (!shield && isFunction(callback)) {
      callback();
    }
    return shield;
  }
}

export function createAd(opts, { closeShield, max = 1, filter, MIN_TS = 10, onClose } = {}) {
  return new Promise((resolve, reject) => {
    createInterstitialAdWrap(() => {
      if (process.env.PLATFORM_ENV === 'weapp') {
        const { ad } = Taro.launchParams || {};
        const filterType = ['lotteryTask'].includes(opts);
        if (!filterType && (ad == 0 || ad == 1)) {
          reject();
          return;
        }
        // status === 'load' 仅做预加载处理
        const [key, status = 'show'] = opts.split('-');
        getCityConfigAdId(key, 'interstitial')
          .then(({ id: adUnitId, type }) => {
            console.log('key', key, 'interstitial', adUnitId);
            if (!adUnitId) return reject();
            const adUnitIdKey = `${adUnitId}-${key}`;
            const storageKey = `${AD_STORAGE_KEY}_${adUnitIdKey}`;
            const defaultConfig = {
              adIns: null,
              actionRef: {},
              path: '',
            };
            if (filterType) {
              adInsMap[adUnitIdKey] = defaultConfig;
            } else {
              adInsMap[adUnitIdKey] = adInsMap[adUnitIdKey] || defaultConfig;
            }

            let { actionRef, adIns } = adInsMap[adUnitIdKey];
            if (!adIns) {
              switch (type) {
                case 'rewardedVideo':
                  adIns = Taro.createRewardedVideoAd({ adUnitId });
                  break;
                case 'interstitial':
                  adIns = Taro.createInterstitialAd({ adUnitId });
                default:
                  break;
              }

              actionRef.count = 0;

              // 广告加载成功
              adIns.onLoad(() => {
                actionRef.loaded = true;
                if (status === 'load') {
                  triggerReportAnalyticsByType('load', type);
                  // 预加载处理
                  resolve();
                } else if (actionRef.triggerShow) {
                  actionRef.triggerShow();
                }
              });

              // 广告加载失败
              adIns.onError(({ errCode, errMsg }) => {
                // 激励红包拉取失败统计
                triggerReportAnalyticsByType('error', type, {
                  err: `${errCode} ${errMsg}`,
                });
                reject(new Error(`${errMsg}，请稍后重试`));
              });
              adIns.onClose((res) => {
                const { isEnded = false } = res || {};
                onClose && onClose(isEnded);
                if (isEnded) {
                  resolve();
                } else {
                  reject(new Error('播放中途退出，无法获得奖励'));
                }
              });

              adInsMap[adUnitIdKey].adIns = adIns;
              adInsMap[adUnitIdKey].path = getCurrentPagePath();
            }

            actionRef.triggerShow = (outTs) => {
              if (!actionRef.loaded || (!filter && actionRef.count >= max)) return;
              getStorage({
                key: storageKey,
                complete: (res) => {
                  const { ts: STORAGE_TS = 0, data } = res.data || {};
                  const NOW_TS = new Date().getTime();
                  const APP_TS = Taro.APP_TS || NOW_TS;
                  const OUT_TS = outTs || MIN_TS;
                  const delayTimer = Math.max(OUT_TS - (NOW_TS - APP_TS), MIN_TS);
                  if (
                    filter &&
                    filter({
                      data,
                      NOW_TS,
                      APP_TS,
                      OUT_TS,
                      STORAGE_TS,
                    })
                  )
                    return;
                  actionRef.delay && clearTimeout(actionRef.delay);
                  actionRef.delay = setTimeout(() => {
                    if (getCurrentPagePath() !== adInsMap[adUnitIdKey].path) {
                      return;
                    }
                    adIns
                      .show()
                      .then(() => {
                        resolve();
                        actionRef.count++;
                        setStorage({
                          key: storageKey,
                          data: {
                            count: actionRef.count,
                          },
                        });
                      })
                      .catch(({ message, errMsg = message }) => {
                        console.log('errMsg', errMsg);
                        if (!outTs) {
                          actionRef.triggerShow(15000);
                          return;
                        }
                        triggerReportAnalyticsByType('show', type, {
                          errormsg: errMsg,
                          err: '激励视频 广告显示失败',
                        });
                      });
                  }, delayTimer);
                },
              }).catch(noop);
            };

            if (status === 'load') return;
            actionRef.triggerShow();
          })
          .catch(noop);
      }
    }, closeShield);
  });
}

/**
 * @description 广告导航器
 */
export function adNavigator(opts) {
  let {
    ad_url,
    adUrl,
    url = adUrl,
    options,
    app_id: appId,
    report,
    target = 'blank',
    ...rest
  } = opts;

  /**
   *
   * @description 补充广告通用上报统计
   * position：广告位置
   * title：广告标题
   * status：copy-复制
   * 1、调用Taro.navigator
   * 上报时会补充 - status：click-点击|success-跳转成功|fail-跳转失败、message:错误信息；
   * 2、调用getThirdAd
   * 上报时会补充 - options: `三方拉取${to ? '成功' : '失败'}`,source: to；
   */
  const { title: reportTitle, position: reportPosition, ...restReport } = report || {};
  report = {
    ...restReport,
    key: 'event_push_ad',
    position: reportPosition || opts.position || 'ad',
    title: reportTitle || opts.title || 'empty',
  };
  opts.report = report;

  if (url === 'videoAd') {
    createAd('0')
      .then(() => {
        // 可以获取奖励
        console.log('跳转奖励领取页面');
      })
      .catch((err) => {
        Taro.kbToast({
          text: err.message,
        });
      });
    return;
  }

  // 广告链接默认为完整链接  suffix 设置为 null
  if (check('path', url).code === 0 || check('pluginUrl', url).code === 0) {
    if (check('path', url).code === 0 && ad_url) {
      url = ad_url;
    }
    // 小程序页面
    Taro.navigator({
      url,
      target,
      options,
      appId,
      report,
      force: true,
      ...rest,
    });
    return;
  }

  Taro.navigator({
    url,
    appId,
    target: 'webview-' + target,
    force: true,
    report,
    suffix: null,
    ...rest,
  });
}

/**
 *
 * @description 预加载广告
 * @param {*} keys
 * @returns
 */
export function preloadAd(keys = Object.keys(idsMap)) {
  if (process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV === 'weapp') {
    if (!wx.preloadAd) return;
    const list = keys
      .map((key) => {
        const [unitId, type = 'banner'] = `${idsMap[key] || ''}`.split('.');
        return unitId ? { unitId, type } : null;
      })
      .filter((item) => item);
    wx.preloadAd(list);
  }
}

/**
 * 拉取admin后台广告
 */
export const loadAdminAd = (reqData = {}) => {
  reqData = isObject(reqData) ? reqData : { position: reqData };
  return new Promise((resolve, reject) => {
    request({
      url: '/g_tbk/v2/AdConfig/getAdConfig',
      data: {
        platform: 'wkdmini',
        type: 'miniapp',
        ...reqData,
      },
      onThen: ({ code, data = [] }) => {
        if (code == 0 && data) {
          data = isArray(data) ? data : [data];
          if (data.length > 0) {
            data = data.map((item) => {
              let [title, tag] = item.title.split('#');
              return {
                ...item,
                title,
                tag,
              };
            });
          }
          resolve(data);
        } else {
          reject();
        }
      },
    });
  });
};

/**
 *
 * @description 检查广告位是否展示
 */
export function checkAdCanShow(data) {
  return new Promise(async (resolve) => {
    console.log('检查广告位是否展示==>', data);
    const relation = await getCurrentRelation();
    console.log('relation', relation);
    if (!relation) return;
    request({
      url: '/api/weixin/mini/Ad/checkIsBlockAdvertisements',
      data: {
        cm_id: relation.dak_id,
      },
      autoRequestAfterLogin: true,
      toastLoading: false,
      timeout: 3000,
      onThen: (res) => {
        const { ad = '-1' } = res.data || {};
        resolve({ ad });
      },
    });
  });
}

/**
 *
 * @description
 * ad = 0 屏蔽所有类型广告
 * ad = 1 屏蔽所有流量主，插屏等官方相关广告；
 * ad = 2 屏蔽所有自建广告
 * type 当前广告位类型
 * @param {'0'|'1'|'2'} ad
 * @param {'1'|'2'} type
 */
export function checkAdShow(ad, type) {
  const ads = ['0', '1', '2'];
  const ad_ = `${ad}`;
  let type_ = `${type}`;
  type_ = ads.slice(1).includes(type_) ? type_ : '';

  if (!ads.includes(ad_)) return true; // 不在范围内的都可展示；
  if (ad_ === '0' || !type_) return false; // 没传type等同于ad===0
  return ad_ !== type_; // 相等代表要屏蔽
}

export const getCurrentRelation = () => {
  return new Promise((resolve) => {
    if (!isEmpty(Taro.kbRelationInfo.data)) {
      resolve(Taro.kbRelationInfo.data);
    } else {
      getLastUseRelation()
        .then((data) => {
          console.log('获取默认下单关系==>', data);
          resolve(data);
        })
        .catch(() => {
          resolve();
        });
    }
  });
};
