/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import { checkIsShieldAd } from './sdk/shield';
import { reportAnalytics } from '~base/utils/utils';

/**
 *
 * @description 拉取广告位统一上报
 * position：广告位置，具体可对应admin后台的配置
 * status：load：初始拉取、load-success：拉取成功、load-fail：拉取失败、load-exact-success：拉取成功 - 精确（会有广告位的title）
 */
const triggerAdConfigEventPush = (
  data,
  list,
  status = !list ? 'load' : list.length > 0 ? 'load-success' : 'load-fail',
) => {
  const opts = {
    key: 'event_push_ad',
    position: (data && data.position) || '0',
  };
  reportAnalytics({
    ...opts,
    status,
  });
  if (isArray(list)) {
    // 精确统计
    list.forEach((item) => {
      reportAnalytics({
        ...opts,
        title: item.title,
        status: 'load-exact-success',
      });
    });
  }
};

export const getAdConfig = (params) => {
  let url = '/api/weixin/mini/Ad/getAdConfig';
  let data = {
    platform: process.env.PLATFORM_ENV == 'weapp' ? 'yzmina' : 'yzaliapp',
    ...params,
  };
  if (process.env.MODE_ENV === 'wkd') {
    url = '/g_order_core/v2/mina/User/getBannerList';
    data = params;

    if (data && data.platform === 'dtcc') {
      // 兼容地图查查
      url = '/g_tbk/v2/AdConfig/getAdConfig';
    }
  }
  triggerAdConfigEventPush(data); // 开始请求
  return new Promise((resolve) => {
    checkIsShieldAd('2', null, null, data).then((shield) => {
      if (shield) {
        // 拦截自建广告
        resolve([]);
        return;
      }
      request({
        url,
        data,
        toastLoading: false,
        mastHasMobile: false,
        onThen: (res) => {
          const { data } = res || {};
          const list = isArray(data) ? data : [];
          triggerAdConfigEventPush(data, list); // 成功
          resolve(list);
        },
      });
    });
  });
};

export const getAdExtensionReq = (key) => {
  const adExtensionReqMap = {
    'query.appointment': 3,
    'order.station': 5,
    'order.result': 4,
    'order.detail': 6,
    query: 1,
    'query.detail': 2,
    'order.edit': 21,
  };
  return { position: adExtensionReqMap[key] };
};

export const getAdTypeReq = (key) => {
  const adTypeMap = {
    'order.result': 'place',
    'query.detail': 'logistics',
  };
  return { type: adTypeMap[key] };
};

export const getAdStorageKey = (key) => {
  const storageKeyMap = {
    'order.result': 'intersitialAdTimer',
    'query.detail': 'logisticsAdTimer',
  };
  return storageKeyMap[key];
};
