/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Ad, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import { Fragment, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { useCheckIsShieldAd } from '../sdk/shield';
import idmap from '../mapAdId';
import './index.scss';

/**
 * 微信官方广告组件位;
 */
const KbExternalAd = (props) => {
  const {
    unitId: unitIdProps,
    adUnitIdIndex,
    intervals,
    style,
    onLoad,
    onError,
    onClose,
    wrapper,
    implant,
    force,
  } = props;
  const unitIdAndType = idmap[adUnitIdIndex] || unitIdProps || '';
  const [unitId = '', type = 'normal'] = unitIdAndType.split('.');
  const [isOpened, updateIsOpened] = useState(!!unitId);
  const { isVip = false } = useSelector((state) => state.global);
  const rootClass = classNames({
    'kb-ad__wrapper': wrapper || type !== 'custom',
    'kb-box': implant,
    'kb-spacing-md': implant && type !== 'custom',
  });

  const handelClose = (e) => {
    updateIsOpened(false);
    onClose(e);
  };
  const handelAdErr = (e) => {
    onError(e);
    handelClose();
  };

  // 是否屏蔽官方广告
  const shield = useCheckIsShieldAd('1');

  return process.env.PLATFORM_ENV === 'weapp' && process.env.MODE_ENV !== 'third.post' ? (
    <Fragment>
      {isOpened && (force || (!isVip && !shield)) ? (
        <View style={{ ...style }} className={rootClass}>
          {type === 'custom' ? (
            <ad-custom
              className='kb-ad'
              adIntervals={intervals}
              style='height:auto;'
              unitId={unitId}
              onLoad={onLoad}
              onError={handelAdErr}
              onClose={handelClose}
            />
          ) : (
            <Ad
              className='kb-ad'
              adIntervals={intervals}
              style='height:auto;'
              unitId={unitId}
              onLoad={onLoad}
              onError={handelAdErr}
              onClose={handelClose}
            />
          )}
        </View>
      ) : null}
    </Fragment>
  ) : (
    <Fragment />
  );
};
KbExternalAd.defaultProps = {
  intervals: '2000',
  adUnitIdIndex: '',
  implant: true,
  wrapper: false,
  onLoad: () => console.log('ad onLoad'),
  onError: () => console.log('ad onError'),
  onClose: () => console.log('ad onClose'),
};
KbExternalAd.options = {
  addGlobalClass: true,
};
export default KbExternalAd;
