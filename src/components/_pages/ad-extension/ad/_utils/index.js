/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useState } from '@tarojs/taro';
import isPlainObject from 'lodash/isPlainObject';
import { useStorage } from '~base/utils/storage';
import idsMap from '../../mapAdId';

// 获取广告位id
export const useKbAdExtensionAdId = (opt = {}) => {
  const { adUnitIdIndex } = opt;
  const unitIdAndType = idsMap[adUnitIdIndex] || adUnitIdIndex;

  // 官方广告轮询展示
  const { update, getData } = useStorage('ad-official-loop');

  const [unitIds, setUnitIds] = useState('');

  useEffect(() => {
    // 未获取到位置信息前也要先展示原本广告
    const _unitIds = unitIdAndType;
    if (isPlainObject(_unitIds)) {
      const { action, list } = _unitIds;
      switch (action) {
        case 'loop': // 循环展示
          getData().then((data) => {
            const { data: { index = 0 } = {} } = data || {};
            const currentAd = list[index];
            setUnitIds(currentAd);
            const nextIndex = 1 + index;
            update({ index: nextIndex >= list.length ? 0 : nextIndex });
          });

          break;
      }
    } else if (_unitIds) {
      setUnitIds(_unitIds);
    }
  }, [unitIdAndType]);

  return {
    unitIds,
    setUnitIds,
  };
};
