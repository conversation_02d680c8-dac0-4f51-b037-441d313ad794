import Taro, { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附
 */
export function useAutoAdsorption(coverData, { selector, checkCollisionOnDrag = true } = {}) {
  const ref = useRef({
    curX: 0,
    curY: 300,
    originalY: 300, // 记录原始Y位置（未调整前的位置）
    imageSize: { width: 0, height: 0 },
    lastAdjustedY: null, // 记录上一次调整后的位置
  });
  // 添加碰撞检测计数器
  const collisionRef = useRef({
    count: 0,
    direction: 'up',
  });
  const [posX, setPosX] = useState(ref.current.curX);
  const [posY, setPosy] = useState(ref.current.curY);

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;
  };

  // 碰撞检测函数，计算需要调整的距离
  const checkCollision = useCallback(
    (floatRect) => {
      if (!coverData || !floatRect) {
        return null; // 返回null表示不需要移动
      }

      const { top: floatTop, height: floatHeight } = floatRect;
      const { top: targetTop, height: targetHeight } = coverData;

      if (
        floatTop !== undefined &&
        floatHeight !== undefined &&
        targetTop !== undefined &&
        targetHeight !== undefined
      ) {
        const floatBottom = floatTop + floatHeight;
        const targetBottom = targetTop + targetHeight;

        // 检查是否有重叠
        const hasOverlap = floatBottom > targetTop && floatTop < targetBottom;
        console.log('碰撞检测：', hasOverlap);
        if (hasOverlap) {
          // 计算应该设置的新Y位置（绝对位置，不再基于当前posY）
          let newY;
          let actualDirection;

          // 根据当前recorded方向决定移动方向
          if (collisionRef.current.direction === 'down') {
            // 强制向下移动 - 直接计算绝对位置
            newY = targetBottom + 100; // 目标底部下方10px
            actualDirection = 'down';
          } else {
            // 强制向上移动 - 直接计算绝对位置
            newY = targetTop - floatHeight - 100; // 目标顶部上方10px减去浮动元素高度
            actualDirection = 'up';
          }

          return { newY, direction: actualDirection };
        }
      }
      return null; // 返回null表示不需要移动
    },
    [coverData],
  );

  // 碰撞检测
  const handleDragCollisionCheck = useCallback(() => {
    if (!checkCollisionOnDrag || !selector) return;

    // 优化延迟检测，通过多次尝试确保DOM更新完成
    const attemptCheck = (attempt = 1, maxAttempts = 3, delay = 50) => {
      if (attempt > maxAttempts) {
        return;
      }

      setTimeout(() => {
        const query = Taro.createSelectorQuery();
        query.select(selector).boundingClientRect();
        query.exec((res) => {
          // 确保获取到的结果有效
          if (res && res[0] && res[0].top !== undefined && res[0].height !== undefined) {
            // 检查是否达到最大碰撞检测次数
            console.log(collisionRef.current);
            if (collisionRef.current.count >= 5) {
              // 达到次数限制，切换方向
              const newDirection = collisionRef.current.direction === 'up' ? 'down' : 'up';
              console.log('切换方向：', collisionRef.current.direction, '->', newDirection);
              collisionRef.current.direction = newDirection;
              collisionRef.current.count = 0; // 重置计数

              // 强制移动到屏幕上方或下方
              const { windowHeight } = getSystemInfoSync();
              const safeY = newDirection === 'down' ? windowHeight - 200 : 100;
              setPosy(safeY);
              return;
            }

            const result = checkCollision(res[0]);
            const needAdjust = result !== null;

            if (needAdjust) {
              // 如果当前移动方向与记录的相同，增加计数
              if (result.direction === collisionRef.current.direction) {
                collisionRef.current.count++;
              } else {
                // 方向改变，重置计数
                collisionRef.current.direction = result.direction;
                collisionRef.current.count = 1;
              }
              // 设置新位置
              setPosy(result.newY);
            } else {
              // 重置碰撞计数
              collisionRef.current.count = 0;
            }
          } else {
            // 延长每次重试的等待时间
            attemptCheck(attempt + 1, maxAttempts, delay * 1.5);
          }
        });
      }, delay);
    };

    // 开始首次尝试
    attemptCheck();
  }, [checkCollisionOnDrag, selector, checkCollision, posY]);

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线

    // 用户手动拖动时重置碰撞计数
    collisionRef.current.count = 0;
    // 保持direction不变，因为用户可能有意识地移动到某个方向

    // 更新当前位置
    setPosX(curX);
    setPosy(curY);

    // 拖拽结束后检测碰撞
    handleDragCollisionCheck();

    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      const elementCenterX = curX + imgWidth / 2;
      setPosX(elementCenterX < threshold ? 0 : screenWidth - imgWidth);
    }, 150);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  // 监听覆盖状态变化，应用位置调整
  useEffect(() => {
    if (!coverData || Object.keys(coverData).length === 0) return;

    // 当coverData变化时，延迟执行碰撞检测，确保DOM更新完成
    const timer = setTimeout(() => {
      handleDragCollisionCheck();
    }, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [coverData, handleDragCollisionCheck]);

  useEffect(() => {
    collisionRef.current = {
      count: 0,
      direction: 'up'
    };
  }, [coverData]);

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);
  const [coverData, setCoverData] = useState({}); // 当有selector时，需要获取selector位置，并设置patchTop

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    setPosReady(true);
    setCoverData(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  useEffect(() => {
    if (!selector || !active) return;
    triggerGetBoundingDebounce({ selector, component: false });
  }, [active, selector]);

  // moveArea样式
  const styles = useMemo(() => {
    // const { windowHeight: screenHeight } = getSystemInfoSync();
    // const _top = `${
    //   Math.max(posPatchTop, top + height) > screenHeight - 100
    //     ? top + height
    //     : Math.max(posPatchTop, top + height)
    // }px`;

    return { top: top + height + 'px', bottom: isTabPage ? '100px' : 0 };
  }, [top, height, isTabPage]);

  return {
    coverData,
    posReady,
    styles,
  };
}
