/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDidShowCom } from '@base/hooks/page';
import request from '@base/utils/request';
import { reportAnalytics } from '@base/utils/utils';
import { Image, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { Fragment, useEffect, useRef, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import { adNavigator } from '../sdk';
import './float.scss';

/**
 * 微信官方广告组件位 - 浮动;
 */
const Index = () => {
  const { isVip = false } = useSelector((state) => state.global);
  const [imgUrl, updateImgUrl] = useState('');
  const [size, updateSize] = useState({});
  const [hideAd, setHideAd] = useState(false); // 屏蔽广告
  const actionRef = useRef(); //当前广告数据
  const actionRefAds = useRef(); //全部广告数据
  const refreshRef = useRef(false); //刷新标志
  const reportKey = 'exp_result_ad';

  useEffect(() => {
    const { ad } = Taro.launchParams || {};
    if (ad == 0 || ad == 2) {
      setHideAd(true);
    }
  }, []);

  useEffect(() => {
    if (isVip) return;
    request({
      url: '/g_order_core/v2/mina/User/getTablePlaqueBanner',
      data: { type: 'record' },
      toastLoading: false,
      onThen: ({ code, data }) => {
        //兼容旧版单个数据结构返回时的问题---以后可以去掉
        if (isObject(data) && data.id) {
          data = [data];
        }
        if (code == 0 && isArray(data) && data.length > 0) {
          actionRefAds.current = {
            list: data,
            cIndex: 0,
          };
          getCurrentAd();
        }
      },
    });
  }, [isVip]);

  useDidShowCom(() => {
    if (refreshRef.current) {
      getCurrentAd('switch');
      refreshRef.current = false;
    }
  });

  const getCurrentAd = (action = 'init') => {
    let { list, cIndex } = actionRefAds.current;
    if (action == 'switch' && list.length <= 1) return;
    if (cIndex > list.length - 1) {
      cIndex = 0;
    }
    let data = list[cIndex];
    actionRef.current = data;
    const { imgUrl, title } = data || {};
    if (imgUrl) {
      reportAnalytics({
        key: reportKey,
        options: `展示广告-${title}`,
      });
      updateImgUrl(imgUrl);
    }
    actionRefAds.current.cIndex = cIndex + 1;
  };

  const handleImageLoad = (e) => {
    const {
      detail: { width, height },
    } = e;
    if (width && height) {
      updateSize({ width: `${width}px`, height: `${height}px` });
    }
  };

  // 关闭广告
  const handleClose = () => updateImgUrl('');

  // 点击广告
  const handleClickAd = () => {
    adNavigator({
      ...actionRef.current,
      report: {
        key: reportKey,
        options: `点击广告-${actionRef.current.title}`,
      },
    });
    refreshRef.current = true;
  };

  return !isVip && !hideAd ? (
    <View className='kb-ad-float'>
      {imgUrl ? (
        <Fragment>
          <View hoverClass='kb-hover-opacity' onClick={handleClickAd}>
            <Image
              lazyLoad
              className='kb-ad-float__image'
              mode='widthFix'
              src={imgUrl}
              onLoad={handleImageLoad}
              style={size}
            />
          </View>
          <View className='kb-ad-float__close' hoverClass='kb-hover-opacity' onClick={handleClose}>
            X
          </View>
        </Fragment>
      ) : null}
    </View>
  ) : (
    <Fragment />
  );
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
