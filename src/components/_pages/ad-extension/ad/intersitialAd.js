/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { frequencyLimitByMinute, reportAnalytics } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import { createInterstitialAdWrap } from '../sdk';

export function intersitialAd({ closeShield, adId, storageKey, minutes }) {
  createInterstitialAdWrap(() => {
    if (process.env.PLATFORM_ENV == 'weapp') {
      const intersitialAd = Taro.createInterstitialAd({
        adUnitId: adId,
      });
      frequencyLimitByMinute('check', storageKey, minutes).then((status) => {
        if (!status) {
          intersitialAd.onLoad(() => {
            reportAnalytics({
              key: 'cpad_onload',
            });
            intersitialAd.show().catch((err) => {
              console.log('cpad_err', err);
              reportAnalytics({
                key: 'cpad_errormsg',
                errormsg: err.errMsg,
              });
            });
            frequencyLimitByMinute('limit', storageKey);
          });
        }
      });
      return intersitialAd;
    }
  }, closeShield);
  // return intersitialAd.destroy()
}
