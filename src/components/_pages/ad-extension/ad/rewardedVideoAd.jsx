/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';

let toastIns;
const showAd = (ins) => {
  if (process.env.PLATFORM_ENV === 'weapp') {
    ins.show().catch(() => {
      toastIns = Taro.kbToast({
        status: 'loading',
      });
      ins
        .load()
        .then(() => {
          toastIns && toastIns.close();
          ins.show();
        })
        .catch(() => {
          toastIns && toastIns.close();
          Taro.reportAnalytics('rewarded_video_ad', {
            err: '激励视频 广告显示失败',
          });
        });
    });
  }
};

const createRewardedVideoAd = (data, component) => {
  if (process.env.PLATFORM_ENV === 'weapp') {
    const { adUnitId, auto = true, tips = '播放中途退出，无法获得奖励' } = data;
    if (Taro.createRewardedVideoAd) {
      if (component.rewardedVideoAdIns) {
        if (auto) {
          showAd(component.rewardedVideoAdIns);
        }
        return;
      }
      const rewardedVideoAdIns = Taro.createRewardedVideoAd({
        adUnitId,
      });
      if (auto) {
        showAd(rewardedVideoAdIns);
      }
      rewardedVideoAdIns.onError((res) => {
        Taro.reportAnalytics('rewarded_video_ad', {
          err: `${res.errCode} ${res.errMsg}`,
          adUnitId,
        });
      });
      rewardedVideoAdIns.onClose((res) => {
        if (res && res.isEnded) {
          //  已观看完毕
          Taro.navigateTo({
            url: '/pages/help/reward/reward',
          });
        } else {
          Taro.kbModal({
            content: tips,
          });
        }
      });
      component.rewardedVideoAdIns = rewardedVideoAdIns;
      return rewardedVideoAdIns;
    } else if (auto) {
      Taro.kbModal({
        content: '您的微信基础库版本太低，请升级微信版本后再尝试',
      });
    }
  }
};

export default createRewardedVideoAd;
