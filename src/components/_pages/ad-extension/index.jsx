/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getAdConfig } from '@/components/_pages/ad-extension/_utils';
import { useUpdate } from '@base/hooks/page';
import { getStorage, setStorage } from '@base/utils/utils';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Fragment, useRef, useState } from '@tarojs/taro';
import classNames from 'classnames';
import dayjs from 'dayjs';
import calendar from 'dayjs/plugin/calendar';
import isArray from 'lodash/isArray';
import { AtCurtain } from 'taro-ui';
import { check } from '~base/utils/rules';
import './index.scss';
import { adNavigator } from './sdk';

dayjs.extend(calendar);
const getAdminPopAdsStorageKey = (position) => `adminPopAds_${position}`;

const Index = (props) => {
  const actionRef = useRef({});
  const { data, className, showRemark, imgPreview, onLoad, onClick } = props;
  const [ads, updateAds] = useState(null);
  const [popAds, upPopAds] = useState(null);
  const [popShow, upPopShow] = useState(false);
  const [previewImg, upPreviewImg] = useState(null);
  const popAdsRef = useRef(null);
  // 获取广告
  const getAdList = () => {
    // actionRef.current.loaded = true;
    getAdConfig(
      process.env.MODE_ENV === 'wkd'
        ? data
        : {
            position: process.env.PLATFORM_ENV == 'weapp' ? 1 : 7,
            ...data,
          },
    ).then((list) => {
      const isPopAds = (title) => /^(T|t)-/.test(title);
      const isValidList = (list) => isArray(list) && list.length > 0;
      let bannerAds = [];
      let popAds = [];
      isValidList(list) &&
        list.forEach((item) => {
          if (!isPopAds(item.title)) {
            bannerAds.push(item);
          } else {
            popAds.push(item);
          }
        });
      updateAds(isValidList(bannerAds) ? bannerAds : null);
      onLoad && onLoad();
      if (isValidList(popAds)) {
        upPopAds(popAds[0]);
        showPopAds();
      }
    });
  };

  const updatePopInfo = (preInfo) => {
    const { position = 'query_banner' } = data || {};
    // 超过2次或者已经弹出2次｜｜距离上次弹出大于30分
    const currentDate = dayjs().format('YYYY-MM-DD HH:mm');
    const { count = 0, date = 0 } = preInfo || {};
    const currentDay = currentDate.split(' ')[0];
    const preDay = date ? date.split(' ')[0] : 0;
    const isDayDifferent = Math.abs(dayjs(currentDay).diff(dayjs(preDay), 'days')) >= 1;
    const isMinuteDifferent = Math.abs(dayjs(currentDate).diff(dayjs(date), 'minute')) >= 30;

    if ((count < 2 && isMinuteDifferent) || isDayDifferent) {
      const nextData = {
        date: currentDate,
        count: isDayDifferent ? 1 : count + 1,
      };
      upPopShow(true);
      setStorage({
        key: getAdminPopAdsStorageKey(position),
        data: nextData,
      });
      popAdsRef.current = nextData;
    }
  };

  const showPopAds = () => {
    const { position = 'query_banner' } = data || {};
    const preData = popAdsRef.current;
    if (!preData) {
      getStorage({
        key: getAdminPopAdsStorageKey(position),
        complete(res) {
          const { data: { date, count } = {} } = res.data || {};
          updatePopInfo({ date, count });
        },
      });
    } else {
      updatePopInfo(preData);
    }
  };
  const handlePopAdsClose = () => {
    upPopShow(false);
  };

  // 点击跳转
  const handleClick = (item) => {
    handlePopAdsClose();
    if (onClick) {
      onClick(item);
    }
    const { adUrl } = item || {};
    if (imgPreview && check('imgUrl', adUrl).code == 0) {
      upPreviewImg(adUrl);
      return;
    }
    adNavigator(item);
  };

  useUpdate(
    (loginData) => {
      const { logined } = loginData || {};
      if (!logined || actionRef.current.loaded) return;
      if (process.env.PLATFORM_ENV !== 'swan') {
        getAdList();
      }
    },
    [data],
  );

  const wrapCls = classNames('kb-ads__swiper', className);

  return (
    <Fragment>
      {ads ? (
        <View className='kb-ads'>
          <Swiper
            className={wrapCls}
            autoplay
            circular
            indicatorDots={ads.length > 1}
            indicatorColor='rgba(255,255,255,0.5)'
            indicatorActiveColor='rgba(255,90,122,0.5)'
          >
            {ads.map((item) => {
              return (
                <SwiperItem key={item.id}>
                  <View
                    className='kb-ads__swiper--item'
                    hoverClass='kb-hover-opacity'
                    onClick={handleClick.bind(null, item)}
                  >
                    <Image
                      className='kb-ads__swiper--image'
                      src={item.imgUrl}
                      showMenuByLongpress
                    />
                  </View>
                </SwiperItem>
              );
            })}
          </Swiper>
        </View>
      ) : null}
      {process.env.PLATFORM_ENV === 'alipay' && popAds ? (
        <AtCurtain
          onClose={handlePopAdsClose}
          isOpened={popShow && popAds ? true : false}
          closeBtnPosition='top-right'
        >
          <Image
            style='width:100%;height:auto'
            mode='widthFix'
            src={popAds.imgUrl}
            onClick={handleClick.bind(null, popAds)}
          />
          {showRemark && <View className='kb-color__white kb-size__base2'>{popAds.remark}</View>}
        </AtCurtain>
      ) : null}
      {imgPreview && (
        <AtCurtain
          onClose={() => upPreviewImg(null)}
          isOpened={!!previewImg}
          closeBtnPosition='bottom'
        >
          <Image
            style='width:100%;height:auto'
            mode='widthFix'
            src={previewImg}
            showMenuByLongpress
          />
        </AtCurtain>
      )}
    </Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default connect(({ global }) => ({
  loginData: global.loginData,
}))(Index);
