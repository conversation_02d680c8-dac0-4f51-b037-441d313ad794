/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbBarcode from '@base/components/canvas/barcode';
import KbLongList from '@base/components/long-list';
import request from '@base/utils/request';
import { importFieldHide, noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { Component, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import isString from 'lodash/isString';
import { AtButton } from 'taro-ui';
import './index.scss';

class Index extends Component {
  static defaultProps = {
    onListReady: noop,
    onScanSuccess: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    this.listData = {
      api: {
        url:
          process.env.MODE_ENV === 'wkd'
            ? '/g_wkd/v2/mina/Mark/userPickUpCode'
            : '/api/weixin/mini/user/Mark/userPickUpCode',
        onThen: (_, { code, data = {} }) => {
          const { mobile = '', code: id_code } = data || {};
          if (code == 0) {
            this.setState({
              text: id_code,
              mobile: isString(mobile) ? mobile : '',
            });
          }
        },
      },
    };
    this.state = {
      text: '',
      hide: true,
      mobile: '',
      bgInfo: {
        count: 9,
        pageBg: `rgba(0,0,0,0.${9})`,
        textColor: '#fff',
        barcodeBg: '#ccc',
      },
    };

    this.close = false;
  }
  componentDidShow() {
    const { action } = this.props;
    if (action === 'turnstiles') {
      this.loopQueryIdCodeUserStatus();
    }
    this.changeBgcolor();
  }

  componentDidUpdate(prevProps) {
    const { action: preAction } = prevProps;
    const { action } = this.props;
    if (action === 'turnstiles' && action !== preAction) {
      this.loopQueryIdCodeUserStatus();
    }
  }

  componentWillUnmount() {
    this.close = true;
    this.timer && clearTimeout(this.timer);
    this.bgTimer && clearTimeout(this.bgTimer);
  }

  // 后台反馈用户有上万次请求，增加小程序隐藏时清除定时器。
  componentDidHide() {
    this.close = true;
    this.timer && clearTimeout(this.timer);
    this.bgTimer && clearTimeout(this.bgTimer);
  }

  handleRefresh = () => this.listIns.loader();
  // 列表准备就绪
  handleReady = (ins) => {
    const { onListReady } = this.props;
    this.listIns = ins;
    onListReady(ins);
  };

  changeBgcolor = () => {
    const that = this;
    if (this.bgTimer) {
      clearTimeout(this.bgTimer);
    }
    that.bgTimer = setInterval(() => {
      const { bgInfo } = that.state;
      const count = (bgInfo.count + 1) % 9;
      that.setState({
        bgInfo: {
          count,
          pageBg: `rgba(0,0,0,0.${count})`,
          textColor: count < 4 ? '#666' : '#fff',
          barcodeBg: count < 4 ? '#ccc' : '#fff',
        },
      });
    }, 500);
  };

  // 去实名
  handleNavToRealname = () => {
    const { realname } = this.props;
    const { phone } = realname || {};
    Taro.navigator({
      url: 'realname',
      options: {
        phone,
      },
    });
  };
  getIdCodeUseStatus = () => {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/cloudPrint/order/v1/user/Mark/canDisableUserMark',
        toastLoading: false,
        data: { code: this.state.text },
        onThen: (res) => {
          const { code, data = {} } = res;
          if (code === 0 || this.close) {
            resolve({
              status: this.close ? '-1' : '0',
              data: data.dak_id
                ? {
                    ...data,
                    dakId: data.dak_id,
                    picktype: 'gate',
                    source: 'idCode',
                  }
                : {},
            });
          } else {
            reject();
          }
        },
        onStop: () => {
          reject();
        },
      });
    });
  };

  loopQueryIdCodeUserStatus() {
    if (process.env.MODE_ENV !== 'wkd') {
      this.getIdCodeUseStatus().then(
        ({ status, data }) => {
          status === '0' &&
            Taro.kbToast({
              text: '扫描成功',
              onThen: () => {
                this.props.onScanReady(data);
              },
            });
        },
        () => {
          this.timer && clearTimeout(this.timer);
          this.timer = setTimeout(() => {
            this.loopQueryIdCodeUserStatus();
          }, 1000);
        },
      );
    }
  }
  // 查看数字
  handleCheck = () => {
    const { hide } = this.state;
    if (hide) {
      Taro.kbModal({
        content: `请不要把这串数字给别人，该数字只用于${
          process.env.MODE_ENV !== 'third.post' ? '快宝驿站' : '中邮驿站'
        }出库时，展示给出库仪扫描`,
        confirmText: '知道了',
        onConfirm: () => {
          this.setState({
            hide: false,
          });
        },
      });
    } else {
      this.setState({
        hide: true,
      });
    }
  };
  handleNavBack() {
    this.props.onScanReady();
  }
  render() {
    const { hide, text, mobile, bgInfo } = this.state;
    let { buttomTip, action, realname } = this.props;
    const { realnamed } = realname || {};
    buttomTip = action === 'turnstiles' ? '' : buttomTip;
    const barcodeCls = classNames('kb-IDcode__barcode kb-IDcode__barcode--full');

    const { pageBg, barcodeBg, textColor } = bgInfo || {};

    return (
      <Fragment>
        <KbLongList data={this.listData} onReady={this.handleReady}>
          <View className={barcodeCls} style={{ background: pageBg }}>
            <View className='kb-full-header'>
              <View className='kb-id-auth'>
                <Text style={{ color: textColor }}>身份码</Text>
                <AtButton
                  onClick={this.handleNavToRealname}
                  circle
                  type='secondary'
                  // disabled={realnamed}
                  className='kb-button__mini kb-id-auth-tag kb-margin-xs-l'
                >
                  {realnamed ? '已实名认证' : '未实名认证,去实名 >'}
                </AtButton>
              </View>
              {action === 'turnstiles' ? (
                <Fragment>
                  <View className='kb-turnstiles-close' onClick={this.handleNavBack} />
                </Fragment>
              ) : null}
            </View>
            <View className='kb-IDcode__barcode--content' style={{ color: textColor }}>
              {mobile && (
                <View className='kb-size__80 kb-margin-lg-b kb-text__center'>{mobile}</View>
              )}
              <View className='kb-IDcode__barcode--image'>
                <KbBarcode
                  style={{ borderColor: barcodeBg }}
                  barcodeBg={barcodeBg}
                  text={text}
                  className='kb-IDcode__barcode--code'
                />
              </View>
              <View
                className='at-row at-row__align--center at-row__justify--center kb-IDcode__barcode--text'
                style={{ color: textColor }}
              >
                <Text>{hide ? importFieldHide(text, 8) : text}</Text>
                <View hoverClass='kb-hover-opacity' onClick={this.handleCheck}>
                  {hide ? '查看' : '隐藏'}数字
                </View>
                <View className='kb-spacing-md-l'>
                  <AtButton className='kb-button__link' onClick={this.handleRefresh}>
                    刷新
                  </AtButton>
                </View>
              </View>
              {buttomTip ? <View className='kb-IDcode__barcode--tips'>{buttomTip}</View> : null}
            </View>
          </View>
        </KbLongList>
      </Fragment>
    );
  }
}

export default Index;
