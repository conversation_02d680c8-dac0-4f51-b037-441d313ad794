/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-IDcode {
  &__footer {
    padding: $spacing-v-md $spacing-h-md;
    text-align: center;
    background-color: $color-white;
  }

  &__barcode {
    padding: 5 * $spacing-v-md $spacing-h-md;
    background-color: $color-grey-5;

    &--open {
      padding-bottom: 3 * $spacing-v-md;
      text-align: right;
    }

    &--text {
      padding-top: 3 * $spacing-v-md;
      line-height: 30px;
    }

    &--tips {
      padding-top: $spacing-v-md;
      font-weight: bold;
      text-align: center;
    }

    &--image {
      width: 95%;
      height: 300px;
    }

    &--code {
      border-width: $spacing-h-md * 1.2 $spacing-h-md * 1.2 !important;
    }

    &--content {
      transition: transform $animation-duration-slow;
      .kb-size__80 {
        font-size: 80px;
      }
    }

    &--full {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 0;
      overflow: hidden;
      color: $color-white;
      background: $color-black-1;
    }

    &--full &--image {
      height: 400px;
    }

    &--full &--content {
      box-sizing: border-box;
      width: calc(97vh - 150px);
      padding: 0 4 * $spacing-h-md 0 5 * $spacing-h-md;
      transform: rotate(90deg);
    }
  }
}

.kb-id-auth {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  padding: $spacing-h-lg 0 0 $spacing-h-lg;
  font-size: $font-size-lg;
  &-tag {
    color: $color-orange-lighter !important;
    border-color: $color-orange-lighter !important;
  }
}
.kb-turnstiles-close {
  // border: solid 4px $color-grey-4;
  position: absolute;
  top: 20px;
  right: 40px;
  z-index: 10;
  width: 50px;
  height: 50px;
  // border-radius: $border-radius-arc;
  overflow: hidden;
  &::after {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 4px;
    height: 100%;
    margin: -$width-base;
    background-color: $color-grey-3;
    transform: rotate(45deg);
    content: '';
  }
  &::before {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 4px;
    height: 100%;
    margin: -$width-base;
    background-color: $color-grey-3;
    transform: rotate(-45deg);
    content: '';
  }
}
