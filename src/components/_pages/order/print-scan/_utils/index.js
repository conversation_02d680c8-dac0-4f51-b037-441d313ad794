/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { check } from '@base/utils/rules';
import { getPage } from '@base/utils/utils';
import { qrCodeParse, checkCodeInfo } from '@/utils/scan';

const scanFail = () => {
  const _this = getPage();
  _this.scanLock = false;
  Taro.kbToast({
    text: '请您按照提示扫描正确的二维码信息完成校验!',
  });
};

export const handleScanCode = (ev) => {
  const _this = getPage();
  const { orderData } = _this.state;
  if (_this.scanLock) return;
  console.log('handleScanCode.ev==>', ev);
  const { type, result } = ev.detail || {};
  if (['qrcode', 'QR_CODE'].includes(type) && result && check('url', result).code == 0) {
    Taro.vibrateShort({ type: 'light' });
    _this.scanLock = true;
    qrCodeParse(result, _this, { onlyParse: true })
      .then((codeInfo) => {
        console.log('codeInfo==>', codeInfo);
        checkCodeInfo(orderData, codeInfo)
          .then((res) => {
            if (res) {
              _this.scanLock = false;
              Taro.navigator({
                post: {
                  type: 'printScan',
                },
              });
            } else {
              scanFail();
            }
          })
          .catch(scanFail);
      })
      .catch(scanFail);
  } else {
    scanFail();
  }
};

export const handleError = (ev) => {
  console.log('handleError.ev==>', ev);
};
