/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.o_l_delivery_item {
  &__header {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-v-md;
    border-bottom: $border-lightest;
  }
  &__name {
    display: flex;
    flex: 1;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: flex-start;
    font-size: $font-size-lg;
  }
  &__content {
    display: flex;
    flex-direction: column;
    gap: $spacing-v-sm;
    align-items: center;
    justify-content: flex-start;
    padding: $spacing-v-md;
    // border-bottom: $border-lightest;
  }
  &__address {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    color: $color-grey-0;
    font-size: $font-size-lg;
    &__time {
      flex-shrink: 1;
    }
  }
  &__pickupCode {
    width: 100%;
    color: $color-grey-1;
    font-size: $font-size-base;
    text-align: left;
  }
  &__time {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    color: $color-grey-3;
    font-size: $font-size-base;
  }
}
