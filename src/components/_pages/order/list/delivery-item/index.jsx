/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro, { useMemo } from '@tarojs/taro';
// import DetailDeliveryFooter from '../../detail-delivery/footer';
import { AtIcon } from 'taro-ui';
import { dateCalendar, makePhoneCall } from '@base/utils/utils';
import { DELIVERY_ORDER_STATUS } from '../../detail-delivery/_utils/utils';
import './index.scss';

const OrderListDeliveryItem = (props) => {
  const {
    data,
    //  handleUpdate
  } = props;

  const {
    inn_name,
    order_status,
    first_level_address,
    second_level_address,
    reserve_address,
    reserve_arrival_time,
    pickup_code = [],
    create_time,
    package_count,
    paid_price,
    inn_phone = '',
  } = data;

  const statusObj = useMemo(
    () => DELIVERY_ORDER_STATUS.find((i) => i.key == order_status) || {},
    [order_status],
  );

  const onNavigator = () => {
    Taro.navigator({
      url: 'order/detail-delivery',
      options: {
        order_id: data.order_id,
      },
    });
  };

  const pickCode = Array.isArray(pickup_code) ? pickup_code.join('，') : [];

  return (
    <View>
      <View className='o_l_delivery_item' onClick={onNavigator}>
        <View className='o_l_delivery_item__header'>
          <View className='o_l_delivery_item__name'>
            <Text>{inn_name}</Text>
            {inn_phone && (
              <View onClick={(event) => event.stopPropagation()}>
                <AtIcon
                  prefixClass='kb-icon'
                  value='phone'
                  className='kb-size__base2 kb-color__brand'
                  onClick={makePhoneCall.bind(null, inn_phone)}
                />
              </View>
            )}
          </View>
          {statusObj.label && (
            <View className={`o_l_delivery_item__status ${statusObj.cnName}`}>
              {statusObj.label}
            </View>
          )}
        </View>
        <View className='o_l_delivery_item__content'>
          <View className='o_l_delivery_item__address'>
            <View className='o_l_delivery_item__address__co'>
              {first_level_address}-{second_level_address}
              {reserve_address}
            </View>
            <View className='o_l_delivery_item__address__time'>
              {dateCalendar(reserve_arrival_time, { timer: true })} 送达
            </View>
          </View>
          <View className='o_l_delivery_item__pickupCode'>取件码：{pickCode}</View>
          <View className='o_l_delivery_item__time'>
            <View>{create_time}</View>
            <View>
              共{package_count}件，合计<Text className='kb-color__red'>{paid_price}</Text>元
            </View>
          </View>
        </View>
      </View>
      {/* <DetailDeliveryFooter orderInfo={data} handleUpdate={handleUpdate} /> */}
    </View>
  );
};

OrderListDeliveryItem.defaultProps = {
  data: {},
  handleUpdate: () => {},
};

OrderListDeliveryItem.options = {
  addGlobalClass: true,
};

export default OrderListDeliveryItem;
