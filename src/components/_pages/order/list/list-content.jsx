/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import {
  formatResponseOrderList,
  getUrlAndDataOrderDetail,
  mergeOrderList,
  orderListInit,
} from '@/components/_pages/order/_utils/order.list';
import KbAffix from '@base/components/affix';
import KbAffixItem from '@base/components/affix/item';
import KbLongList from '@base/components/long-list';
import { extendMemo } from '@base/components/_utils';
import { noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { useDispatch, useSelector } from '@tarojs/redux';
import Taro, { Fragment, useEffect, useRef, useState } from '@tarojs/taro';
import KbEmpty from '@base/components/empty';
import { orderAction } from '../_utils';
import './list-content.scss';
import KbOrderListItem from './list-item';
import OrderListCabinetItem from './cabinet-item';
import OrderListDeliveryItem from './delivery-item';
import { REFRESH_KEY_ORDER_DELIVERY, refreshControl } from '~/utils/refresh-control';
import { useDidShowCom } from '~base/hooks/page';

const Index = (props) => {
  const { show, active, searchData, selectted, tabKey, type, customer_id } = props;
  const dispatch = useDispatch();
  const actionRef = useRef({});
  const listRef = useRef({});
  const [list, updateList] = useState(null);
  const { brands, brands_tcjs } = useSelector((state) => state.global);
  const { pageKey, pageSizeKey, extraData, noMoreText, orderType, orderDate } = orderListInit({
    tabKey,
    type,
  });

  const listData = {
    pageKey,
    api: {
      ...getUrlAndDataOrderDetail({
        pageSizeKey,
        extraData,
        tabKey,
        type,
      }),
      moreDataMerger: mergeOrderList,
      formatResponse: formatResponseOrderList({
        orderType,
        pageKey,
      }),
      formatRequest: (req) => {
        // app分享增加max_order_id请求参数
        let max_order_id = '';
        if (orderType == 'send' && req[pageKey] > 1 && listRef) {
          max_order_id = listRef.current && listRef.current.max_order_id;
        }
        if (
          !req.extra_info ||
          (req.extra_info &&
            !Object.prototype.hasOwnProperty.call(req.extra_info, 'wait_pickup') &&
            tabKey == 'cabinet')
        ) {
          req.extra_info = {
            ...(req.extra_info || {}),
            wait_pickup: '1',
          };
          delete req.extra_info.wait_pay;
        }
        return {
          customer_id,
          max_order_id,
          ...req,
        };
      },
      nonceKey: 'customer_id,courier_id',
      onThen: (data, ...arg) => {
        const [res = {}] = arg || [];
        if (res && res.data && res.data.max_order_id && orderType == 'send') {
          listRef.current.max_order_id = res.data.max_order_id;
        }
        updateList(data);
        props.onGetted(data, ...arg);
      },
    },
  };

  useEffect(() => {
    if (show) {
      dispatch(get());
    }
  }, [dispatch, show]);

  const triggerRefresh = (...arg) => actionRef.current.listIns.loader(...arg);

  // 点击按钮
  const handleUpdate = (action) => {
    if (action === 'cancel') {
      triggerRefresh();
    }
  };

  // 跳转订单详情
  const handleClickItem = (item) => {
    const { order_id, disabled } = item;
    if (!order_id) return;
    if (selectted) {
      if (disabled) return;
      props.onClickItem(item);
      return;
    }
    orderAction({
      action: 'detail',
      data: {
        ...item,
        customer_id,
        orderTypeAndDate: {
          type: orderType,
          date: orderDate,
        },
      },
    });
  };

  // 列表准备就绪
  const handleReady = (ins) => {
    actionRef.current.listIns = ins;
    props.onReady(ins);
  };

  useDidShowCom(() => {
    if (tabKey == 'delivery') {
      if (refreshControl(REFRESH_KEY_ORDER_DELIVERY, 'check')) {
        triggerRefresh();
      }
    }
  });
  const handleNavigator = () => {
    Taro.navigator({
      url: 'query',
    });
  };

  return (
    <KbAffix active={show}>
      <KbLongList
        active={active && searchData}
        data={listData}
        enableMore
        onReady={handleReady}
        noMoreText={noMoreText}
        useRenderEmpty={tabKey == 'cabinet'}
        renderEmpty={
          <View className='kb-orderListCabinetEmpty'>
            <KbEmpty centered description='暂无匹配订单'>
              <View className='kb-orderListCabinetEmpty__text' onClick={handleNavigator}>
                点错了，我想取快递
              </View>
            </KbEmpty>
          </View>
        }
      >
        {list && (
          <View className='kb-list kb-list__order'>
            {list.map((listItem) => (
              <KbAffixItem
                key={listItem.title + listItem.list.length}
                count={listItem.list.length}
                title={listItem.title}
                renderTitle={
                  <Fragment>
                    {listItem.title && (
                      <View className='kb-list__title'>
                        <Text className='kb-list__title--text'>{listItem.title}</Text>
                      </View>
                    )}
                  </Fragment>
                }
              >
                <View>
                  {listItem.list.map((item, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <View className='kb-list__item--wrapper' key={`${item.order_id}-${index}`}>
                      {tabKey == 'cabinet' ? (
                        <OrderListCabinetItem data={item} handleUpdate={handleUpdate} />
                      ) : tabKey == 'delivery' ? (
                        <OrderListDeliveryItem data={item} handleUpdate={handleUpdate} />
                      ) : (
                        <KbOrderListItem
                          data={item}
                          orderType={orderType}
                          orderDate={orderDate}
                          brands={
                            orderType === 'tcjs' && item.is_open_order == 1 ? brands_tcjs : brands
                          }
                          onClick={handleClickItem}
                          onUpdate={handleUpdate}
                        />
                      )}
                    </View>
                  ))}
                </View>
              </KbAffixItem>
            ))}
          </View>
        )}
      </KbLongList>
    </KbAffix>
  );
};

Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  searchData: null,
  active: false,
  show: true,
  onGetted: noop,
  onClickItem: noop,
  onReady: noop,
  selectted: null,
  type: 'normal', // all tab切换所有列表，history tab切换历史列表 , normal 单列表，company：企业订单
  customer_id: '', // 大客户id
  handleSwitchTab: noop,
};

export default extendMemo(Index);
