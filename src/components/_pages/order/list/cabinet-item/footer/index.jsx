/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import DetailCabinetPay from '../../../detail-cabinet/footer/pay';
import DetailCabinetPickup from '../../../detail-cabinet/footer/pickup';
import DetailCabinetCancel from '../../../detail-cabinet/footer/cancel';
import DetailCabinetOpen from '../../../detail-cabinet/footer/open';
import OrderListCabinetItemOrderTime from '../order-time';

import './index.scss';

const OrderListCabinetItemFooter = (props) => {
  const { orderInfo = {}, handleUpdate } = props;

  const { order_status = '0' } = orderInfo || {};

  const is_show_footer_keys = ['-9', '0'];

  return !is_show_footer_keys.includes(order_status) ? (
    <View className='kb-spacing-md-l kb-spacing-md-r'>
      <View className='olciFooter'>
        <OrderListCabinetItemOrderTime orderInfo={orderInfo} handleUpdate={handleUpdate} />
        <View className='olciFooter__operate'>
          <DetailCabinetOpen orderInfo={orderInfo} handleUpdate={handleUpdate} />
          <DetailCabinetCancel orderInfo={orderInfo} handleUpdate={handleUpdate} />
          <DetailCabinetPay orderInfo={orderInfo} handleUpdate={handleUpdate} />
          <DetailCabinetPickup orderInfo={orderInfo} handleUpdate={handleUpdate} />
        </View>
      </View>
    </View>
  ) : null;
};

OrderListCabinetItemFooter.options = {
  addGlobalClass: true,
};

export default OrderListCabinetItemFooter;
