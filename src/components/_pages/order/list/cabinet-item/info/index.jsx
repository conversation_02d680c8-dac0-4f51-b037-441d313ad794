/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatPrice, formatSecondDate } from '../../../detail-cabinet/_utils/utils';

import './index.scss';

const OrderListCabinetItemInfo = (props) => {
  const { orderInfo, onNavigator } = props;
  const { order_status, pickup_code, goods, storage_duration, wait_pay_price, pay_price } =
    orderInfo || {};

  return (
    <View className='olciInfo' onClick={onNavigator}>
      <View className='olciInfo__content'>
        <View className='olciInfo__pickupCode'>{pickup_code}</View>
        <View className='olciInfo__tags'>
          {goods && <View className='olciInfo__tags__item'>{goods}</View>}
          <View className='olciInfo__tags__item'>{formatSecondDate(storage_duration)}</View>
        </View>
      </View>
      <View className='olciInfo__price'>
        ¥{formatPrice(order_status == '1' ? wait_pay_price : pay_price)}
      </View>
    </View>
  );
};

OrderListCabinetItemInfo.options = {
  addGlobalClass: true,
};

export default OrderListCabinetItemInfo;
