/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import OrderListCabinetItemHeader from './header';
import OrderListCabinetItemInfo from './info';
import OrderListCabinetItemFooter from './footer';
import { formatCabinetOrderInfo } from '../../detail-cabinet/_utils/utils';

const OrderListCabinetItem = (props) => {
  const { data = {}, handleUpdate } = props;

  const onNavigator = () => {
    Taro.navigator({
      url: 'order/detail-cabinet',
      options: {
        order_id: data.order_id,
      },
    });
  };

  const orderInfo = formatCabinetOrderInfo(data);

  return (
    <View>
      <OrderListCabinetItemHeader
        orderInfo={orderInfo}
        onNavigator={onNavigator}
        handleUpdate={handleUpdate}
      />
      <OrderListCabinetItemInfo orderInfo={orderInfo} onNavigator={onNavigator} />
      <OrderListCabinetItemFooter orderInfo={orderInfo} handleUpdate={handleUpdate} />
    </View>
  );
};

export default OrderListCabinetItem;
