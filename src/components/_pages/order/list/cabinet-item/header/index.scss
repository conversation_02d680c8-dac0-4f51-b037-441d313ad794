/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.olciHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-v-md;
  &__name {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: flex-start;
    color: $color-grey-0;
    font-size: $font-size-lg;
  }
  &__cabinet {
    width: 50px;
    height: 50px;
  }
  &__timer {
    width: 30px;
    height: 30px;
  }
  &__status {
    display: flex;
    gap: $spacing-v-md;
    align-items: first baseline;
    justify-content: center;
    font-size: $font-size-lg;
  }
}
