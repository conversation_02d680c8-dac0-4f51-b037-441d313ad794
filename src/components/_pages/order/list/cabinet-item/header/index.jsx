/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import Taro, { useMemo } from '@tarojs/taro';
import { CABINET_ORDER_STATUS } from '../../../detail-cabinet/_utils/utils';

import './index.scss';

const OrderListCabinetItemHeader = (props) => {
  const { orderInfo, onNavigator } = props;
  const { cabinet_name, order_status } = orderInfo || {};

  const statusObj = useMemo(
    () => CABINET_ORDER_STATUS.find((i) => i.key == order_status) || {},
    [order_status],
  );

  return (
    <View className='olciHeader' onClick={onNavigator}>
      <View className='olciHeader__name'>
        <Image
          className='olciHeader__cabinet'
          src='//cdn-img.kuaidihelp.com/yz/miniapp/icon_cabinet.png?v=20240712'
        />
        <View>{cabinet_name}</View>
      </View>
      <View className={`olciHeader__status ${statusObj.cnName}`}>{statusObj.label}</View>
    </View>
  );
};

OrderListCabinetItemHeader.options = {
  addGlobalClass: true,
};

export default OrderListCabinetItemHeader;
