/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import KbCountDownClock from '@base/components/count-down-clock';
import classNames from 'classnames';
import { formatSecondDate } from '../../../detail-cabinet/_utils/utils';

import './index.scss';

const OrderListCabinetItemOrderTime = (props) => {
  const { handleUpdate, orderInfo } = props;

  const {
    show_time,
    is_show_pay_time,
    order_over_second,
    order_duration_second,
    order_status,
    is_show_order_over,
    pay_time,
  } = orderInfo || {};

  const handleEnd = () => {
    handleUpdate('cancel');
  };

  return show_time ? (
    <View className='order_time'>
      {/* <Image
        className='order_time__icon'
        src={
          order_status == '4'
            ? '//cdn-img.kuaidihelp.com/yz/miniapp/icon_countdown_grey.png'
            : '//cdn-img.kuaidihelp.com/yz/miniapp/icon_countdown_red.png'
        }
      /> */}
      {is_show_pay_time ? (
        <KbCountDownClock
          end={pay_time}
          onEnd={handleEnd}
          theme='red'
          cname='order_time__countDown'
        />
      ) : (
        <View
          className={classNames({
            'kb-color__red': ['2', '3'].includes(order_status),
            'kb-color__grey': order_status == '4',
          })}
        >
          {is_show_order_over
            ? `超时${formatSecondDate(order_over_second)}`
            : formatSecondDate(order_duration_second)}
        </View>
      )}
    </View>
  ) : (
    <View />
  );
};

OrderListCabinetItemOrderTime.options = {
  addGlobalClass: true,
};

export default OrderListCabinetItemOrderTime;
