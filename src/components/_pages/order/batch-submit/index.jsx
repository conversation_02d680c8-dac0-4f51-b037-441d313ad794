/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useEffect } from '@tarojs/taro';
import './index.scss';

const Index = (props) => {
  const { actionRef } = props;

  useEffect(() => {
    actionRef.current = {};
  }, []);

  return <View className='kb-batch-submit'></View>;
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {};

export default Index;
