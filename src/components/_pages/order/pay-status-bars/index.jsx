/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import { useOrderListStatusBars } from './useOrderListStatusBars';
import './index.scss';

const OrderListStatusBars = (props) => {
  const { activeKeys, buttons, handleBars } = useOrderListStatusBars(props);

  return (
    <View className='kb_statusBars'>
      {buttons.map((item) => (
        <View key={item.key} className='kb-spacing-md-r'>
          <Button
            size='mini'
            circle
            className={classNames('kb_statusBarsButton', {
              'kb-background__brand': activeKeys == item.key,
              'kb-background__white': activeKeys != item.key,
            })}
            onClick={handleBars.bind(null, item.key)}
          >
            {item.label}
          </Button>
        </View>
      ))}
    </View>
  );
};

OrderListStatusBars.options = {
  addGlobalClass: true,
};

export default OrderListStatusBars;
