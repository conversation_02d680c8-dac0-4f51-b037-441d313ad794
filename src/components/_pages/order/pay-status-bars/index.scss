/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb_statusBars {
  display: flex;
  margin: $spacing-h-md;
}
.kb_statusBarsButton {
  border: 1px solid #e6e6e6;
  border-radius: 30px !important;
}
.kb_statusBarsButton::after {
  border: none;
}

.kb-background {
  &__white {
    color: #666;
    background-color: $color-white !important;
  }

  &__brand {
    color: $color-white;
    background-color: $color-brand !important;
  }
}
