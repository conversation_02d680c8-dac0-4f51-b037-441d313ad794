/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useMemo, useState } from '@tarojs/taro';

export function useOrderListStatusBars(props) {
  const { updateSearchData, show, searchData = {}, currentKey } = props;

  const SEARCH_KEY = currentKey == 'send' ? 'wait_pay' : 'wait_pickup';
  const DEFAULT_KEY = currentKey == 'send' ? 'all_order' : 'wait_pickup';
  const [activeKeys, setActiveKeys] = useState(DEFAULT_KEY);

  // 初始化要显示的按钮
  const buttons = useMemo(
    () =>
      [
        {
          label: '全部',
          key: 'all_order',
          activeKey: 'send',
        },
        {
          label: '待支付',
          key: 'wait_pay',
          activeKey: 'send',
        },
        {
          label: '历史订单',
          key: 'history',
          activeKey: 'send',
        },
        {
          label: '待取件',
          key: 'wait_pickup',
          activeKey: 'cabinet',
        },
        {
          label: '全部',
          key: 'all_cabinet',
          activeKey: 'cabinet',
        },
      ].filter((i) => i.activeKey == currentKey),
    [currentKey],
  );

  // 设置初始值
  useEffect(() => {
    if (show && !searchData[SEARCH_KEY]) {
      // 快递柜的初始是待取，所以初始应该设为1
      updateSearchData({
        extra_info: {
          [SEARCH_KEY]: currentKey == 'send' ? 0 : 1,
        },
      });
      setActiveKeys(DEFAULT_KEY);
    }
  }, [show, SEARCH_KEY]);

  const handleBars = (type) => {
    switch (type) {
      case 'history':
        Taro.navigator({
          url: 'order/history',
          options: {
            customer_id: props.customer_id || '',
          },
        });
        break;
      case 'all_order':
      case 'wait_pay':
      case 'wait_pickup':
      case 'all_cabinet':
        const ALL_KEY = ['all_order', 'all_cabinet'];
        updateSearchData({
          extra_info: {
            [SEARCH_KEY]: ALL_KEY.includes(type) ? 0 : 1,
          },
          courier_id: '', // 清空从下单拦截弹窗带来的数据
        });
        setActiveKeys(type);
        break;
      default:
        break;
    }
  };

  return {
    activeKeys,
    buttons,
    handleBars,
  };
}
