/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { checkIsShop } from '@/components/_pages/store-card/_utils';
import KbSubscribe from '@base/components/subscribe';
import request from '@base/utils/request';
import { debounce } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { Fragment, useMemo } from '@tarojs/taro';
import classNames from 'classnames';
import numeral from 'numeral';
import { usePatchOrderDetail } from './_utils/pay-status';
import './pay-status.scss';

const Index = (props) => {
  const { data: propsData, random } = props;
  const data = usePatchOrderDetail(propsData);
  // 格式化数字
  const formatNumeral = (val) => `${numeral(val).format('0.00')}`;
  const {
    coupon,
    points,
    activeEquityId: equity_card_id,
    points_count,
    canOperate,
    support_pay,
    source,
  } = data || {};
  // eslint-disable-next-line no-unused-vars
  const isKdgOrder = source === 'expresslocker';
  const handleClickItem = debounce((key) => {
    const { equity_card_id, order_id, refund_order_id } = data;
    const opts = {
      refund_card: {
        // 退卡
        url: 'refundEquityCardPrice',
        data: { equity_card_id },
        toastSuccess: '退卡成功',
      },
      refund_kf: {
        // 客服退款
        url: 'refundByCustomerService',
        data: {
          order_id: refund_order_id,
        },
      },
      refund: {
        // 退款
        url: 'applyRefund',
        data: {
          order_id,
        },
      },
    };
    if (key === 'pay') {
      // 跳转支付页
      Taro.navigator({
        url: 'order/pay',
        options: { order_id, order_random: random },
      });
    } else {
      const { url, ...rest } = opts[key];
      request({
        url: `/api/weixin/mini/minpost/Pay/${url}`,
        toastSuccess: '申请成功',
        ...rest,
        toastError: true,
        onThen: ({ code }) => {
          if (code == 0) {
            props.onChange();
          }
        },
      });
    }
  });

  const list = useMemo(() => {
    let result = null;
    if (data) {
      const {
        back_equity_card,
        status,
        pro_price = 0,
        pay_status,
        refund_status,
        pay,
        waybill,
        refundResult,
        reckonFeeInfo,
        price,
        relationData,
        source,
        is_logistic,
        refund_button_show,
        pay_button_show,
      } = data;
      result = [];
      // 驿站小程序，微快递优寄品牌同步的订单，不展示支付按钮
      const yzOrderFromWkd =
        process.env.MODE_ENV == 'yz' && ['yyj', 'kbyj', 'yhj', 'online'].includes(source);

      if (back_equity_card) {
        result.push({
          key: 'refund_card',
          buttonText: '退卡',
        });
      }
      if (status !== '已取消') {
        // 未支付，且未取消时
        if (!yzOrderFromWkd && pay_button_show) {
          result.push({
            key: 'pay',
            label: '预估运费总计',
            money: formatNumeral(price * 1 + pro_price * 1),
            buttonText: '立即支付',
          });
        } else if (support_pay && !reckonFeeInfo && pay_status == '0') {
          result.push({
            key: 'placeholder',
            showTable: false,
            placeholder:
              !reckonFeeInfo && support_pay
                ? `最终运费以${relationData.type == 'dak' ? '驿站' : '快递员'}为准`
                : false,
          });
        }
        if (pay_status == '1') {
          const resultItem = {
            money: formatNumeral(pay),
            label: '已支付金额',
          };
          if (refund_status) {
            if (refund_status == '3') {
              result.push({
                ...resultItem,
                key: 'refund_kf',
                buttonText: '客服退款',
              });
            } else {
              result.push({
                ...resultItem,
                key: 'dis',
                refundResult,
              });
            }
          } else if (refund_button_show && (!waybill || !is_logistic)) {
            result.push({
              ...resultItem,
              key: 'refund',
              buttonText: '申请退款',
            });
          }
        }
      }
    }
    return result;
  }, [data]);
  const hasList = list && list.length > 0;
  const isOpened = data && hasList;

  const discountsDetail = coupon
    ? `使用${coupon}元优惠券`
    : points_count && points
    ? `使用${points_count}积分抵扣${points}元`
    : equity_card_id
    ? '使用权益次卡抵扣首重'
    : null;
  const discountsCls = classNames('kb-spacing-md-l kb-spacing-md-b kb-size__md', {
    'kb-color__red': !!equity_card_id,
    'kb-spacing-md-t': !hasList,
  });
  return isOpened ? (
    <View className='kb-pay-status kb-margin-lg-b'>
      {list.map((item) => (
        <View className='status-item at-row at-row__align--center' key={item.key}>
          {item.key === 'refund_card' ? (
            <View className='kb-size__base at-col'>
              抱歉，您购买的权益次卡两次未能正常使用，可点击【退卡】，
              {checkIsShop(data) ? '驿站' : '快递员'}
              将按权益次卡未使用次数对应购卡金额折算后退款给您
            </View>
          ) : (
            <View className='at-col'>
              {item.showTable !== false && (
                <Fragment>
                  <Text>{item.label}：</Text>
                  <Text className='kb-color__orange'>￥{item.money}</Text>
                </Fragment>
              )}
              {item.placeholder && <View>{item.placeholder}</View>}
            </View>
          )}
          {item.buttonText && canOperate ? (
            <View className='status-item__button'>
              <KbSubscribe
                type='secondary'
                circle
                action='pay'
                onSubscribe={handleClickItem.bind(null, item.key)}
                className='kb-button__mini'
              >
                {item.buttonText}
              </KbSubscribe>
            </View>
          ) : item.refundResult ? (
            <View className='kb-size__base'>
              <Text className='kb-spacing-md-r'>{item.refundResult.label}</Text>
              <Text className='kb-color__orange'>￥{item.money}</Text>
            </View>
          ) : null}
        </View>
      ))}
      {discountsDetail && <View className={discountsCls}>{discountsDetail}</View>}
    </View>
  ) : null;
};

Index.defaultProps = {
  data: null,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
