/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { queryWaybillUrl } from '~/services/query';
import request from '~base/utils/request';

/**
 *
 * @description 驿站小程序，需要自行查询是否有物流信息；
 * Tower 任务: 小程序侧申请退款机制优化 ( https://tower.im/teams/258300/todos/103935 )
 */
export function usePatchOrderDetail(orderDetailData) {
  if (process.env.MODE_ENV === 'wkd') {
    return orderDetailData;
  } else {
    /* eslint-disable react-hooks/rules-of-hooks */
    const [patchedData, setPatchedData] = useState(null);
    useEffect(() => {
      if (!orderDetailData) return;
      const { waybill, brand } = orderDetailData;
      if (!waybill) {
        setPatchedData(orderDetailData);
      } else {
        request({
          url: queryWaybillUrl,
          data: {
            waybill,
            brand,
          },
          onThen: (res) => {
            const { list } = res.data || {};
            setPatchedData({
              ...orderDetailData,
              is_logistic: isArray(list) && list.length > 0,
            });
          },
        });
      }
    }, [orderDetailData]);
    /* eslint-enable react-hooks/rules-of-hooks */
    return patchedData;
  }
}
