/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbPicker from '@base/components/picker';
import request from '@base/utils/request';
import { noop, extractData, debounce } from '@base/utils/utils';
import { transferWkdAddress } from '@/components/_pages/order/_utils';
import isArray from 'lodash/isArray';
import isString from 'lodash/isString';
import './index.scss';

function formatNumber(n) {
  //补0
  n = n.toString();
  return n[1] ? n : '0' + n;
}

class Index extends Taro.Component {
  static defaultProps = {
    relationInfo: {}, //下单对象
    address: {}, //地址信息
    extraInfo: {}, //额外信息
    current: [],
    columns: [],
    onChange: noop,
    onRef: noop,
    isOpenCredit: false,
  };

  constructor() {
    super(...arguments);
    this.state = {
      cValue: [],
      times: [],
    };
    this.pickerRef = '';
    this.triggerUpdate = debounce(this.triggerUpdate, 300, {
      trailing: true,
    });
  }

  componentDidUpdate(prevProps) {
    const { columns, address, relationInfo } = prevProps;
    const {
      columns: nextColumns,
      address: nextAddress,
      relationInfo: nextRelationInfo,
    } = this.props;
    if (nextAddress != address || columns !== nextColumns || relationInfo != nextRelationInfo) {
      this.init(nextColumns);
    }
  }

  componentWillMount() {
    this.props.onRef(this);
  }

  // 保存picker的ref
  onRef = (ref) => {
    this.pickerRef = ref;
  };

  onShowPicker() {
    let isFullAddr = this.getAddrData();
    if (!isFullAddr) return;
    this.initTimeData((times) => {
      if (times.length > 0) {
        this.pickerRef.show();
      } else {
        Taro.kbToast({
          text: '暂无可选期望上门时间',
        });
      }
    });
  }

  onConfirm = (e) => {
    const { result, value, obj } = e;
    let arr = [];
    if (isArray(value)) {
      arr = value.length > 1 ? [...value, result] : [...value, '', result];
    }
    if (isString(value) || !value) {
      arr = [obj.value, '', obj.label];
    }
    this.setTimes(arr);
  };

  init(columns) {
    columns = columns || this.props.columns;
    let reserve_start_time, reserve_end_time, expectedTime;
    if (isArray(columns) && columns.length > 0) {
      let curColumms = columns[0];
      reserve_start_time = reserve_end_time = curColumms.value;
      expectedTime = curColumms.label;
    } else {
      reserve_start_time = reserve_end_time = '1h';
      expectedTime = '立即取件';
    }
    this.triggerUpdate({
      reserve_start_time,
      reserve_end_time,
      expectedTime,
    });
  }

  initTimeData(then) {
    const {
      relationInfo: { type: relationType, brand, platform = '' },
      extraInfo: { product_code },
    } = this.props;
    let func = noop;
    if (relationType == 'brand') {
      if (brand == 'yjkd' && platform == 'yjkd_courier') {
        func = this.getConfirmTime();
      } else {
        func = this.queryTime({
          url: '/g_wkd/v2/PickupTime/getPickupTime',
          reqData: {
            brand,
            product_code,
          },
        });
      }
    } else if (relationType == 'tcjs') {
      func = this.queryTime({
        url: '/g_wkd/v1/rush/Rush/timeLists',
        reqData: {
          brand,
        },
      });
    }
    Promise.resolve(func).then(then);
  }

  setTimes([oDate, oClock, result]) {
    let reserve_start_time, reserve_end_time, expectedTime;
    if (oDate.endsWith('h')) {
      reserve_start_time = reserve_end_time = oDate.substring(-1);
      expectedTime = result;
    } else if (!oClock || oClock == '-') {
      reserve_start_time = reserve_end_time = '1h';
      expectedTime = '立即取件';
    } else {
      let [oClock0, oClock1] = oClock.split('-');
      reserve_start_time = `${oDate} ${oClock0}`;
      reserve_end_time = `${oDate} ${oClock1}`;
      expectedTime = result;
    }
    this.triggerUpdate({
      reserve_start_time,
      reserve_end_time,
      expectedTime,
    });
  }

  triggerUpdate({ reserve_start_time, reserve_end_time, expectedTime }) {
    this.props.onChange({
      reserve_start_time,
      reserve_end_time,
      expectedTime,
    });
  }

  getAddrData() {
    const { address } = this.props;
    let addressData = extractData(transferWkdAddress(address), [
      'shipper_province',
      'shipper_city',
      'shipper_district',
      'shipper_address',
      'shipping_province',
      'shipping_city',
      'shipping_district',
      'shipping_address',
    ]);
    if (!this.isFullData(addressData)) {
      Taro.kbToast({
        text: '请填写完整地址，再选择时间!',
      });
      return;
    }
    return addressData;
  }

  queryTime({ url, reqData = {} }) {
    const { address, relationInfo: { type: relationType } = {} } = this.props;
    let addressData = this.getAddrData();
    if (relationType == 'tcjs') {
      addressData = {
        ...addressData,
        shipper_door: address.send_door,
        shipping_door: address.receive_door,
        cityName: address.city_code,
      };
    }
    reqData = {
      ...reqData,
      ...addressData,
    };
    return new Promise((resolve, reject) => {
      request({
        url,
        data: reqData,
        formatResponse: (res) => {
          if (
            res.code == 0 &&
            res.data &&
            res.data.pickupSliceTimeDTOList &&
            res.data.pickupSliceTimeDTOList.length > 0
          ) {
            let arr = [];
            res.data.pickupSliceTimeDTOList.map((item) => {
              let linkedValueArr = [];
              if (item.pickupSliceTimeList && item.pickupSliceTimeList.length > 0) {
                item.pickupSliceTimeList.map((iitem) => {
                  iitem.startTime = iitem.startTime.substr(0, 5);
                  iitem.endTime = iitem.endTime.substr(0, 5);
                  linkedValueArr.push({
                    label: iitem.name,
                    value: `${iitem.startTime}-${iitem.endTime}`,
                  });
                });
              }
              arr.push({
                label: this.formatDate(item.dateKey, true)[0],
                value: item.dateKey,
                children: linkedValueArr,
              });
            });
            res.times = arr;
          }
          return res;
        },
        onThen: ({ code, times }) => {
          console.log('times', times);
          if (code == 0 && times) {
            this.setState(
              {
                times,
              },
              () => {
                resolve(times);
              },
            );
          } else {
            Taro.kbToast({
              text: '暂无可选期望上门时间',
            });
            reject();
          }
        },
      });
    });
  }

  getConfirmTime() {
    return new Promise((resolve) => {
      const { columns } = this.props;
      this.setState(
        {
          times: columns,
        },
        () => {
          resolve(columns);
        },
      );
    });
  }

  isFullData(data = {}) {
    for (let key in data) {
      if (!data[key]) {
        return false;
      }
    }
    return true;
  }

  formatDate(date, isspace) {
    var nowDate = new Date(),
      nTs,
      fTs,
      aday = 24 * 3600 * 1000,
      reg = /\-/g,
      fDateArr;
    date = date ? date : nowDate.getTime();
    date = new Date(date);
    var year = date.getFullYear(),
      month = date.getMonth() + 1,
      day = date.getDate(),
      hour = date.getHours(),
      minute = date.getMinutes(),
      fDate = [year, month, day].map(formatNumber).join('-'),
      fTime = [hour, minute].map(formatNumber).join(':'),
      ny = nowDate.getFullYear(),
      nm = nowDate.getMonth() + 1,
      nd = nowDate.getDate(),
      nDate = [ny, nm, nd].map(formatNumber).join('-');
    fTs = Date.parse(fDate.replace(reg, '/'));
    nTs = Date.parse(nDate.replace(reg, '/'));
    if (isspace == 'ts') {
      return date ? fTs : nTs;
    }
    if (fTs == nTs) {
      fDate = '今天';
    } else if (nTs - fTs >= aday && nTs - fTs < 2 * aday) {
      fDate = '昨天';
    } else if (fTs - nTs > 0 && fTs - nTs <= aday) {
      fDate = '明天';
    } else if (fTs - nTs > aday && fTs - nTs <= 2 * aday) {
      fDate = '后天';
    } else {
      fDateArr = ny == year ? [month, day] : [year, month, day];
      fDate = fDateArr.map(formatNumber).join('-');
    }
    return isspace ? [fDate, fTime] : fDate + ' ' + fTime;
  }

  render() {
    const { times, cValue } = this.state;
    const isSingleCol = times && times[0] && !times[0].children;
    return (
      <View>
        <KbPicker
          mode={isSingleCol ? 'selector' : 'linkage'}
          options={times}
          value={cValue}
          level={isSingleCol ? 1 : 2}
          onConfirm={this.onConfirm}
          onRef={this.onRef}
        />
      </View>
    );
  }
}

export default Index;
