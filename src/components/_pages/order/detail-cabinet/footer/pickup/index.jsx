/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import CabinetOpen from '@/components/_pages/kdg/cabinetOpen';
import { noop } from '@base/utils/utils';

const DetailCabinetPickup = (props) => {
  const { orderInfo, handleUpdate } = props;

  const { order_status, device_id, pickup_code, cabinet_name } = orderInfo || {};

  return order_status == '3' ? (
    <View className='kb-spacing-md-l'>
      <CabinetOpen
        handleOpenSuccess={handleUpdate.bind(null, 'cancel')}
        button_label='取出包裹'
        type='storage'
        data={{
          device_id,
          pickup_code,
          inn_name: cabinet_name,
          source: 'storage',
          ...orderInfo,
        }}
      />
    </View>
  ) : null;
};

DetailCabinetPickup.options = {
  addGlobalClass: true,
};

DetailCabinetPickup.defaultProps = {
  orderInfo: {},
  handleUpdate: noop,
};

export default DetailCabinetPickup;
