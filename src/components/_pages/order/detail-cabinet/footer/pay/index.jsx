/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import { formatPayResultParamsAndNavigator, payCabinetOrder } from '../../_utils/utils';

const DetailCabinetPay = (props) => {
  const { orderInfo, handleUpdate } = props;

  const { order_status, order_id } = orderInfo;

  const onPay = async () => {
    await payCabinetOrder(order_id);
    handleUpdate('cancel');
    formatPayResultParamsAndNavigator(orderInfo);
  };

  return order_status == '1' ? (
    <View className='kb-spacing-md-l'>
      <AtButton size='small' circle type='primary' onClick={onPay}>
        去支付
      </AtButton>
    </View>
  ) : null;
};

DetailCabinetPay.options = {
  addGlobalClass: true,
};

DetailCabinetPay.defaultProps = {
  orderInfo: {},
  handleUpdate: noop,
};

export default DetailCabinetPay;
