/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import { cancelCabinetOrder } from '../../_utils/utils';

const DetailCabinetCancel = (props) => {
  const { orderInfo, handleUpdate = () => {} } = props;

  const { is_show_cancel, order_id } = orderInfo || {};

  const onCancel = async () => {
    const status = await cancelCabinetOrder(order_id);
    if (status) {
      handleUpdate('cancel');
    }
  };

  return is_show_cancel ? (
    <View className='kb-spacing-md-l'>
      <AtButton size='small' circle onClick={onCancel}>
        取消订单
      </AtButton>
    </View>
  ) : null;
};

DetailCabinetCancel.options = {
  addGlobalClass: true,
};

export default DetailCabinetCancel;
