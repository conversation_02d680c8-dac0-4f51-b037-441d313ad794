/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import { formatPayResultParamsAndNavigator } from '../../_utils/utils';

// q=https%3A%2F%2Fkbydy.cn%2Fkdg%3Fdak_id%3D3473661

const DetailCabinetOpen = (props) => {
  const { orderInfo } = props;

  const { order_status } = orderInfo || {};

  return order_status == '2' ? (
    <View className='kb-spacing-md-l'>
      <AtButton
        size='small'
        circle
        type='primary'
        onClick={formatPayResultParamsAndNavigator.bind(null, orderInfo)}
      >
        存柜
      </AtButton>
    </View>
  ) : null;
};

// q=https%3A%2F%2Fkbydy.cn%2Fkdg%3Fdak_id%3D3473617 
// 3473617 
// 3473661

DetailCabinetOpen.options = {
  addGlobalClass: true,
};

DetailCabinetOpen.defaultProps = {
  orderInfo: {},
  handleUpdate: noop,
};

export default DetailCabinetOpen;
