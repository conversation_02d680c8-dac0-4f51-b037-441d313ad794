/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import DetailCabinetPay from './pay';
import DetailCabinetPickup from './pickup';

import './index.scss';
import DetailCabinetCancel from './cancel';
import DetailCabinetOpen from './open';

const DetailCabinetFooter = (props) => {
  return (
    <View className='detailCabinetFooter'>
      <DetailCabinetOpen {...props} source='detail' />
      <DetailCabinetCancel {...props} source='detail' />
      <DetailCabinetPay {...props} source='detail' />
      <DetailCabinetPickup {...props} source='detail' />
    </View>
  );
};

export default DetailCabinetFooter;
