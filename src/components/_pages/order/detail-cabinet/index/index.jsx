/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import KbDescription from '../descriptions';
import { useDetailCabinetIndex } from './useDetailCabinetIndex';
import DetailCabinetHeader from '../header';

const DetailCabinetIndex = (props) => {
  const { infoDescription, priceDescription } = useDetailCabinetIndex(props);

  return (
    <View className='kb-spacing-md-t'>
      <DetailCabinetHeader {...props} />
      <KbDescription listData={infoDescription} />
      <KbDescription listData={priceDescription} />
    </View>
  );
};

DetailCabinetIndex.options = {
  addGlobalClass: true,
};

export default DetailCabinetIndex;
