/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { dateCalendar } from '@base/utils/utils';
import Taro, { useMemo } from '@tarojs/taro';
import { formatPrice, formatSecondDate } from '../_utils/utils';

export function useDetailCabinetIndex(props) {
  const { orderInfo = {} } = props;

  const infoDescription = useMemo(
    () => ({
      header: {
        key: 'title',
        label: '存储信息',
      },
      info: [
        {
          key: 'pickupCode',
          label: '取件码',
          extra: parseFloat(orderInfo.pickup_code || '0') ? orderInfo.pickup_code : '--',
        },
        {
          key: 'guidInfo',
          label: '格口信息',
          extra: orderInfo.grid_number
            ? `${orderInfo.grid_number}（${orderInfo.grid_desc}）`
            : '--',
        },
        {
          key: 'timer',
          label: '储存时间',
          extra: formatSecondDate(orderInfo.order_duration_second),
        },
        {
          key: 'goods',
          label: '物品信息',
          extra: orderInfo.goods,
        },
        {
          key: 'date',
          label: '下单时间',
          extra: dateCalendar(orderInfo.create_time, { timer: true }),
        },
        {
          key: 'order_id',
          label: '订单号',
          extra: orderInfo.order_id,
        },
      ],
    }),
    [orderInfo],
  );

  const priceDescription = useMemo(() => {
    const priceLabel = `${orderInfo.grid_desc || '格口'}（${formatSecondDate(
      orderInfo.storage_duration,
    )}）`;
    const priceExtra = `¥ ${formatPrice(orderInfo.base_price)}`;

    const priceExtraLabel =
      orderInfo.order_status == '4' || orderInfo.order_status == '1'
        ? ''
        : `${
            orderInfo.order_status == '-9'
              ? '(已退款)'
              : parseFloat(orderInfo.wait_pay_price || 0) > 0
              ? '(未支付)'
              : ''
          }`;

    const overLabel = `超时费`;

    const overLabelExtra = `${
      orderInfo.order_over_second ? `(超时${formatSecondDate(orderInfo.order_over_second)})` : ''
    }`;

    const overExtra = orderInfo.order_over_second
      ? parseFloat(orderInfo.pay_overdue_price || 0)
        ? `¥ ${formatPrice(orderInfo.pay_overdue_price)}`
        : `¥ ${formatPrice(orderInfo.overdue_price)}`
      : '未超时';

    const overExtraLabel = orderInfo.order_over_second
      ? parseFloat(orderInfo.pay_overdue_price || 0)
        ? `(已支付)`
        : `(待支付)`
      : '';

    const totalPay = `¥ ${
      formatPrice(orderInfo.pay_price * 1 + orderInfo.pay_overdue_price * 1) || 0
    }`;

    return {
      header: {
        key: 'title',
        label: '费用信息',
      },
      info: [
        {
          key: 'price',
          label: priceLabel,
          extra: priceExtra,
          extraLabel: priceExtraLabel,
          extraClass: 'kb-color__black',
          extraLabelClass: orderInfo.order_status == '-9' ? 'kb-color__red' : 'kb-color__brand',
        },
        {
          key: 'overtime',
          label: overLabel,
          labelExtra: overLabelExtra,
          extra: overExtra,
          extraLabel: overExtraLabel,
          extraClass: 'kb-color__grey',
          extraLabelClass: 'kb-color__brand',
          labelExtraClass: 'kb-color__red',
        },
      ],
      total: {
        key: 'total',
        label: '实付',
        extra: totalPay,
        extraClass: 'kb-color__red kb-size__xl',
      },
    };
  }, [orderInfo]);

  return {
    infoDescription,
    priceDescription,
  };
}
