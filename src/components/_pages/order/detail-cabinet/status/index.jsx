/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbCountDownClock from '@base/components/count-down-clock';

import './index.scss';
import { CABINET_ORDER_STATUS } from '../_utils/utils';
import { noop } from '@base/utils/utils';

const DetailCabinetStatus = (props) => {
  const { orderInfo = {}, handleUpdate } = props;

  const { order_status, is_show_pay_time, pay_time } = orderInfo;

  const statusObj = useMemo(() => {
    return CABINET_ORDER_STATUS.find((i) => i.key == order_status) || {};
  }, [order_status]);

  return (
    <View className='detailCabinetStatus'>
      {statusObj.label && (
        <View className={`kb-size__lg ${statusObj.cnName}`}>{statusObj.label}</View>
      )}
      {is_show_pay_time && (
        <KbCountDownClock
          end={pay_time}
          onEnd={handleUpdate}
          theme='red'
          cname='detail_order_time__countDown'
        />
      )}
    </View>
  );
};

DetailCabinetStatus.options = {
  addGlobalClass: true,
};

DetailCabinetStatus.defaultPros = {
  orderInfo: {},
  handleUpdate: noop,
};

export default DetailCabinetStatus;
