/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import './index.scss';

// 接收数据类型

// =>单条类型
// const obj = {
//   key: 'string',
//   label: '费用信息',
//   labelClass: 'kb-color__red',
//   extra: 'extra content',
//   extraClass: 'kb-color__blue',
// };

// 完整数据类型,兼容了数据传递
// const listData = {
//   header:obj || [obj1,obj2],
//   info:obj || [obj1,obj2],
//   total:obj || [obj1,obj2],
// };

const KbDescription = ({ listData = {} }) => {
  return (
    <View className='kb_desc'>
      {Object.keys(listData).map((keys) => {
        // 判断类型是否为数组
        const obj = listData[keys]
          ? Array.isArray(listData[keys])
            ? listData[keys]
            : [listData[keys]]
          : [];
        return obj.length ? (
          <View className={`kb_desc__${keys}`} key={keys}>
            {obj.map((item) => {
              const labelClass = classNames('kb_desc__flex__label', {
                'kb-color__black kb-size__lg': keys == 'header' || keys == 'total',
                'kb-color__grey': keys == 'info',
                [item.labelClass]: !!item.labelClass,
              });
              const extraClass = classNames('kb-color__greyer', {
                [item.extraClass]: !!item.extraClass,
              });
              return (
                <View className='kb_desc__flex' key={item.key}>
                  {item.label && (
                    <View className={labelClass}>
                      {item.label}
                      {item.labelExtra && (
                        <Text className={item.labelExtraClass}>{item.labelExtra} </Text>
                      )}
                    </View>
                  )}
                  {item.extra && (
                    <View className={extraClass}>
                      {item.extraLabel && (
                        <Text className={item.extraLabelClass}>{item.extraLabel} </Text>
                      )}
                      {item.extra}
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        ) : null;
      })}
    </View>
  );
};

KbDescription.options = {
  addGlobalClass: true,
};

export default KbDescription;
