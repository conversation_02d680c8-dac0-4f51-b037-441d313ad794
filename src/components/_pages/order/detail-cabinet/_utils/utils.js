/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import dayjs from 'dayjs';
import { requestPayment } from '@/utils/qy';
import Taro from '@tarojs/taro';
import isNumber from 'lodash/isNumber';

// 格式化时间 传入秒，返回天/小时/分格式
export function formatSecondDate(timestamp) {
  if (!timestamp) return '';
  const minute = Math.ceil(Math.abs(timestamp) / 60);
  if (minute < 1) {
    return '刚刚';
  } else if (minute < 60) {
    return minute + '分钟';
  } else if (minute < 1440) {
    const min = minute % 60;
    return Math.floor(minute / 60) + '小时' + (min ? min + '分' : '');
  } else {
    return Math.floor(minute / 1440) + '天' + formatSecondDate(timestamp % (1440 * 60));
  }
}

// 格式化金额
export function formatPrice(val = '') {
  return val || isNumber(val) ? (val * 1).toFixed(2) : '';
}

// 订单状态映射
export const CABINET_ORDER_STATUS = [
  {
    key: '-9',
    label: '已退款',
    cnName: 'kb-color__grey',
  },
  {
    key: '0',
    label: '已取消',
    cnName: 'kb-color__grey',
  },
  {
    key: '1',
    label: '待支付',
    cnName: 'kb-color__brand',
  },
  {
    key: '2',
    label: '待存柜',
    cnName: 'kb-color__brand',
  },
  {
    key: '3',
    label: '待取出',
    cnName: 'kb-color__brand',
  },
  {
    key: '4',
    label: '已取出',
    cnName: 'kb-color__greyer',
  },
];

// 快递柜取消订单
export function cancelCabinetOrder(order_id) {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/minpost/CabinetStorage/cancelOrder',
      data: {
        order_id,
      },
      toastSuccess: '取消成功',
      toastError: true,
      onThen: ({ code }) => {
        if (`${code}` === '0') {
          resolve(true);
        }
      },
    });
  });
}

// 获取支付签名
const getPaySign = (order_id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/minpost/CabinetStorage/payStorageFeeSign',
      data: {
        order_id,
      },
      toastError: true,
      onThen: ({ code, data }) => {
        if (`${code}` === '0') {
          resolve(data);
        }
      },
    });
  });
};

// 支付动作
const payHandle = (sign) => {
  return new Promise((resolve) => {
    requestPayment(sign)
      .then(() => {
        Taro.kbToast({ text: '支付成功' });
        resolve();
      })
      .catch(() => {
        Taro.kbToast({ text: '支付失败' });
      });
  });
};

// 快递柜支付订单 仅限创建订单支付，超时支付不走这里
export function payCabinetOrder(order_id) {
  return new Promise(async (resolve) => {
    const sign = await getPaySign(order_id);
    await payHandle(sign);
    resolve();
  });
}

// 显示已经在快递柜中持续时间，以存柜时间做节点。
// 如订单已完成，取存到开中间持续时间。
const getOrderDurationSecond = (info) => {
  // 显示已经在快递柜中持续时间，以存柜时间做节点。
  // 如订单已完成，取存到开中间持续时间。
  const { order_status, put_in_time, take_out_time } = info;

  if (order_status == '3' && put_in_time) {
    return dayjs().diff(put_in_time, 'second') || '1';
  }
  if (order_status == '4' && put_in_time && take_out_time) {
    return dayjs(take_out_time).diff(put_in_time, 'second');
  }
  return '';
};

// 格式化快递柜订单，处理订单状态，时间类
export function formatCabinetOrderInfo(info = {}) {
  const {
    create_time,
    expired_at,
    origin_create_time = create_time,
    order_status,
    take_out_time = '',
  } = info;

  if (!order_status) {
    return info;
  }

  // 待支付订单到期时间
  const PAY_TIME_LIMIT = 605;

  const show_time = !['0', '2', '-9'].includes(order_status);
  const is_show_pay_time = order_status == '1';
  const pay_time = dayjs(origin_create_time).add(PAY_TIME_LIMIT, 'seconds');
  const order_over_second =
    order_status == '4'
      ? dayjs(take_out_time).diff(expired_at, 'second')
      : dayjs().diff(expired_at, 'second');
  const is_show_order_over = ['2', '3'].includes(order_status) && order_over_second > 0;

  const is_show_cancel = order_status == '1';

  return {
    ...info,
    order_status, // 订单状态
    show_time, // 是否展示时间 列表用
    is_show_order_over, // 是否展示订单超时
    order_over_second: order_over_second > 0 ? order_over_second : 0, // 订单超时时间
    order_duration_second: getOrderDurationSecond(info), // 订单持续时间
    is_show_pay_time, // 是否展示待支付时间
    pay_time, // 支付时间
    is_show_cancel, // 是否展示取消
  };
}

// 用于订单列表&详情支付后，跳转到开柜页面时，数据整理。
export function formatPayResultParamsAndNavigator(info) {
  // const obj = {
  //   dakInfo:
  //     '{"dak_id":"","inn_name":"德沃公司Test01柜","inn_phone":"15833332222","id":"185","slave_cm_id":"3473684"}',
  //   device_id: '2ee3970d-0edb-3873-9286-9d90b1f27e68',
  //   order_id: '1010286110704618',
  // };
  const { order_id, device_id, dak_id, cabinet_name, inn_phone = '', cabinet_id, grid_type } = info;
  const params = {
    order_id,
    device_id,
    size: grid_type,
    dakInfo: JSON.stringify({
      dak_id,
      inn_name: cabinet_name,
      inn_phone: inn_phone,
      id: cabinet_id,
      slave_cm_id: dak_id,
    }),
  };
  Taro.navigator({
    url: 'kdg/storage/result',
    options: params,
  });
}
