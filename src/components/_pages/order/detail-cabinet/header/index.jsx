/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
// import { openLocation } from '@/components/_pages/store-card/_utils';
import { View, Image } from '@tarojs/components';
// import { AtIcon } from 'taro-ui';

import './index.scss';
import DetailCabinetStatus from '../status';

const DetailCabinetHeader = (props) => {
  const { orderInfo, handleUpdate } = props;

  const { cabinet_name, cabinet_address } = orderInfo || {};

  const handleOpenAddr = () => {
    console.info('需要经纬度=====21');
    // openLocation({});
  };

  return (
    <View className='detailCabinetHeader'>
      <Image
        className='detailCabinetHeader__icon'
        src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_cabinet.png?v=20240712'
      />
      <View className='detailCabinetHeader__info'>
        <View className='kb-size__xl' hoverClass='kb-hover-opacity'>
          {cabinet_name}
          {/* <AtIcon prefixClass='kb-icon' value='call' className='kb-size__base kb-color__brand' /> */}
        </View>
        <View
          className='kb-size__sm kb-color__grey'
          hoverClass='kb-hover-opacity'
          onClick={handleOpenAddr}
        >
          {cabinet_address}
          {/* <AtIcon
            prefixClass='kb-icon'
            value='location'
            className='kb-size__base kb-color__brand'
          /> */}
        </View>
      </View>
      <DetailCabinetStatus orderInfo={orderInfo} handleUpdate={handleUpdate} />
    </View>
  );
};

DetailCabinetHeader.options = {
  addGlobalClass: true,
};

export default DetailCabinetHeader;
