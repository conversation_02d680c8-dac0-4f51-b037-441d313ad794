/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.detailCabinetHeader {
  display: flex;
  gap: $spacing-v-md;
  align-items: center;
  justify-content: space-between;
  margin: $spacing-v-md;
  padding: $spacing-v-md * 1.5 $spacing-v-md;
  background-color: $color-white;
  border-radius: $border-radius-md;
  &__icon {
    width: 80px;
    height: 80px;
  }
  &__info {
    flex: 1;
  }
}
