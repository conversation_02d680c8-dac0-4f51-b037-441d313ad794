/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-area {
  line-height: 1.5;
  padding: $spacing-v-lg $spacing-h-lg;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: $color-white;
  &__value,
  &__placeholder {
    flex-grow: 1;
    padding-right: $spacing-h-lg;
  }
  &__placeholder {
    color: $color-grey-3;
  }
  &::after {
    font-family: "kb-icon";
    color: $color-grey-3;
    font-size: $font-size-lg;
  }
  &.kb-hover {
    border-radius: 0 !important;
  }
}
.kb-nav__icon {
  &::after {
    content: "\e620";
    margin-right: -$spacing-h-sm;
  }
}
