/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import isEmpty from 'lodash/isEmpty';
// 兼容支付宝与微信
export const supportAi = ['ai', 'camera', 'mic', 'book'];

/**
 * 检查地址是否有效
 *  */
export const checkAddress = (address = {}) => {
  const checkKey = ['address', 'city', 'district', 'mobile', 'name', 'province'];
  const emptyCheck = checkKey.every((key) => !address[key]);
  const fullCheck = checkKey.every((key) => address[key]);

  if (isEmpty(address) || emptyCheck) {
    return 'empty';
  }

  if (fullCheck) {
    return 'full';
  }

  return 'notFull';
};
