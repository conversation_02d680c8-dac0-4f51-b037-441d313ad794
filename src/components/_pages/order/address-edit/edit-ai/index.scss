/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.item-bar {
  width: auto !important;
  &__item {
    padding-left: $spacing-v-lg;
  }
}

.kb-border-radius-lg {
  overflow: hidden;
  border-radius: $border-radius-md;
}

.kb-background {
  &__white {
    background-color: $color-white;
  }
  &__grey {
    background-color: $color-grey-5;
  }
}

.kb-item-wrap {
  overflow: hidden;
  border-radius: $border-radius-lg;
}
.kb-flex-grw {
  display: flex;
  flex-grow: 1;
}

.kb-edit-ai {
  height: 304px;
}
