/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { extractDataFromFull, tcjsAddressList } from '@/components/_pages/order/_utils/order.tcjs';
import { Text, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './index.scss';

class Index extends Taro.Component {
  static defaultProps = {
    data: {},
    sendId: '',
    receiveId: '',
  };
  static options = {
    addGlobalClass: true,
  };

  constructor() {
    super(...arguments);
  }

  onClickBar = (key, item) => {
    const { data, sendId, receiveId } = this.props;
    const { key: type, ...resetData } = item;
    switch (key) {
      case 'edit':
        Taro.navigator({
          url: 'order/delivery/editAddress',
          options: {
            type: type,
            id: type == 'send' ? sendId : receiveId,
            ...extractDataFromFull({ data, prefix: type }),
          },
        });
        break;
      case 'lib':
        Taro.navigator({
          url: 'order/delivery/addressLib',
          options: {
            type: type,
          },
        });
        break;
    }
  };

  render() {
    const { data } = this.props;
    return (
      <View className='kb-address-info'>
        {tcjsAddressList.map((item, index) => {
          const addrInfoItemCls = classNames('kb-address-info-item', {
            'kb-address-info-item-top': index == 0,
            'kb-address-info-item-bottom': index != 0,
          });
          const iconCls = classNames('kb-icon-size__lg', `kb-color__${item.color}`);
          const iconCls_xl = classNames('kb-icon-size__xl', `kb-color__${item.color}`);
          const address = data[`${item.key}_address`];
          const door = data[`${item.key}_door`];
          const name = data[`${item.key}_name`];
          const mobile = data[`${item.key}_mobile`];
          return (
            <View
              className={addrInfoItemCls}
              key={item.key}
              onClick={(ev) => this.onClickBar('edit', item, ev)}
            >
              <View className='kb-address-info-item-icon'>
                <AtIcon prefixClass='kb-icon' className={iconCls_xl} value='water' />
                <Text className='kb-address-info-item-icon__text'>{item.tag}</Text>
              </View>
              <View className='kb-address-info-item-content' hoverClass='kb-hover'>
                {!address ? (
                  <View className='kb-size__lg kb-margin-sm-b kb-color__grey'>
                    {item.key == 'send' ? '从哪寄' : '送到哪儿'}
                  </View>
                ) : (
                  <Fragment>
                    <View className='kb-size__lg kb-margin-sm-b'>
                      {address} {door}
                    </View>
                    {!name && !mobile ? (
                      <View className='size-13 kb-color__grey'>
                        请完善{item.key == 'send' ? '寄' : '收'}件人信息
                      </View>
                    ) : (
                      <View className='size-13 kb-color__grey'>
                        {name} {mobile}
                      </View>
                    )}
                  </Fragment>
                )}
              </View>
              <View
                className='item-bar__item--icon item-bar__item--lib'
                onClick={(ev) => {
                  ev.stopPropagation();
                  this.onClickBar('lib', item, ev);
                }}
                hoverStopPropagation
                hoverClass='kb-hover'
              >
                <AtIcon prefixClass='kb-icon' value='book' className={iconCls} />
              </View>
            </View>
          );
        })}
      </View>
    );
  }
}

export default Index;
