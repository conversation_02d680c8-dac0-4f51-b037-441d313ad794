/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$address-placeholder-height: 150px;
$address-border-radius: $border-radius-lg;

.kb-address-info {
  &-item {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 150px;
    background: $color-white;
    &-top {
      border-radius: 10px 10px 0 0;
      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 2px;
        background: #eee;
        left: 90px;
        right: 0;
        bottom: 0;
      }
    }
    &-bottom {
      border-radius: 0 0 10px 10px;
    }
    &-icon {
      position: relative;
      padding: 0 $spacing-h-md;

      &__text {
        position: absolute;
        color: $color-white;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -60%);
        font-size: $font-size-base;
      }
    }
    &-content {
      flex: 1;
    }
    .item-bar {
      &__item {
        &--icon {
          line-height: 1;

          padding: 2 * $spacing-v-md $spacing-h-md;

          &.kb-hover {
            border-radius: 0;
          }
        }

        &--icon::before {
          content: "";
          height: 100%;
          width: 0;
          display: inline-block;
          vertical-align: middle;
        }

        &--lib::before,
        &:first-child::after {
          content: "";
          height: $address-placeholder-height/2.5;
          border-right: $border-lightest;
          margin: 0 $spacing-h-md 0 2 * $spacing-h-md;
        }

        &--lib {
          padding-right: 2 * $spacing-h-md;
          padding-left: 0;
          display: flex;
          align-items: center;

          &::before {
            margin: 0 2 * $spacing-h-md 0 0;
          }
        }
      }
    }
  }
}
