/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { org, showTip, onClose, onClipboard } = props;
  const iconCls = `kb-icon-size__lg kb-color__${
    org === 'send' ? 'brand' : 'orange'
  } kb-clipboard-text`;

  const handleCloseClipborardTip = () => {
    onClose && onClose();
  };
  const handleClipboard = () => {
    onClipboard && onClipboard();
  };
  return (
    <View className='kb-pos-r'>
      <View className='kb-spacing-xl-lr kb-spacing-sm-tb kb-clipboard-tip at-row '>
        {showTip && (
          <View className='kb-clipboard-tip_text'>
            <Text className='kb-clipboard-tip_text--info'>点击粘贴,一键录入收寄件地址</Text>
            <Text
              hoverClass='kb-hover'
              onClick={handleCloseClipborardTip.bind(null, (e) => e.stopPropagation())}
              className='kb-clipboard-tip--close'
            />
          </View>
        )}
      </View>
      <View
        onClick={handleClipboard.bind(null, (e) => e.stopPropagation())}
        className='kb-block--inline'
      >
        <AtIcon className={iconCls} prefixClass='kb-icon' value='clipboard-address' />
      </View>
    </View>
  );
};

export default Index;
