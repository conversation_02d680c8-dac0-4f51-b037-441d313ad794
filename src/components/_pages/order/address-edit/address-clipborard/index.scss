/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-pos-r {
  position: relative;
}

.kb-block--inline {
  display: inline-block;
}

.kb-clipboard-tip {
  position: absolute;
  top: -60px;
  left: 50%;
  width: auto;
  padding: 0 $spacing-h-xs;
  transform: translateX(-57%);

  &_text {
    display: inline-block;
    padding: $spacing-h-xs + 2 52px $spacing-h-xs + 2 $spacing-h-md;
    color: $color-white;
    font-size: $font-size-sm;
    white-space: nowrap;
    background-color: rgba($color: $color-black-1, $alpha: 0.6);
    border-radius: 4px;

    &::before {
      position: absolute;
      bottom: -10px;
      left: 55%;
      display: block;
      width: 0;
      height: 0;
      border: 10px solid rgba($color: $color-black-1, $alpha: 0.6);
      border-top-width: 10px;
      border-right-color: transparent;
      border-right-width: 10px;
      border-bottom: 0;
      border-left-color: transparent;
      border-left-width: 10px;
      content: '';
    }

    &--info {
      white-space: nowrap;
    }
  }

  &--close {
    @include close-icon(22px, $color-white);
    position: absolute;
    top: 50%;
    right: 6px;
    display: inline-block;
    width: 22px;
    font-size: $font-size-xs;
    transform: translateY(-50%);
  }
}
