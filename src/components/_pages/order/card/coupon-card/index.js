/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useState } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon, AtTag } from 'taro-ui';
import KbCheckbox from '@base/components/checkbox';
import { getWkdStoreInfo } from '@/components/_pages/store-card/_utils';
import classNames from 'classnames';
import './index.scss';

function Index(props) {
  const { className, mode, type, card, selected, onSelect } = props;

  const [close, setClose] = useState(true);

  const handleSelect = () => {
    if (mode == 'selectable') {
      onSelect({ card });
    }
  };

  const onExpress = (ev) => {
    ev.stopPropagation();
    if (card.account_phone) {
      getWkdStoreInfo({ phone: card.account_phone })
        .then((data) => {
          if (data) {
            Taro.navigator({
              url: 'order/edit',
              target: 'tab',
              onArrived: () => {
                Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo(data);
              },
            });
          }
        })
        .catch((err) => console.log(err));
    } else {
      Taro.navigator({
        url: 'order/edit',
        target: 'tab',
      });
    }
  };

  const onSwitchDescStatus = (ev) => {
    ev.stopPropagation();
    setClose(!close);
  };

  const rootCls = classNames('coupon', className);
  return (
    <View
      className={rootCls}
      onClick={handleSelect}
      hoverClass={mode == 'selectable' && card.past_date > 0 ? 'kb-hover' : 'none'}
    >
      <View className='coupon-head' />
      <View className='coupon-body'>
        <View className='coupon-body-price'>
          <Text className='coupon-body-price_unit'>￥</Text>
          <Text className='coupon-body-price_num'>{card.cost}</Text>
        </View>
        <View className='coupon-body-explain'>
          {type == 'yj_mini' ? (
            <View className='kb-size__bold'>优寄服务专用优惠券</View>
          ) : card.card_type == 'yj_coupon' ? (
            <View className='at-row at-row__align--center'>
              <AtTag type='primary' size='small' active>
                优寄专用
              </AtTag>
              <View className='kb-margin-sm-l'>{card.card_name}</View>
            </View>
          ) : card.account_name ? (
            <View>仅限对快递员{card.account_name}下单可用</View>
          ) : null}
          <View className='display-flex-row'>
            {card.expiration_time && <View>有效期至{card.expiration_time}</View>}
            {card.past_date > 0 && card.past_date <= 2 && (
              <Fragment>
                (剩
                <View className='kb-color__red'>{card.past_date}</View>
                日)
              </Fragment>
            )}
          </View>
        </View>
        <View className='coupon-body-option kb-margin-md-r'>
          {mode == 'selectable' ? (
            <KbCheckbox checked={selected} />
          ) : card.card_type == 'coupon' ? (
            <View className='kb-size__base kb-color__brand' onClick={onExpress}>
              去使用
            </View>
          ) : null}
        </View>
      </View>
      {card.desc && card.desc.length > 0 && type != 'yj_mini' && (
        <View className='kb-spacing-md-lr kb-spacing-md-b'>
          {!close && (
            <View className='kb-size__sm kb-color__grey'>
              {card.desc.map((oItem) => (
                <View className='kb-margin-sm-b' key={oItem}>
                  {oItem}
                </View>
              ))}
            </View>
          )}
          <View className='at-row at-row__justify--center' onClick={onSwitchDescStatus}>
            <AtIcon
              className={close ? 'kb-icon__direction-down' : 'kb-icon__direction-up'}
              prefixClass='kb-icon'
              value='arrow'
              size='16'
            />
          </View>
        </View>
      )}
    </View>
  );
}

Index.defaultProps = {
  className: '',
  mode: 'normal',
  type: 'normal', //normal正常类型/yj_mini优寄精简模式
  card: {},
  selected: false,
  onSelect: () => {},
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
