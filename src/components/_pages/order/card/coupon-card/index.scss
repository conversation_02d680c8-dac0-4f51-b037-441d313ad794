/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.coupon {
  background: $color-white;
  border-radius: $border-radius-md;
  &-head {
    width: 100%;
    height: 20px;
    border-radius: 10px 10px 0 0;
    background: linear-gradient(to right, #ff5567, #ffe284);
  }
  &-body {
    position: relative;
    padding: $spacing-v-lg;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      display: block;
      background-color: #f2f2f2;
      border-radius: 50%;
      width: 30px;
      height: 30px;
    }
    &::before {
      left: -15px;
    }
    &::after {
      right: -15px;
    }
    &-price {
      color: #ff5567;
      &_unit {
        font-size: $font-size-base;
      }
      &_num {
        font-size: 42px;
      }
    }
    &-explain {
      padding-left: $spacing-h-md;
      flex: 1;
      color: $color-grey-1;
      font-size: $font-size-base;
    }
  }
}
