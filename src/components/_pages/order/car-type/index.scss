/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-carType {
  overflow: hidden;
  border-radius: $border-radius-lg;
  background: $color-white;
  &__info {
    display: flex;
    align-items: center;
    padding: $spacing-h-lg;
    .content {
      display: flex;
      flex: 1;
      font-size: $font-size-base;
      color: $color-grey-1;
      &__img {
        width: 200px;
        height: 120px;
      }
    }
  }
  .carList {
    display: flex;
    flex-wrap: wrap;
    &__item {
      width: 33%;
      height: 240px;
      border-right: $border-lighter;
      border-bottom: $border-lighter;
      &--img {
        width: 240px;
        height: 144px;
      }
      &--name {
        text-align: center;
      }
    }
  }
}
