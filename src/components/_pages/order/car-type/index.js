/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import { AtFloatLayout, AtIcon, AtTabs } from 'taro-ui';
import request from '@base/utils/request';
import './index.scss';

class Index extends Taro.Component {
  static defaultProps = {
    brand: '',
    cityCode: '',
    onChange: () => {},
  };

  static options = {
    addGlobalClass: true,
  };

  constructor() {
    super(...arguments);
    this.state = {
      carList: [],
      carDetail: {},
      isOpened: false,
    };
  }

  componentDidUpdate(preProps) {
    const { brand: pre_brand, cityCode: pre_cityCode } = preProps;
    const { brand, cityCode } = this.props;
    if (pre_brand != brand || pre_cityCode != cityCode) {
      this.getCars();
    }
  }

  setCurrentCar({ index, car_name, car_id }) {
    let carInfo = {
      city_revision: this.city_revision,
      index,
      car_id,
      car_name,
    };
    this.setState({
      carInfo,
    });
    this.getCarInfo(car_id);
    this.props.onChange({ carInfo });
  }

  getCars() {
    const { brand, cityCode } = this.props;
    if (!brand || !cityCode) return;
    request({
      url: '/g_wkd/v1/rush/Rush/getCars',
      data: {
        brand,
        city_code: cityCode,
      },
      onThen: (res) => {
        if (res.code == 0) {
          const { type: types, revision } = res.data;
          this.city_revision = revision;
          let carList = types.map((item, index) => {
            return {
              ...item,
              index,
              title: item.name,
              car_name: item.name,
              car_id: item.id,
            };
          });
          if (carList.length > 0) {
            this.setCurrentCar(carList[0]);
          }
          this.setState({
            carList,
          });
        }
      },
    });
  }

  getCarInfo(car_id) {
    const { brand, cityCode } = this.props;
    if (!car_id || !brand || !cityCode) return;
    request({
      url: '/g_wkd/v1/rush/Rush/getCarInfo',
      data: {
        brand,
        city_code: cityCode,
        car_id,
      },
      onThen: (res) => {
        if (res.code == 0) {
          const { img_url_high_light, weight, size, volume } = res.data;
          let carDetail = {
            url: img_url_high_light,
            weight: weight,
            size: size,
            volume: volume,
          };
          this.setState({
            carDetail,
          });
        }
      },
    });
  }

  handleSelect(index) {
    const { carList, isOpened } = this.state;
    this.setCurrentCar(carList[index]);
    isOpened && this.handleClose();
  }

  handleOpen() {
    this.setState({
      isOpened: true,
    });
  }

  handleClose() {
    this.setState({
      isOpened: false,
    });
  }

  JumpToDetail() {
    const { cityCode, brand } = this.props;
    const {
      carInfo: { car_id },
    } = this.state;
    Taro.navigator({
      url: 'order/delivery/carDetail',
      options: {
        city_code: cityCode,
        brand,
        car_id,
      },
    });
  }

  render() {
    const { carList, carInfo, carDetail, isOpened } = this.state;
    return (
      <Fragment>
        {carList && carList.length > 0 && (
          <View className='kb-carType'>
            <View className='at-row'>
              <View className='at-col'>
                <AtTabs
                  current={(carInfo && carInfo.index) || 0}
                  tabList={carList}
                  scroll
                  onClick={this.handleSelect.bind(this)}
                />
              </View>
              <View onClick={this.handleOpen.bind(this)}>
                <AtIcon
                  className='kb-color__brand kb-spacing-md-lr kb-margin-lg-t'
                  prefixClass='kb-icon'
                  value='more'
                  size='18'
                />
              </View>
            </View>
            <View className='kb-carType__info' onClick={this.JumpToDetail.bind(this)}>
              <View className='content'>
                <View>
                  <Image className='content__img' src={carDetail.url} lazyLoad />
                </View>
                <View className='content__list'>
                  {carDetail.weight && (
                    <View className='kb-margin-sm-b'>载重：{carDetail.weight}</View>
                  )}
                  {carDetail.size && (
                    <View className='kb-margin-sm-b'>长宽高：{carDetail.size}</View>
                  )}
                  {carDetail.volume && (
                    <View className='kb-margin-sm-b'>载货体积：{carDetail.volume}</View>
                  )}
                </View>
              </View>
              <View>
                <AtIcon className='kb-color__grey' prefixClass='kb-icon' value='arrow' size='18' />
              </View>
            </View>
            <AtFloatLayout
              isOpened={isOpened}
              title='选择车型'
              onClose={this.handleClose.bind(this)}
            >
              <View className='carList'>
                {carList.map((item, index) => {
                  return (
                    <View className='carList__item' onClick={this.handleSelect.bind(this, index)}>
                      <Image
                        lazyLoad
                        className='carList__item--img'
                        src={item.img_url_high_light}
                      />
                      <View className='carList__item--name'>{item.name}</View>
                    </View>
                  );
                })}
              </View>
            </AtFloatLayout>
          </View>
        )}
      </Fragment>
    );
  }
}

export default Index;
