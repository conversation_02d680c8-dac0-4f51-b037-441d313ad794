/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { login } from '@/utils/qy';
import {
  refreshControl,
  REFRESH_KEY_CREDIT,
  REFRESH_KEY_CREDIT_CLICK,
} from '@/utils/refresh-control';
import { getLaunchParams } from '@base/utils/navigator';
import request from '@base/utils/request';
import { creatSchemeLink, getPage, reportAnalytics } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isUndefined from 'lodash/isUndefined';

const WxCreditAppId = 'wxd8f3793ea3b935b8';

/**
 * 先享后付指微信支付分、支付宝芝麻分等产品的服务
 */
export const CreditConfig = {
  text: process.env.PLATFORM_ENV == 'alipay' ? '芝麻先寄后付' : '微信支付分',
  icon: process.env.PLATFORM_ENV == 'alipay' ? 'zhima' : 'wxpay',
  colorClass: process.env.PLATFORM_ENV == 'alipay' ? 'kb-color__brand' : 'kb-color__green',
};

/**
 * 检查是否开通先享后付
 */
export const checkCreditService = (refresh) => {
  if (process.env.MODE_ENV !== 'wkd') return Promise.reject();
  return new Promise((resolve) => {
    let isOpenCredit = Taro.kbGetGlobalData('isOpenCredit');
    const ForceRefresh = refreshControl(REFRESH_KEY_CREDIT, 'check') || refresh;
    if (!isUndefined(isOpenCredit) && !ForceRefresh) {
      resolve(isOpenCredit);
      return;
    }
    login()
      .then(({ code }) => {
        request({
          url: '/g_order_core/v2/mina/Payment/getWxPayServiceStatus',
          toastLoading: false,
          data: {
            code,
          },
          onThen: (res) => {
            isOpenCredit = res.code == 0 ? res.data.serviceStatus : false;
            Taro.kbSetGlobalData('isOpenCredit', isOpenCredit);
            resolve(isOpenCredit);
          },
        });
      })
      .catch((err) => console.log(err));
  });
};

/**
 * 开通先享后付
 */
export const openCreditService = (data) => {
  reportAnalytics({
    key: 'wxpay_score',
    options: '点击开通',
  });
  return new Promise((resolve, reject) => {
    refreshControl(REFRESH_KEY_CREDIT);
    request({
      url: '/g_order_core/v2/mina/Payment/wxPayAuthOrder',
      data,
      toastError: true,
      nonceKey: 'order_id',
      formatRequest: (req) => {
        req = req || {};
        if (process.env.PLATFORM_ENV == 'alipay') {
          const { $router: { path = '' } = {} } = getPage();
          let link = creatSchemeLink({
            page: path.substring(0),
            query: 'creditSource=Credit',
          });
          link = encodeURIComponent(link);
          req = {
            returnBackLink: link,
            cancelBackLink: link,
            ...req,
          };
        }
        return req;
      },
      onThen: ({ code, data }) => {
        if (code == 0) {
          refreshControl(REFRESH_KEY_CREDIT_CLICK);
          if (process.env.PLATFORM_ENV == 'alipay') {
            const { type, sign, zm_service_id } = data;
            // eslint-disable-next-line
            const ZhiMaCredit = requirePlugin('ZhiMaCredit');
            ZhiMaCredit.startService({
              type,
              sign_str: sign,
              zm_service_id,
              success: resolve,
              fail: reject,
              complete: () => {},
            });
          } else {
            const { miniprogram_appid, miniprogram_path: path, extraData } = data;
            const appId = miniprogram_appid || WxCreditAppId;
            Taro.navigator({
              url: path,
              appId,
              extraData,
              report: {
                key: 'wxpay_score',
                options: '调用微信跳转接口',
              },
            })
              .then(() => {
                resolve({ appId });
              })
              .catch(reject);
          }
        }
      },
    });
  });
};

/**
 * 检查是否从支付分开通页面返回
 */
export const IsBackFromCredit = () => {
  const page = getPage();
  return new Promise((resolve, reject) => {
    if (process.env.PLATFORM_ENV == 'alipay') {
      const { creditSource } = getLaunchParams(page);
      if (creditSource == 'Credit') {
        resolve();
      } else {
        reject();
      }
    } else {
      const isBack = refreshControl(REFRESH_KEY_CREDIT_CLICK, 'check');
      // const { scene } = Taro.getLaunchOptionsSync();
      // if (scene == 1038 && referrerInfo && referrerInfo.appId == 'wxd8f3793ea3b935b8') {
      if (isBack) {
        resolve();
      } else {
        reject();
      }
    }
  });
};

/**
 * 用户确认支付分(支付宝芝麻分特有逻辑，因为协议内容等可能更改，需要用户确认;)
 */
export const confirmCredit = (data = {}) => {
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/mina/Payment/getOrderAfterPayStatus',
      data,
      onThen: resolve,
    });
  });
};

// 获取信用支付信息描述
export function getCreditInfo(serviceStatus) {
  let isAlipay = process.env.PLATFORM_ENV == 'alipay';
  if (serviceStatus) {
    return {
      image: `https://cdn-img.kuaidihelp.com/wkd/miniApp/credit/open${
        isAlipay ? '-alipay' : ''
      }.png?t=4`,
      qa: [
        {
          q: '开通后寄件流程是什么样呢？',
          a: '答：开通后您在“微快递”平台上选择申通、中通、极兔、顺丰或您合作的固定快递员进行下单，下单完成后您只需要等待快递员进行揽收货物即可，无需在线下支付快递运费，当货物在物流公司确认实际重量后，平台会以标准收费价格从您的账户余额进行扣费',
        },
        isAlipay
          ? {
              q: '如何关闭芝麻先寄后付?',
              a: '答：如需要关闭先寄后付授权，请进入【支付宝-芝麻信用-信用管理-授权管理-芝麻先用后付】中进行解除授权；',
            }
          : {
              q: '怎么样关闭授权的支付分呢？',
              a: '答：如需要关闭支付分扣款，请进入【微信-我-支付-钱包-支付分-点击右上角-服务管理-微快递（极速寄件）】进行解除授权；',
            },
      ],
    };
  }
  return {
    image: `https://cdn-img.kuaidihelp.com/wkd/miniApp/credit/unopen${
      isAlipay ? '-alipay' : ''
    }.png?t=4`,
    qa: isAlipay
      ? [
          {
            q: '什么是芝麻先寄后付?',
            a: '答：芝麻先寄后付是芝麻信用基于快递推出的寄件服务，芝麻分≥550的用户开通后可享受快递员先上门取件，后扣快递费的服务。快递费将按照用户在支付宝设置的扣款顺序完成。扣款成功可积累信用。',
          },
          {
            q: '开通后有什么好处？',
            a: '答：开通后用户寄件时无需先支付快递费，微快递将按照实际揽收成功的重量进行扣款，更方便、准确、安全。且寄件订单可积累信用。',
          },
        ]
      : [
          {
            q: '微信支付分是什么？安不安全？',
            a: '答：支付分是微信官方推出的一种对个人的身份特质、支付行为、使用历史等情况的综合计算分值，旨在为用户提供更简单便捷的生活方式。官方服务，请放心使用。',
          },
          {
            q: '开通后有什么好处？',
            a: '答：开通后寄件无需支付给快递员，微快递平台将以货物实际揽收重量进行扣款，保障您的资金安全及寄件利益。',
          },
        ],
  };
}

/**
 * 芝麻分二次绑单失败
 * 1、pay=2必须要开通支付分的会取消并删除订单
 * 2、pay=3优先走支付分的，重新推送第三方
 */
export function orderCreditFail(order_id) {
  return new Promise((resolve) => {
    if (!order_id) return;
    console.info('调用orderCreditFail接口');
    request({
      url: '/g_order_core/v2/mina/Payment/afterPayFail',
      data: {
        order_id,
      },
      toastLoading: false,
      onThen: resolve,
    });
  });
}

/**
 * 查询支付分订单授权状态
 */
export function authStatusQuery(order_id) {
  return new Promise((resolve) => {
    if (!order_id) return;
    request({
      url: '/g_finance_core/v2/PayAfterUsed/authStatusQuery',
      data: {
        order_number: order_id,
      },
      toastLoading: false,
      onThen: resolve,
    });
  });
}
