/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getLastUseRelation } from '@/components/_pages/store-card/_utils';
import logger from '@base/utils/logger';
import request from '@base/utils/request';
import { extractData, getPage, mergeBySpace } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';

/**
 *
 * @description 开启切换品牌弹窗
 * @param {*} data
 * @param {*} page
 */
function openSwitchBrandModal(data, page = getPage(), resolve, reject) {
  page &&
    page.setState({
      switchBrandData: {
        ...data,
        resolve,
        reject,
      },
    });
}

/**
 *
 * @description 引导切换品牌 ,actionName不存在时：order/cancel页面反馈后引导切换品牌
 * @param {*} param0
 * @returns
 */
export function guideSwitchBrand({ data, guide = true, actionName }) {
  if (!guide) return;
  let content = '是否需要转寄其他快递品牌？';
  const isFromCancelFeedback = !actionName;
  if (!isFromCancelFeedback) {
    content = `该订单已${actionName}，${content}`;
  }
  Taro.kbModal({
    content,
    cancelText: '不用了',
    confirmText: '选择其他快递转寄',
    onConfirm: () => {
      Taro.navigator({
        url: 'order/relation',
        target: isFromCancelFeedback ? 'self' : 'blank',
        key: 'globalData_orderDetail',
        extraData: {
          type: 'brand',
          action: 'brand-switch',
          brand: data.brand,
        },
        options: {
          data,
        },
      });
    },
    onClose: () => {
      if (!isFromCancelFeedback) {
        // 取消或删除订单
        const { order_id } = data;
        Taro.navigator({
          url: 'order/cancel',
          key: 'globalData_orderDetail',
          extraData: { order_id },
          options: {
            data,
          },
        });
      } else {
        Taro.navigator();
      }
    },
  });
}

/**
 *
 * @param {*} data
 * @param {*} page
 * @param {*} force 为true时，优寄或同城订单不做拦截，直接触发取消逻辑
 * @returns
 */
export const cancelOrder = ({ data, force = false, modal = true }, page = getPage()) => {
  return new Promise((resolve, reject) => {
    // type 主要用于区分tcjs|history date 历史订单日期
    const {
      order_id,
      activeEquityStatus,
      source,
      order_state,
      platform,
      brand,
      orderTypeAndDate: { type, date } = {},
      is_open_order,
      cabinet_info,
    } = data;

    let actionName = '取消';
    let content = '';
    let url = `/api/weixin/mini/minpost/${
      activeEquityStatus == '1' ? 'Pay/refundOrderPrice' : 'order/cancel'
    }`;
    let reqData = {
      order_id,
      refund_source: 'user_cancel',
    };

    // @微快递区分不同订单使用不同接口取消
    let guide = true;
    if (process.env.MODE_ENV === 'wkd') {
      // 已取消的订单
      if (order_state === 'canceled' && type !== 'receive') {
        return;
      }
      url = '/v1/order/wkdCancel';
      reqData = {
        order_number: order_id,
      };
      const typeValidList = ['tcjs', 'history']; // 有效的tabKey

      let orderType = ''; // 订单类型
      if (type === 'receive') {
        // 收到的订单
        actionName = '删除';
        url = '/v1/WeApp/delSharedOrder';
        if (source === 'wzg') {
          // 微掌柜订单
          url = '/g_order_core/v2/mina/WsOrders/delWsOrder';
          reqData = {
            order_no: order_id,
          };
        }
      } else if (order_state === 'temporary') {
        orderType = order_state;
        reqData = { order_id };
      } else if (source === 'dak') {
        orderType = source;
        reqData = { order_no: order_id };
      } else if (typeValidList.includes(type)) {
        orderType = type;
        reqData = {
          order_no: order_id,
          date,
        };
      } else if (platform === 'yjkd' && source !== 'premium') {
        orderType = platform;
      }
      switch (orderType) {
        case 'temporary': // 临时单
          url = '/v1/WeApp/orderCancel';
          break;
        case 'dak': // 驿站
          url = '/g_order_core/v2/mina/Dak/cancelDakOrder';
          break;
        case 'tcjs': // 同城
          // 跳转取消页面
          if (!force) {
            Taro.navigator({
              url: 'order/cancel',
              options: { order_id, type: orderType, brand, is_open_order },
            });
            return;
          }
          const { cancel_reason_id, cancel_reason } = data;
          url = '/g_wkd/v1/rush/Rush/cancel';
          reqData = {
            order_id,
            brand,
            cancel_reason_id,
            cancel_reason: cancel_reason,
          };
          if (is_open_order == 1) {
            url = '/g_wkd/v2/rushOrder/Order/cancel';
            reqData.reason = cancel_reason;
          }
          break;
        case 'yjkd': // 优寄
          // 触发是否切换品牌下单
          if (!force) {
            openSwitchBrandModal(data, page, resolve, reject);
            return;
          }
          guide = false;
          break;
        case 'history': // 历史订单
          url = '/g_order_core/v2/HistoryOrderC/orderHistoryCancel';
          break;

        default:
          break;
      }
      // 福利券使用
      const { help_status, activity } = data;
      if (activity || help_status) {
        if (!activity && help_status) {
          content = '本订单已使用福利券，订单取消后福利券退回账户，下次寄件可正常使用，请确认！';
        } else if (activity && help_status) {
          content =
            '本订单已使用福利券，并参与寄件领红包活动，订单取消后福利券退回账户，现金红包将作废，请确认！';
        } else {
          content = '本订单已参与寄件领红包活动，订单取消后现金红包将作废，请确认！';
        }
      }
    }

    const isKdgOrder = source === 'expresslocker';
    if (isKdgOrder) {
      const { status } = cabinet_info || {};
      if (status == '1') {
        Taro.kbToast({
          text: '包裹已被驿站取走，取消订单请联系驿站。',
        });
        return;
      }
      if (data.isPutIn == '1') {
        content = '请确定在您快递柜前，再取消订单';
      }
    }

    // 触发取消
    const triggerCancel = () => {
      request(
        {
          url,
          quickTriggerThen: true,
          toastError: true,
          toastSuccess: `已${actionName}`,
          data: reqData,
          onThen: ({ code, msg }, req) => {
            if (code == 0) {
              // 微快递触发是否换其他品牌寄件逻辑
              if (process.env.MODE_ENV === 'wkd') {
                modal && guideSwitchBrand({ data, guide, actionName }, page);
              }
              resolve(req);
            } else {
              reject(new Error(msg));
            }
          },
        },
        page,
      );
    };

    if (!modal) {
      // modal为false 直接触发取消，且不引导切换订单
      triggerCancel();
      return;
    }
    Taro.kbModal({
      content: content
        ? [{ text: content, className: 'kb-color__red' }]
        : `确定${actionName}该订单吗？`,
      centered: true,
      onConfirm: () => {
        triggerCancel();
      },
    });
  });
};

/**
 *
 * @description 编辑订单
 * @param {*} data
 */
export const editOrder = (data) => {
  const {
    relation = null,
    address = null,
    extraInfo,
    extraData,
    type = 'edit',
    action,
  } = data || {};
  const { type: relationType } = relation || {};
  if (relationType === 'company') {
    // 大客户编辑
    Taro.navigateBack();
    return;
  }
  if (relationType === 'tcjs') {
    // 同城急送
    Taro.navigator({
      url: 'order/delivery',
      key: 'globalData_tcjs',
      options: { relation, address },
    });
    return;
  }
  if (relationType === 'yhj') {
    // 优惠寄
    Taro.navigator({
      url: 'ws/order/edit-yhj',
      key: 'globalData_yhj',
      options: { relation, address },
    });
    return;
  }
  // 通用寄件
  if (relation) {
    logger.info('更新下单关系-获取2', relation.type);
    action !== 'back' && Taro.kbUpdateRelationInfo(relation);
  }
  Taro.navigator({
    url: 'order/edit',
    target: 'tab',
    options: {
      address,
      extraInfo,
      extraData,
      type,
    },
  });
};

/**
 * @description 获取订单当前的下单关系
 */
function getOrderRelationInfo(data, type = '') {
  const { source, dak, team, brand, brand_name, collect_courier_mobile, platform } = data;
  if (type === 'tcjs') {
    return {
      type,
      brand,
      name: brand_name,
    };
  }
  if (source === 'dak' && dak && dak.cm_id) {
    const { concat_area, concat_location, cm_id: dak_id, ...restDak } = dak;
    return {
      type: 'dak',
      address: (concat_area + concat_location).replace(/\s/g, ''),
      dak_id,
      ...restDak,
    };
  }
  if (source === 'team' && team && team.join_code) {
    return {
      type: 'team',
      ...team,
    };
  }
  if (source == 'online' || (platform == 'yjkd' && source != 'premium')) {
    return {
      type: 'brand',
      brand,
    };
  }
  if (collect_courier_mobile && source != 'online') {
    return {
      type: 'courier',
      brand,
      ...extractData(data, [
        ['courier_id', 'collect_courier_id'],
        ['account_phone', 'collect_courier_mobile'],
        ['account_name', 'collect_courier_name'],
        ['account_shop', 'express_shop_name'],
      ]),
    };
  }
}

/**
 *
 * @description 微快递克隆订单处理
 * @param {*} data
 * @param {*} param1
 */
function createAddressKeys(action) {
  const keys = [
    'name',
    'mobile',
    'province',
    'city',
    'district',
    'address',
    'house_num',
    'longitude',
    'latitude',
  ];
  const types = ['send', 'receive'];
  const list = [];
  types.map((type) => {
    list.push(
      ...keys.map((key) => {
        const from = `${type}_${key}`;
        let to = from;
        if (action === 'back') {
          to = `${types.find((current) => current !== type)}_${key}`;
        }
        if (key === 'mobile') {
          const by = to;
          to = (data) => mergeBySpace(data[by], data[by.replace('mobile', 'tel')]);
        }
        if (to !== from) {
          return [from, to];
        }
        return from;
      }),
    );
  });
  return list;
}
function getOrderInfo(order_id) {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/minpost/MiniDak/orderInfo',
      toastError: true,
      data: {
        order_id,
      },
      nonceKey: process.env.MODE_ENV === 'yz' ? 'third_order_id,order_id,order_random' : '',
      onThen: ({ data, code, msg }) => {
        if (code === 0) {
          resolve(data);
        } else {
          reject(msg);
        }
      },
    });
  });
}

function getOrderAddress(data, { action }) {
  return new Promise((resolve) => {
    const { receive_mobile, order_id } = data;
    const keys = [...createAddressKeys(action), ['courier_phone', 'counterman_mobile']];
    if (process.env.MODE_ENV !== 'wkd' && !receive_mobile) {
      getOrderInfo(order_id).then((order) => {
        resolve(extractData(order, keys));
      });
    } else {
      resolve(extractData(data, keys));
    }
  });
}

async function cloneOrderByEdit(data, { action }) {
  let relation = null,
    extraInfo = null;
  const { source, orderTypeAndDate: { type: orderType } = {} } = data;
  const key = source === 'yhj' ? source : orderType;
  switch (key) {
    case 'tcjs':
      // 急送订单
      // 跳转急送寄件页
      relation = getOrderRelationInfo(data, orderType);
      break;
    case 'yhj':
      // 优惠寄
      relation = { type: 'yhj' };
      break;

    default:
      const { goods_name, goods_weight, goods_remark, pic } = data;
      if (goods_name) {
        extraInfo = {
          goods_name,
          goods_weight,
          goods_remark,
          package_images: isArray(pic) ? pic : [],
        };
      }
      try {
        relation = await getLastUseRelation();
      } catch (err) {}

      break;
  }
  if (action == 'brand-switch') {
    relation = data.relation;
  }
  getOrderAddress(data, { action }).then((address) => {
    console.log('data', data);
    logger.info('再来一单', relation, address);
    editOrder({
      relation,
      address,
      extraInfo,
      type: 'clone',
    });
  });
}

/**
 *
 * @description  action： back - 寄回给发件人；clone - 再来一单/重新下单 data.relation存在 - 再来一单且固定下单关系
 * @param {*} data
 */
export const cloneOrder = (data, { action } = {}) => {
  cloneOrderByEdit(data, { action });
};

/**
 *
 * @description 申请退款或联系客服处理
 * @param {*} data
 */
export const refundOrder = (data) => {
  return new Promise((resolve, reject) => {
    const {
      order_id,
      refund_status,
      refundResult: { tips },
    } = data;
    const { url, data: requestData } = ((item) => item[refund_status])({
      refund: {
        url: 'apply',
      },
      custom: {
        url: 'refundMoneyAndStatus',
        data: {
          status: 4,
        },
      },
    });
    Taro.kbModal({
      content: `是否确定此订单${tips}?`,
      onConfirm: () => {
        request({
          url: `/g_order_core/v2/RefundApply/${url}`,
          data: {
            order_id,
            ...requestData,
          },
          toastError: true,
          toastSuccess: `已${tips}`,
          onThen: ({ code, msg }) => {
            code == 0 ? resolve() : reject(new Error(msg));
          },
        });
      },
    });
  });
};

/**
 *
 * @description 退卡
 * @param {*} data
 * @returns
 */
export const refundCard = (data) => {
  const { order_id } = data;
  return new Promise((resolve, reject) => {
    request({
      url: '/g_order_core/v2/RefundApply/refundMoney',
      data: {
        order_id,
      },
      toastError: true,
      toastSuccess: true,
      formatResponse: ({ code, data, msg }) => {
        if (code == 0 && data > 0) {
          return {
            code,
            msg: '退卡成功',
          };
        }
        return {
          code: 5003,
          msg: code > 0 ? msg : '退卡失败',
        };
      },
      onThen: ({ code, msg }) => {
        code == 0 ? resolve() : reject(new Error(msg));
      },
    });
  });
};
