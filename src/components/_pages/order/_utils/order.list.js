/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import apis from '@/utils/apis';
import request from '@base/utils/request';
import { dateCalendar } from '@base/utils/utils';
import dayjs from 'dayjs';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';
import { createOrderStatusInfo, formatOrderDetail } from './order.detail';
/**
 * 格式化合并列表选项
 */
function formatMergeOrderListItem(opts, req) {
  return (data) => {
    let item = data;
    if (process.env.MODE_ENV === 'wkd') {
      item = formatOrderDetail(data, {
        type: 'list',
        user_id: req.user_id,
        ...opts,
      });
    }
    const { create_time: $create_time, order_state } = item;
    const create_time = dateCalendar($create_time, {
      timer: true,
    });
    return {
      title: order_state === 'temporary' ? '待提交' : `下单时间：${create_time.split(/\s/)[0]}`,
      list: [
        {
          ...item,
          create_time,
          statusInfo: createOrderStatusInfo(item, opts),
          origin_create_time: item.create_time,
        },
      ],
    };
  };
}

/**
 *
 * @description @微快递 获取临时单列表
 * @param {*} searchValue
 * @param {*} currentPage
 * @returns
 */
function getByOrderReadyList(orderType, searchValue = '', currentPage = 0) {
  return new Promise((resolve) => {
    orderType === 'send' && currentPage == 1
      ? request({
          url: '/v1/WeApp/getByOrderReadyList',
          toastLoading: false,
          onThen: ({ data }) => {
            const list =
              isArray(data) && searchValue
                ? data.filter(({ send_user_mobile }) => `${send_user_mobile}`.includes(searchValue))
                : data;
            resolve(isArray(list) && list.length > 0 ? list : null);
          },
        })
      : resolve(null);
  });
}

/**
 *
 * @description 合并类表数据
 * @param {*} current
 * @param {*} list
 */
export const mergeOrderList = (current, list, callback) => {
  if (!isArray(current)) return list;
  current.map((item) => {
    const formattedItem = isFunction(callback) ? callback(item) : item;
    const { title: itemTitle, list: itemList } = formattedItem;
    const index = list.findIndex(({ title }) => title === itemTitle);
    if (index >= 0) {
      list[index].list = list[index].list.concat(itemList);
    } else {
      list.push(formattedItem);
    }
  });
  return list;
};

/**
 *
 * @param {*} res
 * @param {*} req
 */
export const formatResponseOrderList = (opts) => {
  return async (res, req) => {
    const { orderType, pageKey } = opts;
    const { data } = res;
    let list = null;
    let hasMore = void 0;
    // @微快递订单需要做特殊处理
    if (process.env.MODE_ENV === 'wkd') {
      const currentPage = req[pageKey];
      if (orderType === 'receive') {
        const { lists, pages } = data || {};
        list = lists;
        // 接口翻页实现有问题，需要客户端自行判断是否拉取下一页
        hasMore = currentPage < pages;
      } else {
        // 如果临时单存在则合并
        let readyList = await getByOrderReadyList(orderType, req.search, currentPage);
        if (readyList && isArray(data)) {
          readyList = readyList.concat(data);
        }
        list = readyList || data;
      }
    } else {
      list = data;
    }
    const hasList = isArray(list) && list.length > 0;
    if (hasList) {
      // 分组处理
      const listGroup = mergeOrderList(list, [], formatMergeOrderListItem(opts, req));
      let max_order_id = '';
      let lastOrder = list[list.length - 1] || {};
      if (lastOrder.order_state !== 'temporary') {
        max_order_id = lastOrder.max_order_id;
      }
      return {
        data: {
          max_order_id,
          list: listGroup,
        },
        hasMore,
      };
    }
    return {
      data: void 0,
    };
  };
};

/**
 *
 * @description 订单列表初始处理
 * @param {*} param0
 * @returns
 */
export const orderListInit = ({ tabKey = 'send', type }) => {
  // 不同请求的不同参数key
  let pageKey = 'page_num';
  let pageSizeKey = 'page_size';
  let extraData = null;
  let noMoreText = '已显示30天内全部订单';

  // 历史列表为新页打开，以type标识为准，其他以tabKey为准
  let orderType = tabKey;
  let orderDate = '';
  if (type === 'history') {
    orderType = type;
    orderDate = tabKey;
    extraData = { [process.env.MODE_ENV === 'wkd' ? 'date' : 'month']: tabKey };
  }

  if (process.env.MODE_ENV === 'wkd') {
    if (type === 'history') {
      pageKey = 'page_number';
    } else {
      switch (tabKey) {
        case 'company':
        case 'send':
          pageKey = 'page_number';
          extraData = { fields: 'express_rand' };
          break;
        case 'receive':
          pageKey = 'page';
          pageSizeKey = 'limit';
          break;
        case 'tcjs':
          pageKey = 'page';
          pageSizeKey = 'offset';
          noMoreText = '已显示全部订单';
          break;

        default:
          break;
      }
    }
  }

  return {
    pageKey,
    pageSizeKey,
    extraData,
    noMoreText,
    orderType,
    orderDate,
  };
};

// 创建历史记录tabs
export const createHistoryTabs = (size = 5) => {
  const tabs = [];
  const currentDate = dayjs();
  let months = size;
  while (months > 0) {
    const date = currentDate.subtract(months, 'months');
    const key = date.format('YYYY-MM');
    const title = date.format(`${date.isSame(currentDate, 'year') ? '' : 'YYYY年'}MM月`);
    tabs.unshift({
      key,
      title,
    });
    months--;
  }
  return tabs;
};

export const createOrderTabs = (type) => {
  // 微快递企业客户，订单tab
  if (process.env.MODE_ENV === 'wkd' && type === 'company') {
    return [
      {
        title: '企业寄出',
        key: 'company',
      },
      {
        title: '历史订单',
        key: 'history',
      },
    ];
  }
  if (type === 'all') {
    const tabs = [
      {
        title: '我寄出的',
        key: 'send',
      },
      {
        title: '我收到的',
        key: 'receive',
      },
      ...(process.env.MODE_ENV === 'wkd'
        ? [
            {
              title: '同城急送/拉货',
              key: 'tcjs',
            },
          ]
        : []),
      {
        title: '快递柜暂存',
        key: 'cabinet',
      },
      {
        title: '代取订单',
        key: 'delivery',
      },
    ];
    if (process.env.MODE_ENV === 'wkd') {
    }
    return tabs;
  }
  if (type === 'history') {
    return createHistoryTabs();
  }
  return null;
};
export function getUrlAndDataOrderDetail({ tabKey, type, extraData, pageSizeKey }) {
  // console.info('tabKey=====>', tabKey);
  // console.info('type=====>', type);
  // console.info('extraData=====>', extraData);
  // console.info('pageSizeKey=====>', pageSizeKey);

  let url = '',
    data = { [pageSizeKey]: 10, ...extraData };
  if (process.env.MODE_ENV === 'wkd') {
    url = apis[`order.${tabKey}`] || apis[`order.${type}`] || apis.order;
  } else {
    url = apis.order;
    if (type !== 'history') {
      switch (tabKey) {
        case 'receive':
        case 'cabinet':
        case 'delivery':
          url = apis[`order.${tabKey}`];
          break;
      }
    } else {
      data.month = tabKey;
    }
  }

  return {
    url,
    data,
  };
}
