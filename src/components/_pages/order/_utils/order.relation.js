/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { fixDynamicFormsData } from '@/components/_pages/order/_utils';
import { getBrandInfo } from '@/components/_pages/store-card/_utils';
import apis from '@/utils/apis';
// import request from '@/utils/request';
import isArray from 'lodash/isArray';

/**
 * @description 获取下单关系接口和数据
 */
export const getApiUrlAndDataRelation = ({ type }) => {
  let url;
  let data = null;
  switch (type) {
    case 'brand':
      url = apis['brand.list'];
      data = {
        is_new: '1',
        source: 'list',
      };
      break;
    case 'courier':
      url = '/v1/WeApp/lists';
      data = {
        limit: 30,
        type: 'all',
      };
      break;
  }
  return {
    url,
    data,
  };
};

/**
 * @description 获取下团队列表
 */
// function getTeamList() {
//   return new Promise((resolve) => {
//     request({
//       url: '/g_order_core/v2/mina/Courier/getTeamList',
//       data: { type: 'team', page: 1, limit: 15 },
//       toastLoading: false,
//       onThen: ({ data }) => {
//         const { list } = data || {};
//         resolve(isArray(list) ? list : []);
//       },
//     });
//   });
// }

/**
 *
 * @description 增加德邦小哥与团队列表
 * @param {*} req
 * @param {*} list
 * @param {*} isVip
 */
// async function addDpCourierAndTeamList(req, list) {
//   const { page = 1 } = req || {};
//   if (page == 1) {
//     const teamList = await getTeamList();
//     // 第一页加入德邦小哥
//     list.unshift(
//       {
//         type: 'brand',
//         account_name: '德邦小哥',
//         brand: 'dp',
//         account_phone: '95353',
//       },
//       ...teamList,
//     );
//   }
// }

/**
 *
 * @description 过滤vip快递员
 * @param {*} relationInfo
 * @param {*} list
 */
export function filterVipCourier(relationInfo, list, isVip) {
  if (!isVip) return list;
  const { account_phone } = relationInfo || {};
  let isListHasVip = list.find((item) => item.account_phone == account_phone);
  if (isListHasVip) {
    list = list.filter((item) => item.account_phone === account_phone);
  }
  // if (list.length === 0) {
  //   // 全部过滤掉没有vip选项，作为修正使用当前的 relationInfo
  //   return [relationInfo];
  // }
  return list;
}

/**
 *
 * @description 过滤品牌
 * @param {*} param0
 * @returns
 */
function filterBrand({ action, currentBrand }) {
  return (item) => {
    return !action
      ? true
      : action === 'brand-switch'
      ? item.brand !== currentBrand
      : item.type === action;
  };
}

/**
 *
 * @description 格式化响应 - 下单关系列表
 * @param {*} res
 * @param {*} req
 * @param {*} param2
 * @returns
 */
export const formatResponseRelation = async (res, req, { type, action, currentBrand }) => {
  const { data } = res;
  let list = null;
  switch (type) {
    case 'brand':
      list = isArray(data)
        ? data
            .filter(filterBrand({ action, currentBrand }))
            .map(({ message: describe, ...rest }) => ({ describe, ...rest }))
        : null;
      break;
    case 'courier':
      list = data.list;
      if (isArray(list)) {
        list = list.map(
          ({ focusStatus, payStatus, account_company: brand, courier_customer, ...rest }) => {
            const tags = [];
            focusStatus > 0 && tags.push('首次关注返券');
            payStatus > 0 && tags.push('下单在线支付返券');
            let item = {
              ...rest,
              customer: courier_customer && courier_customer.id ? courier_customer : '',
              brand,
              tag: tags.join(','),
            };
            if (item.type === 'dak') {
              item.inn_name = item.account_shop;
              item.dak_mobile = item.account_phone;
            }
            return item;
          },
        );
      } else {
        list = [];
      }
      break;
  }
  if (isArray(list) && list.length > 0) {
    return {
      data: { list },
    };
  }
  return {
    data: void 0,
  };
};

/**
 * @description 转移德邦小哥到队尾
 */
export const transferDpToLast = (list) => {
  const index = list.findIndex(({ brand, courier_id }) => brand === 'dp' && !courier_id);
  if (index >= 0) {
    const dp = list.splice(index, 1);
    list.push(...dp);
  }
  return list;
};

/**
 * @description 基础快递公司配置
 */
const BaseBrandConfig = {
  pay: '', //1线下支付;2线上支付;3优先线上付款;注:这里的线上付主要是指支付分;
  cutPayDesc: '', //开通支付分时扣款时机文案
  weightLimitMax: '', //重量限制最大多重
  weightLimitMin: '', //重量限制最小多重
  coupon_welfare: 0, //是否存在福利券业务
  goods_name: { isShow: true },
  goods_weight: { isShow: true },
  goods_remark: { isShow: true },
};

/**
 * @description 前端业务快递公司配置
 */
const FrontBrandConfig = {
  sto: {
    coupon_welfare: 1,
  },
  yt: {
    service: { isShow: true },
  },
  dp: {
    service: { isShow: true },
  },
  zt: {
    appointmentTime: { isShow: true, space: 2 },
  },
  jd: {
    product_code: {
      isShow: true,
      list: [
        { label: '特惠送', value: 'p1' },
        // { label: '特快送', value: 'p2' },
      ],
    },
    appointmentTime: { isShow: true },
    service: { isShow: true },
  },
  ys: {
    appointmentTime: { isShow: true },
  },
  yd: {
    appointmentTime: { isShow: true, space: 2 },
  },
  sf: {
    product_code: {
      isShow: true,
      list: [
        { label: '标快', value: 'offer' },
        { label: '特快', value: 'express' },
      ],
    },
    appointmentTime: { isShow: true },
    service: { isShow: true },
  },
  sfky: {
    product_code: {
      isShow: true,
      list: [
        { label: '重货包裹', value: 'SE0100' },
        { label: '小票零担', value: 'SE0101' },
      ],
    },
    appointmentTime: { isShow: true },
    service: { isShow: true },
    volume: { isShow: true },
  },
  ht: null,
  sxjd: {
    product_code: {
      isShow: true,
      list: [
        { label: '网点自提', value: '1' },
        { label: '送货上门', value: '2' },
      ],
    },
    volume: { isShow: true },
  },
};

/**
 * @description 最终快递公司配置
 */
export async function getBrandConfig(sBrand) {
  let BrandConfig = {};
  try {
    let list = await getBrandInfo();
    if (isArray(list) && list.length > 0) {
      list.map((item) => {
        const { brand, ...rest } = item || {};
        let oFrontBrandConfig = FrontBrandConfig[brand] || {};
        BrandConfig[brand] = {
          ...BaseBrandConfig,
          ...oFrontBrandConfig,
          ...rest,
        };
      });
      BrandConfig = sBrand ? BrandConfig[sBrand] : BrandConfig;
    }
  } catch (error) {}
  return BrandConfig;
}

/**
 * @description 获取下单对象配置
 */
export const getRelationConfig = async (relationInfo) => {
  const { type, brand, platform, dynamicForms = {} } = relationInfo || {};
  let defaultForms = {};

  switch (type) {
    case 'brand':
      if (platform == 'yjkd_courier') {
        defaultForms = {
          goods_name: { isShow: true },
          goods_weight: { isShow: true },
          goods_remark: { isShow: true },
          appointmentTime: { isShow: true },
          service: { isShow: true },
        };
      } else {
        defaultForms = await getBrandConfig(brand);
        //标记优寄快递品牌
        if (defaultForms && defaultForms.isYjkd == 1) {
          relationInfo.platform = 'yjkd_brand';
        }
      }
      break;
    case 'dak':
      defaultForms = {
        goods_name: { isShow: true },
        goods_weight: { isShow: true },
        goods_remark: { isShow: true },
      };
    case 'team':
      defaultForms = {
        goods_name: { isShow: true },
        goods_weight: { isShow: true },
        goods_remark: { isShow: true },
      };
      break;
    case 'courier':
      defaultForms = {
        goods_name: { isShow: true },
        goods_weight: { isShow: true },
        goods_remark: { isShow: true },
        card: { isShow: true },
        service: { isShow: true },
        brand: { isShow: true },
      };
      break;
  }
  // *可以考虑后续优化下这个判断
  if (type == 'brand') {
    relationInfo.dynamicForms = defaultForms;
  } else {
    relationInfo.dynamicForms = fixDynamicFormsData(dynamicForms, defaultForms);
  }
  //针对微快递支付宝版本的权益次卡缓存问题的特殊处理逻辑，上线一段时间后可以去掉
  if (type == 'courier' && relationInfo.dynamicForms) {
    relationInfo.dynamicForms.card = { isShow: true };
  }
  return relationInfo;
};

export const getRelationTabList = () => {
  let list = [
    {
      title: '快递公司',
      key: 'brand',
    },
    {
      title: '收藏快递员/驿站',
      key: 'courier',
    },
    {
      title: '附近驿站',
      key: 'dak',
    },
    {
      title: '同城急送/拉货',
      key: 'tcjs',
    },
  ];
  let removeKeys = [];
  if (process.env.PLATFORM_ENV === 'swan') {
    removeKeys = ['tcjs'];
  }
  list = list.filter((item) => !removeKeys.includes(item.key));
  return list;
};
