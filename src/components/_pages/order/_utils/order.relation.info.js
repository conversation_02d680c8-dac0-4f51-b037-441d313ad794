/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  formatShopInfo,
  getCourierInfo,
  getStoreInfo,
} from '@/components/_pages/store-card/_utils';
import {
  getApiCourierDetail,
  getCourierParams,
} from '@/components/_pages/order/_utils/courier.detail';
import apis from '@/utils/apis';
import { scanAction } from '@/utils/scan';
import { getPage } from '@base/utils/utils';

export const getRelationType = (oPage) => {
  const page = oPage || getPage(-1);
  const { phone, courierId } = getCourierParams(page.$router.params);
  return phone || courierId ? 'courier' : 'dak';
};
export const createBars = (type) => {
  const bars = [];
  if (process.env.MODE_ENV !== 'third') {
    // 非第三方
    bars.push({
      key: 'send',
      label: '寄件',
      explain: '便利快捷',
      around: 'blue-1',
      icon: 'send-3',
      iconColor: 'blue',
    });
  }
  if (type === 'dak') {
    // 驿站
    bars.push({
      label: '取件',
      icon: 'pickup',
      key: 'pickup',
      explain: '预约取件得积分',
      around: 'yellow-1',
      iconColor: 'yellow',
    });
  } else if (process.env.MODE_ENV === 'wkd') {
    // 非驿站且为微快递
    bars.push({
      key: 'message',
      icon: 'message-1',
      label: '留言',
      explain: '咨询反馈',
      around: 'yellow-1',
      iconColor: 'yellow',
    });
  }
  if (process.env.MODE_ENV !== 'third' && process.env.MODE_ENV !== 'wkd') {
    // 非定制普通版，且非微快递
    bars.push({
      key: 'reward',
      label: '加油包',
      icon: 'reward',
      explain: `给${type == 'dak' ? '驿站' : '快递员'}加油`,
      around: 'pink1',
      iconColor: 'red',
    });
  }
  return bars;
};
const getApiDakDetail = (params) => {
  const { dakId } = params || {};
  return {
    url: apis[`dak.detail`],
    data: process.env.MODE_ENV === 'wkd' ? { inn_id: dakId } : { dakId },
    nonceKey: process.env.MODE_ENV === 'wkd' ? 'inn_id' : 'dakId',
    formatResponse: ({ data, code }) => {
      if (code == 0) {
        return { data: formatShopInfo(data) };
      } else {
        return { data: void 0 };
      }
    },
  };
};
export const getApi = (_this) => {
  const type = getRelationType(_this);
  const page = _this || getPage(-1);
  let params = page.$router.params;
  return {
    ...(type == 'dak' ? getApiDakDetail(params) : getApiCourierDetail(params)),
    onIntercept(req, onThen) {
      const { q, qrCode } = params || {};
      // 特殊场景---扫码进入
      if (q || qrCode) {
        scanAction().then(({ dakId, courierId }) => {
          if (dakId) {
            _this.relationType = 'dak';
            getStoreInfo(dakId).then((data) => {
              onThen({ code: 0, data });
            });
          } else if (courierId) {
            _this.relationType = 'courier';
            getCourierInfo(courierId).then((data) => {
              onThen({ code: 0, data });
            });
          }
        });
        return true;
      }
    },
  };
};
