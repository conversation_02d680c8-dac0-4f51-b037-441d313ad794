/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { noop } from '@base/utils/utils';

/**
 *
 * @description 兼容微信与支付宝
 * @param {*} mapId
 * @param {*} $scope
 * @param {*} param2
 * @returns
 */
export const createMapContext = (
  mapId,
  $scope,
  { updateMarkers, updateIncludePoints, setCenterMarker },
) => {
  let ctx = null;
  if (Taro.createMapContext) {
    ctx =
      process.env.PLATFORM_ENV !== 'alipay'
        ? Taro.createMapContext(mapId, $scope)
        : Taro.createMapContext(mapId);
  }

  // 兼容代码，基础库版本更新到一定程度后可取消以下兼容代码
  const addMarkers = ({ markers: markers_, clear, success = noop }) => {
    updateMarkers([...(clear ? [] : markers_), ...markers_]);
    success();
  };

  if (!ctx) {
    ctx = {
      includePoints: ({ points }) => updateIncludePoints(points),
      moveToLocation: () => {},
      addMarkers,
      changeMarkers: addMarkers,
      translateMarker: ({ destination, animationEnd = noop }) => {
        setCenterMarker(destination, true);
        animationEnd();
      },
    };
  }

  if (process.env.PLATFORM_ENV === 'swan') {
    if (!ctx.addMarkers) {
      ctx.addMarkers = addMarkers;
    }
  }

  return ctx;
};
