/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { getPage } from '@base/utils/utils';
import isArray from 'lodash/isArray';

/**
 *
 * @description 获取优寄优惠列表
 * @param {*} order_id
 * @returns
 */
export const getCouponList = ({ order_id, ...restData }) => {
  if (process.env.MODE_ENV !== 'wkd') return Promise.reject();
  return new Promise((resolve, reject) => {
    request({
      url: '/v1/WeApp/getCouponList',
      data: order_id
        ? {
            type: 'yj',
            order_id,
          }
        : restData,
      toastLoading: false,
      onThen: ({ data }) => {
        const { list = data, count = 0 } = data || {};
        if (isArray(list) && list.length > 0) {
          const total = list.reduce((pre, cur) => pre + 1 * (cur.cost || 0), 0);
          resolve({
            count,
            list,
            total,
          });
        } else {
          reject(new Error('暂无数据'));
        }
      },
    });
  });
};

/**
 * @description 申请理赔
 */
export const applyIndemnity = (order_id) => {
  if (!order_id) return;
  request({
    url: '/g_order_core/v2/RefundApply/applyForPayment',
    data: {
      order_id,
    },
    toastSuccess: '申请成功',
    toastError: true,
    onThen: ({ code, data }) => {
      if (code == 0 && data > 0) {
        const page = getPage();
        page &&
          page.setState({
            data: {
              ...data,
              pay_status: 'applyRefundCoupon',
            },
          });
        if (page.handleRefresh) {
          page.handleRefresh();
        }
      }
    },
  });
};
