/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getStorage, setStorage } from '@base/utils/utils';
import Taro from '@tarojs/taro';

// 默认物品信息处理逻辑
const defaultGoodsStorageKey = 'defaultGoods';
export const defaultGoodsValue = '日用品';

export const setDefaultGoods = (value) => {
  const v = value || defaultGoodsValue;
  return new Promise((resolve) => {
    setStorage({
      key: defaultGoodsStorageKey,
      data: v,
    }).then(() => {
      Taro.kbSetGlobalData(defaultGoodsStorageKey, v);
      resolve(v);
    });
  });
};

export const getDefaultGoods = () => {
  let goods = defaultGoodsValue;
  return new Promise((resolve) => {
    const goodsData = Taro.kbGetGlobalData(defaultGoodsStorageKey);
    if (goodsData) {
      resolve(goodsData);
      return;
    }
    getStorage({
      key: defaultGoodsStorageKey,
    })
      .then((res) => {
        if (res && res.data && res.data.data) {
          goods = res.data.data;
        }
        Taro.kbSetGlobalData(defaultGoodsStorageKey, goods);
        resolve(goods);
      })
      .catch(() => {
        Taro.kbSetGlobalData(defaultGoodsStorageKey, goods);
        resolve(goods);
      });
  });
};
