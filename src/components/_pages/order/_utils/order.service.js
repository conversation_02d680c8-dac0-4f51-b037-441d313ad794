/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import isFunction from 'lodash/isFunction';

// 默认到付运费，有此值表示开启到付金额；
export const defaultToPayAmount = 9999;
// 检查是否是默认值
export function checkIsDefaultToPayAmount(amount) {
  return Number(amount) === defaultToPayAmount;
}

/**
 * 增值服务相关配置
 */

export const serviceConfig = {
  tel: {
    pageType: 'desc', //页面类型，纯描述
  },
  proPrice: {},
  collection: {},
  toPay: {},
  goodsValue: {},
  finish_code: {
    pageType: 'h5',
    h5: 'https://m.kuaidihelp.com/help/qhm',
  },
  privacyNumber: {
    pageType: 'h5',
    h5: 'https://m.kuaidihelp.com/help/ss_phone',
  },
};

// 0:service-list组件
// 1:寄件结果页
export function createServiceMap(type = '0') {
  let serviceMap = {
    pro_price: {
      title: '保价费',
    },
    dec_val: {
      title: '物品价值',
    },
    collection: {
      title: '代收金额',
    },
    arrive_pay: {
      title: '到付金额',
      formatter: (value) => {
        return checkIsDefaultToPayAmount(value)
          ? type === '1'
            ? {
                title: '到付运费',
                value: '是',
              }
            : {
                hideValue: true,
                title: '到付运费',
              }
          : {};
      },
    },
  };
  if (type === '1') {
    serviceMap = {
      ...serviceMap,
      collect_code: {
        title: '揽件码',
      },
      goods_name: {
        title: '生鲜件',
        check: (data) => !!data.fresh && data.goods_name,
      },
    };
  }
  return serviceMap;
}

/**
 *
 * @description 格式化增值服务
 * @param {*} data
 * @param {*} type
 * @returns
 */
export function formatServiceList(data, type) {
  const serviceMap = createServiceMap(type);

  const keys = Object.keys(serviceMap).filter((item) => {
    const { check } = serviceMap[item] || {};
    return isFunction(check) ? check(data) : !!data[item];
  });

  const serviceList = [];
  keys.map((item) => {
    const { title, formatter } = serviceMap[item];
    const value = data[item];
    serviceList.push({
      key: item,
      title,
      value,
      ...(isFunction(formatter) ? formatter(value) : {}),
    });
  });

  return serviceList;
}
