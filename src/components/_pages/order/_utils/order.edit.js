/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { addEcode, getEcodeInfo, showNotice } from '@/components/_pages/ecode/_utils';
import { formatAddress, orderAction } from '@/components/_pages/order/_utils';
import {
  authStatusQuery,
  checkCreditService,
  IsBackFromCredit,
  openCreditService,
  orderCreditFail,
} from '@/components/_pages/order/_utils/order.credit-pay';
import { saveRemoteImageToPhotosAlbum, setClipboardData } from '@/utils/qy';
import logger from '@base/utils/logger';
import { getLaunchParams } from '@base/utils/navigator';
import request from '@base/utils/request';
import {
  createGroup,
  extractData,
  getStorage,
  getStorageSync,
  isDataChange,
  isFullData,
  noop,
  removeStorage,
  setStorage,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';

/**
 *
 * @description 清空订单编辑表单信息
 * @param {*} keys
 * @param {*} replaceData
 */
export const cleanOrderEditFormInfo = (keys, replaceData = {}) => {
  const cleanDataMap = {};
  keys.map((item) => {
    cleanDataMap[item] = {
      clean: true,
      ...replaceData[item],
    };
  });
  return cleanDataMap;
};

/**
 * @description 检测是否存在优寄快递员
 */
export const checkYjkdCourier = function (isDefault) {
  const { relationInfo, form: { data: formData = {} } = {} } = this.state;
  const { type: relationType, brand, courier } = relationInfo || {};
  let reqData = extractData(formData, [
    ['shipper_province', 'send_province'],
    ['shipper_city', 'send_city'],
    ['shipper_district', 'send_district'],
    ['shipper_address', 'send_address'],
  ]);
  if (relationType == 'brand' && brand == 'yjkd' && isFullData(reqData)) {
    if (courier && this.pre_reqData && !isDataChange(reqData, this.pre_reqData)) return;
    this.pre_reqData = reqData;
    request({
      url: '/g_wkd/v2/Account/getPlaceType',
      toastLoading: false,
      data: reqData,
      onThen: (res) => {
        const { code, data } = res;
        let relationData = {};
        if (code == 0) {
          data.type == 'yjkd' && (data.type = 'sto');
          if (data.type == 'courier') {
            if (data.reserve_time) {
              data.reserve_time = data.reserve_time.map((item) => {
                return {
                  label: item.time,
                  value: item.time.replace(/小时内/, 'h'),
                };
              });
            }
            relationData = {
              type: 'brand',
              brand: 'yjkd',
              courier: {
                ...data,
              },
            };
          } else {
            relationData = {
              type: 'brand',
              brand: data.type,
            };
          }
          Taro.kbUpdateRelationInfo(
            {
              ...relationData,
              limitUpdate: (data) => {
                const { brand, type } = data;
                return !(brand === 'yjkd' && type === 'brand');
              },
            },
            isDefault,
          );
        }
      },
    });
  }
};

/**
 * @description 拦截下单对象:不活跃快递员;
 */
export const interceptChannel = function (callback = noop) {
  const {
    relationInfo: { type: relationType, account_phone },
  } = this.state;
  if (relationType == 'courier' && account_phone) {
    request({
      url: '/g_wkd/v2/Order/isDefaultYj',
      data: {
        courier_mobile: account_phone,
      },
      toastLoading: false,
      onThen: ({ code, data }) => {
        if (code == 0) {
          logger.info('是否活跃接口', code);
          const { user_nearly_is_send, is_courier } = data;
          if (user_nearly_is_send == 0 || is_courier == 0) {
            logger.info('不活跃快递员', account_phone);
            logger.setFilterMsg('不活跃快递员');
            callback(true);
          } else {
            callback(false);
          }
        } else {
          callback(false);
        }
      },
    });
  } else {
    callback(false);
  }
};

/**
 * @description 拦截支付分失败订单，即强制开通支付分，如极兔品牌强制开通支付分下单
 * @param action bind_fail 绑定失败;/auth_fail未授权;
 */
export const interceptCreditFailOrder = function (action, noBar) {
  if (!action) return;
  const chooseOtherBrand = () => {
    Taro.navigator({
      url: 'order/relation',
      options: {
        type: 'brand',
      },
    });
  };
  if (action == 'auth_fail') {
    Taro.kbModal({
      top: '下单失败',
      template: [
        {
          tag: 'view',
          className: 'kb-margin-md-b',
          value: `由于您未开通${
            process.env.PLATFORM_ENV == 'alipay' ? '芝麻先寄后付' : '微信支付分'
          }授权，导致您无法使用先寄后付尊享服务;`,
        },
        {
          tag: 'view',
          children: [
            {
              tag: 'text',
              className: 'kb-size__base',
              value:
                '注:先寄后付无需付款给快递员，平台将以货物实际揽收重量进行扣款，保障您的资金安全及寄件利益。',
            },
          ],
        },
      ],
      cancelText: '选择其他品牌寄件',
      confirmText: '去开通',
      onCancel: (ev) => {
        if (ev == 'button') {
          chooseOtherBrand();
        }
      },
      onConfirm: () => {
        this.handleOpenCredit();
      },
    });
  } else if (action == 'bind_fail') {
    Taro.kbModal({
      top: '下单失败',
      template: [
        {
          tag: 'view',
          value:
            '由于您先寄后付订单数量达到上限，本单无法享受先寄后付服务，需要您选择其他品牌进行下单',
        },
      ],
      confirmText: noBar ? '好的' : '选择其他品牌寄件',
      onConfirm: () => {
        !noBar && chooseOtherBrand();
      },
    });
  }
};

/**
 * @description 优寄快递员计费说明
 */
export const onShowFeeWay = function () {
  Taro.kbModal({
    className: 'kb-size__base',
    content: [
      {
        text: '1、先寄件，获取运单号后扣除运费',
      },
      {
        text: '目前支持微信支付分免密支付，您开通微信支付分后，待此订单获取运单号后，自动根据订单运费报价扣除您的快递费用。',
      },
      {
        text: '注：若您手填重量小于快递员称重重量，快递员会线下收取续重',
      },
      {
        text: '2、计费说明',
      },
      {
        text: '您填好寄收件人信息，会自动显示首重及续重计费公式，填好重量显示运费报价',
      },
    ],
  });
};

/**
 * @description 检测支付分开通情况
 */
export const handleCheckCreditService = function () {
  checkCreditService().then((isOpenCredit) => {
    this.setState(
      {
        isOpenCredit,
      },
      () => {
        IsBackFromCredit()
          .then(() => {
            if (process.env.PLATFORM_ENV == 'alipay') {
              const LastRequestFormData = getStorageSync('LastRequestFormData').data || {};
              const { isForceOpen, RequestFormData } = LastRequestFormData || {};
              if ((isForceOpen < 1 || (isForceOpen == 1 && isOpenCredit)) && RequestFormData) {
                removeStorage({ key: 'LastRequestFormData' });
                this.CreditFlag = false;
                this.IsBackFromCreditPage = true;
                this.formIns.submit(RequestFormData);
              }
            } else if (isOpenCredit && this.CreditFlag) {
              this.CreditFlag = false;
              this.onSubmit_form();
            }
          })
          .catch(() => {});
      },
    );
  });
};

/**
 * @description 执行开通支付分逻辑
 */
export const handleOpenCredit = function (storageData = '') {
  openCreditService().then(() => {
    this.CreditFlag = true;
    if (process.env.PLATFORM_ENV == 'alipay') {
      //兼容支付宝开通之麻分页面返回时会重启小程序，导致页面数据丢失问题，故采用缓存表单提交数据的方式解决
      if (storageData && storageData.req) {
        const { isForceOpen, req } = storageData || {};
        setStorage({
          key: 'LastRequestFormData',
          data: {
            isForceOpen,
            RequestFormData: req,
          },
        });
      }
    }
  });
};

/**
 * 支付宝芝麻分订单二次确认逻辑
 * @description 需要轮询订单二次授权状态
 * @description 确认失败需要删除订单
 * @description 主要是针对强制开通的支付分的需要删除，优先开通支付分的需要重推
 */
export const handleSecondConfirmOrder = function () {
  if (process.env.PLATFORM_ENV == 'alipay') {
    // 支付宝芝麻分订单二次确认逻辑---确认失败需要删除订单
    getStorage({
      key: 'LastOrderResultData',
      success: (res) => {
        const { creditSource } = getLaunchParams(this) || {};
        console.info('creditSource', creditSource);
        const LastOrderResultData = res.data && res.data.data;
        console.info('LastOrderResultData');
        console.info(LastOrderResultData);
        if (
          LastOrderResultData &&
          LastOrderResultData.order_id &&
          (creditSource == 'CreditAndOrderFail' || creditSource == 'CreditSuccess')
        ) {
          const { order_id, sh_order_number, pay } = LastOrderResultData || {};
          const confirmFail = () => {
            if (pay == 2) {
              this.interceptCreditFailOrder('bind_fail');
              orderCreditFail(order_id);
              removeStorage({ key: 'LastOrderResultData' });
            } else {
              Taro.navigator({
                url: 'order/result',
                options: {
                  creditSource: 'CreditFail',
                },
              });
            }
          };
          if (creditSource == 'CreditAndOrderFail') {
            // 强制开通支付分，确认失败
            confirmFail();
          } else {
            let maxTime = 3,
              n = 0,
              hasResLocked = false; //最多等待3秒
            let loading = Taro.kbToast({
              text: '订单状态查询中...',
              status: 'loading',
            });
            const closeLoadingFn = () => {
              hasResLocked = true;
              loading.close();
              this.SecondConfirmTimer && clearInterval(this.SecondConfirmTimer);
            };
            this.SecondConfirmTimer && clearInterval(this.SecondConfirmTimer);
            this.SecondConfirmTimer = setInterval(() => {
              n++;
              console.info('轮询中', n);
              authStatusQuery(sh_order_number).then(({ code, data }) => {
                if (n > maxTime || hasResLocked) return;
                const { authStatus } = data || {};
                if (code == 0 && (authStatus == 1 || authStatus == 2)) {
                  // 确认成功
                  console.info('确认成功');
                  Taro.navigator({
                    url: 'order/result',
                    options: {
                      creditSource: 'CreditSuccess',
                    },
                  });
                  closeLoadingFn();
                } else if (code != 0 || (code == 0 && authStatus == -1)) {
                  // 确认失败
                  console.info('确认失败');
                  confirmFail();
                  closeLoadingFn();
                }
              });
              // 超时-确认失败
              if (n > maxTime) {
                console.info('轮询结束');
                confirmFail();
                closeLoadingFn();
              }
            }, 1000);
          }
        }
      },
    });
  }
};

/**
 * @description 处理快递码相关逻辑
 */
export const handleECode = function (options = {}) {
  const { sign } = options || {};
  if (sign) {
    // 分享进入
    getEcodeInfo({ sign }).then(({ express_code, notice, ...rest }) => {
      const { data: address } = formatAddress(rest, 'receive', {
        reverse: true,
      });
      orderAction({
        action: 'edit',
        data: {
          address,
        },
      });
      showNotice(notice);
      addEcode({ express_code }, { toastLoading: false });
    });
  }
};

export const defaultFormData = {
  goods_name: '日用品',
};

/**
 * @description 获取某个品牌报价单详情
 */
export const getQuotationDetail = function (brand, quotationList) {
  quotationList = quotationList || Taro.kbGetGlobalData('quotation') || [];
  if (isArray(quotationList) && brand) {
    return quotationList.find((item) => {
      return item.brand == brand;
    });
  }
  return undefined;
};

export const getEditDefaultFormData = () => {
  const storage = getStorageSync('extraInfo').data || {};
  const defaultGoods = getStorageSync('defaultGoods').data || defaultFormData.goods_name;
  return { ...storage, ...{ goods_name: defaultGoods } };
};

/**
 * 绑定用户活动关系
 */
export const bindActivity = (share_id) => {
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/memberCard/MemberCard/userBindSup',
      data: {
        share_id,
      },
      onThen: resolve,
    });
  });
};
export const handleExtraData = function (res) {
  const { type } = res || {};
  switch (type) {
    case 'scanCoupon':
      // 扫码赠券
      this.setState({
        scanCoupon: res,
      });
      break;
    case 'focusCoupon':
      // 收藏优惠券
      this.couponModalRef.current.open(res);
      break;
    case 'miniPostPrinter':
      this.setState({
        miniPostPrinterData: res,
      });
      break;
  }
};

export const handleMiniPostPrinter = function (key, ev) {
  const { miniPostPrinterData } = this.state;
  const { url, qrCodeContent } = miniPostPrinterData || {};
  const close = () => {
    this.setState({
      miniPostPrinterData: '',
    });
  };
  switch (key) {
    case 'close':
    case 'cancel':
      //保存二维码
      ev == 'button'
        ? saveRemoteImageToPhotosAlbum({
            filePath: url,
          })
        : close();
      break;
    case 'confirm':
      Taro.navigator({
        appId: 'wx1a6252fee4475cc1',
        url: `pages/index/index?urlQuery=${qrCodeContent}`,
      });
      break;
  }
};

// 处理百世快递转换极兔快递弹窗
export const handleHTBrandModal = function (key) {
  const { hTBrandTipsData } = this.state;
  const close = () => {
    this.setState({
      hTBrandTipsData: 0,
    });
  };
  switch (key) {
    case 'open':
      let n = 3;
      this.setState({
        hTBrandTipsData: n,
      });
      this.htBrandTimer && clearInterval(this.htBrandTimer);
      this.htBrandTimer = setInterval(() => {
        if (n == 'finish') {
          this.htBrandTimer && clearInterval(this.htBrandTimer);
          return;
        }
        n--;
        if (n <= 0) {
          n = 'finish';
        }
        this.setState({
          hTBrandTipsData: n,
        });
      }, 1000);
      this.setState({
        relationData: {
          brand: 'jt',
        },
      });
      break;
    case 'close':
    case 'confirm':
      hTBrandTipsData == 'finish' && close();
      break;
  }
};

// 春节期间优寄品牌调整通知
export const handleSpringFestive = function (key) {
  const close = () => {
    this.setState({
      springFestiveOpen: false,
    });
  };
  switch (key) {
    case 'open':
      this.setState({
        springFestiveOpen: true,
      });
      break;
    case 'close':
      close();
      break;
    case 'confirm':
      openCreditService();
      close();
      break;
  }
};

// 德邦下单重量提示
export const handelDpWeightTips = function (key) {
  const { isOpenDpWeightNoMore } = this.state;
  const close = () => {
    this.setState({
      isOpenDpWeightTips: false,
    });
  };
  switch (key) {
    case 'close':
      close();
      break;
    case 'open':
      this.setState({
        isOpenDpWeightTips: true,
      });
      break;
    case 'confirm':
      this.handleSubmitForm('dpWeightTipsConfirm');
      if (isOpenDpWeightNoMore) {
        setStorage({
          key: 'isOpenDpWeightNoMore',
          data: {
            isOpenDpWeightNoMore: true,
          },
        });
      }
      close();
      break;
    case 'noMore':
      this.setState({
        isOpenDpWeightNoMore: !isOpenDpWeightNoMore,
      });
      break;
    case 'check':
      const res = getStorageSync('isOpenDpWeightNoMore').data;
      return (res && res.isOpenDpWeightNoMore) || false;
  }
};

// 快递员是否开启在线付
export const isOpenOnlinePay = function (courier_id) {
  if (!courier_id) return;
  return new Promise((resolve) => {
    request({
      url: '/v1/WeApp/isOpenOnlinePay',
      data: { courier_id },
      onThen: ({ code, data }) => {
        //不支持在线付时可以使用权益次卡
        this.setState({
          isOpenOnlinePay: code == 0 && data > 0,
        });
        resolve();
      },
    });
  });
};

export const handleSFKYDefaultWeight = function () {
  const {
    relationInfo: { type: relationInfoType, brand },
    extraInfo: { goods_weight } = {},
  } = this.state;
  if (relationInfoType == 'brand' && brand == 'sfky' && (!goods_weight || goods_weight < 20)) {
    this.setState({
      extraInfoData: {
        goods_weight: 20,
      },
    });
  }
};

/**
 * 批量提交订单
 */
const formatBatchOrderData = (receiveList = []) => {
  let list = [];
  if (receiveList && receiveList.length > 0) {
    list = receiveList.map((item) => {
      let oItem = {};
      if (item.extraInfo) {
        const { goods_name, goods_weight, goods_remark, service } = item.extraInfo || {};
        const { keep_account, cost_value, collection, arrive_pay } = service || {};
        goods_name && (item.product = goods_name);
        goods_weight && (item.weight = goods_weight);
        goods_remark && (item.note = goods_remark);
        item.decVal = keep_account; //声明物品价值
        item.proPrice = cost_value; //保价
        item.collection_amount = collection; //代收货款
        item.to_pay_amount = arrive_pay; //到付
      }
      oItem = extractData(item, [
        ['shipping_name', 'name'],
        ['shipping_mobile', 'mobile'],
        ['shipping_province', 'province'],
        ['shipping_city', 'city'],
        ['shipping_district', 'district'],
        ['shipping_address', 'address'],
        ['package_info', 'product'],
        ['package_weight', 'weight'],
        ['package_note', 'note'],
        'decVal',
        'proPrice',
        'collection_amount',
        'to_pay_amount',
      ]);
      return oItem;
    });
  }
  return list;
};
export function batchSubmitOrder(data) {
  // 触发请求
  return new Promise((resolve, reject) => {
    const { reqData } = data;
    const {
      relationInfo: { type: relationType },
    } = this.state;
    let addrList = formatBatchOrderData(this.receiveList);
    const MAX_NUM = 2000;
    const groups = createGroup(addrList, MAX_NUM);
    const groupLength = groups.length;
    let index = 0;
    const triggerSubmit = (index = 0, extraReqData = {}) => {
      const list = groups[index];
      request(
        {
          url: '/g_order_core/v2/mina/Order/batchSend',
          directTriggerThen: true,
          data: {
            ...reqData,
            ...extraReqData,
          },
          formatRequest: (req) => {
            req.type = relationType == 'brand' ? 'online' : relationType;
            req.shipper_list = {
              shipper_name: req.shipper_name,
              shipper_mobile: req.shipper_mobile,
              shipper_province: req.shipper_province,
              shipper_city: req.shipper_city,
              shipper_district: req.shipper_district,
              shipper_address: req.shipper_address,
            };
            req.shipping_list = list;
            return req;
          },
          onThen: ({ code, msg, data }, req) => {
            const { async_create_order, batch_number } = data || {};
            if (code == 0) {
              if (groupLength == index + 1) {
                // 订单上传完成
                if (async_create_order == 1) {
                  // 异步模式
                  this.checkAsyncBatchStatus(data);
                  resolve({ code, msg, data });
                } else {
                  // 同步模式
                  let oData = {};
                  if (data.status) {
                    oData.ordersNum = req.shipping_list.length;
                  }
                  oData = {
                    ...data,
                    ...oData,
                  };
                  resolve({ code, msg, data: oData });
                }
              } else {
                // 订单上传中
                index++;
                triggerSubmit(index, { batch_number, multi_batch: 1 });
              }
            } else {
              Taro.kbToast({
                text: msg,
              });
              reject({ code, msg, data });
            }
          },
        },
        this,
      );
    };
    // 提交订单
    triggerSubmit(index, { multi_batch: groupLength > 1 ? 1 : 0 });
  });
}

/**
 * 检查异步批量提交接口
 */
export function clearBatchStatusCheck() {
  this.checkAsyncBatchStatusTimer && clearTimeout(this.checkAsyncBatchStatusTimer);
}

export function checkAsyncBatchStatus(orderData = null) {
  const triggerDealRes = (data) => {
    const {
      total_order = 0,
      success_count = 0,
      fail_count = 0,
      fail_order_file = 0,
      loading,
    } = data || {};
    if (total_order > 0 || loading) {
      const total = success_count * 1 + fail_count * 1;
      if (total_order > total || loading) {
        // 提交中...
        data.status = 1;
      } else {
        // 提交完成
        if (fail_order_file) {
          // 存在失败单
          data.status = 2;
        } else {
          // 全部成功
          data.status = 3;
        }
      }
      const resultFn = () => {
        // console.log('异步提交订单，检查结果', data);
        if (data.status == 1) {
          triggerCheck();
        } else {
          this.clearBatchStatusCheck();
        }
        this.handleBatchOrderTips('open', data);
      };
      if (loading) {
        resultFn();
      }
      this.clearBatchStatusCheck();
      this.checkAsyncBatchStatusTimer = setTimeout(resultFn, 2000);
    }
  };
  const triggerCheck = () => {
    request({
      url: '/g_order_core/v2/mina/Order/asyncBatchSendResult',
      toastLoading: false,
      onThen: ({ code, data }) => {
        if (code == 0) {
          triggerDealRes(data);
        }
      },
    });
  };
  if (orderData) {
    orderData.loading = true;
    triggerDealRes(orderData);
  } else {
    triggerCheck();
  }
}

/**
 * 处理批量提交相关事件
 */
export function handleBatchOrderTips(key, data) {
  switch (key) {
    case 'open':
      this.setState({
        oAsyncBatchStatus: data,
      });
      break;
    case 'close':
      this.clearBatchStatusCheck();
      this.setState({
        oAsyncBatchStatus: {},
      });
      break;
    case 'copy':
      setClipboardData(data);
      break;
  }
}

/**
 * 拉取快递公告数据
 */
export function getExpressNotice() {
  const data = { position: 14 };

  if (process.env.PLATFORM_ENV === 'alipay') {
    data.platform = 'wkdaliapp'; // 支付宝
  } else if (process.env.PLATFORM_ENV === 'swan') {
    data.platform = 'wkdswan'; // 百度
  }

  request({
    url: '/g_order_core/v2/mina/User/getBannerList',
    data,
    toastLoading: false,
    onThen: (res) => {
      if (res.data && res.data.length > 0) {
        this.setState({
          expressNotice: res.data[0],
        });
      }
    },
  });
}

/**
 * 获取待支付订单
 *  */
export const getUnPayOrders = ({ courierId, relation_id }) => {
  return new Promise((resolve) => {
    const nonceKey = process.env.MODE_ENV == 'wkd' ? 'courierId' : 'relation_id';
    const reqData = process.env.MODE_ENV == 'wkd' ? { courierId } : { relation_id };
    request({
      url:
        process.env.MODE_ENV == 'wkd'
          ? '/v1/WeApp/userWaitPayOrderList'
          : '/api/weixin/mini/minpost/MiniDak/waitPayOrderList',
      data: reqData,
      nonceKey,
      toastError: false,
      toastSuccess: false,
      toastLoading: false,
      onThen: (res) => {
        if (isArray(res.data) && res.data.length > 0) {
          resolve(res.data);
        } else {
          resolve([]);
        }
      },
    });
  });
};

// 引导实名认证
export const guideRealName = (mobile) => {
  Taro.kbModal({
    content: '此驿站设置寄件人必须实名才能下单，系统检测到此寄件手机号码还未实名，赶快去实名吧~',
    confirmText: '去实名',
    cancelText: '',
    confirmButtonProps: {
      className: 'kb-clear__margin kb-button__middle',
    },
    onConfirm: () => {
      Taro.navigator({
        url: 'realname',
        options: {
          action: 'realname',
          phone: mobile,
        },
      });
    },
  });
};

// 格式化解析图片，统一为数组
export function formatParseImage(parseImg) {
  return parseImg
    ? isArray(parseImg)
      ? parseImg.map((item) => ({
          ...item,
          style: `width:${item.width}px;height:${item.height}px;max-width:100%;`,
        }))
      : [parseImg]
    : null;
}
export function previewParseImage(current, urls) {
  Taro.previewImage({
    current: current.img || current,
    urls: isArray(urls) ? urls.map((item) => item.img) : [urls],
  });
}

export function setAndPreviewParseImage(that, key, data) {
  switch (key) {
    case 'img':
      const { parseImg } = data || {};
      that.setState({ parseImg: formatParseImage(parseImg) });
      break;
    case 'preview':
      previewParseImage(data, that.state.parseImg);
      break;
  }
}
