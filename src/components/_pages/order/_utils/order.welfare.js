/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import isArray from "lodash/isArray";

/**
 *
 */
export const fillListByCount = (list, opts = {}) => {
  const { key = "uid", item, count = 3 } = opts;
  if (isArray(list)) {
    while (list.length < count) {
      list.push({
        ...item,
        [key]: `${key}-${list.length}`
      });
    }
  }
  return list;
};
