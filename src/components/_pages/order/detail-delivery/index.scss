/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.d_delivery_address {
  padding: $spacing-v-md;
  background-color: $color-white;
  border-radius: $border-radius-md;
  &__item {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    &__content {
      flex: 1;
      color: $color-grey-0;
      text-align: left;
    }
    &__status {
      flex-shrink: 1;
      font-size: $font-size-base2;
    }
  }
}

.d_delivery_package {
  margin-top: $spacing-v-md;
  background-color: $color-white;
  border-radius: $border-radius-md;
  &__list {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-v-md;
    font-size: $font-size-base2;
    border-bottom: $border-lightest;
    &:last-child {
      border-bottom: none;
    }
    &__content {
      flex: 1;
    }
  }
  &__total {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: $spacing-v-md;
    color: $color-grey-1;
    font-size: $font-size-base2;
  }
}

.d_delivery_info {
  padding: $spacing-v-md;
  background-color: $color-white;
  border-radius: $border-radius-md;
  &__item {
    display: flex;
    gap: $spacing-v-md;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-v-sm 0;
    &__label {
      color: $color-grey-2;
      font-size: $font-size-base;
    }
    &__values {
      display: flex;
      flex: 1;
      gap: $spacing-v-md;
      align-items: center;
      justify-content: flex-end;
      color: $color-grey-1;
      font-size: $font-size-base2;
      &__copy {
        width: 70px;
        height: 36px;
        color: $color-grey-1;
        font-size: $font-size-sm;
        line-height: 34px;
        text-align: center;
        background-color: #f2f2f2;
        border: $border-lightest;
        border-radius: 18px;
      }
    }
  }
}
