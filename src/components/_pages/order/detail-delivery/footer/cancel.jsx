/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { REFRESH_KEY_ORDER_DELIVERY, refreshControl } from '~/utils/refresh-control';
import KbButton from '~base/components/button';
import request from '~base/utils/request';

const DetailDeliveryFooterCancel = (props) => {
  const { order_id, handleUpdate = () => {} } = props;

  const onHandle = () => {
    Taro.kbModal({
      top: false,
      title: '温馨提示',
      content: '确认取消订单',
      centered: true,
      closable: false,
      cancelText: '取消',
      onConfirm: () => {
        request({
          url: '/api/weixin/mini/DakMini/HomeDeliverOrder/cancelOrder',
          data: {
            order_id,
          },
          toastSuccess: '取消成功',
          toastError: true,
          onThen: ({ code }) => {
            if (`${code}` === '0') {
              refreshControl(REFRESH_KEY_ORDER_DELIVERY);
              handleUpdate();
            }
          },
        });
      },
    });
  };

  return (
    <KbButton size='small' type='primary' circle onClick={onHandle}>
      取消订单
    </KbButton>
  );
};

export default DetailDeliveryFooterCancel;
