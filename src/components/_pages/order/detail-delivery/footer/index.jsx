/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import DetailDeliveryFooterCancel from './cancel';
import DetailDeliveryFooterPay from './pay';
import './index.scss';

const DetailDeliveryFooter = (props) => {
  const { orderInfo, handleUpdate } = props;
  const { order_status, order_id } = orderInfo || {};

  const is_show_cancel = ['1', '2', '3'].includes(order_status);
  const is_show_pay = order_status == '1';
  const is_show_footer = is_show_cancel || is_show_pay;

  return is_show_footer ? (
    <View className='d_delivery_footer'>
      {is_show_cancel && (
        <DetailDeliveryFooterCancel order_id={order_id} handleUpdate={handleUpdate} />
      )}
      {is_show_pay && <DetailDeliveryFooterPay order_id={order_id} handleUpdate={handleUpdate} />}
    </View>
  ) : (
    <View />
  );
};

export default DetailDeliveryFooter;
