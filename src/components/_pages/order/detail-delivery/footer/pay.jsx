/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import KbButton from '@base/components/button';
import { deliveryOrderPay } from '../_utils/utils';
import { REFRESH_KEY_ORDER_DELIVERY, refreshControl } from '~/utils/refresh-control';

const DetailDeliveryFooterPay = (props) => {
  const { order_id, handleUpdate = () => {} } = props;

  const onHandle = async () => {
    await deliveryOrderPay(order_id);
    refreshControl(REFRESH_KEY_ORDER_DELIVERY);
    handleUpdate();
  };

  return (
    <KbButton size='small' type='primary' circle onClick={onHandle}>
      立即支付
    </KbButton>
  );
};

export default DetailDeliveryFooterPay;
