/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text } from '@tarojs/components';
import Taro, { useMemo } from '@tarojs/taro';
import { dateCalendar } from '@base/utils/utils';
import { AtIcon } from 'taro-ui';
import KbBrand from '@/components/_pages/brand';
import { setClipboardData } from '~/utils/qy';
import { DELIVERY_ORDER_STATUS } from './_utils/utils';

import './index.scss';

const DetailDeliveryIndex = (props) => {
  const { orderInfo } = props;
  const {
    receiver_name,
    receiver_phone,
    first_level_address,
    second_level_address,
    receiver_address,
    order_status,
    package_list = [],
    paid_price,
    package_count,
    reserve_arrival_time,
  } = orderInfo;

  const statusObj = useMemo(
    () => DELIVERY_ORDER_STATUS.find((i) => i.key == order_status) || {},
    [order_status],
  );

  const columns = useMemo(() => {
    const {
      order_id,
      create_at,
      pay_date,
      rush_name,
      rush_mobile,
      pickup_time,
      arrival_time,
      receive_time,
      cancel_time,
    } = orderInfo;

    const formatDefaultTime = (str) => (str && str != '0000-00-00 00:00:00' ? str : '');

    return [
      {
        key: 'order_id',
        label: '订单编号',
        value: order_id || '',
        isCopy: true,
      },
      {
        key: 'create_at',
        label: '下单时间',
        value: formatDefaultTime(create_at),
      },
      {
        key: 'pay_date',
        label: '支付时间',
        value: formatDefaultTime(pay_date),
      },
      {
        key: 'receive_time',
        label: '接单时间',
        value: formatDefaultTime(receive_time),
      },
      {
        key: 'rush_name',
        label: '骑手',
        value: rush_name ? `${rush_name} ${rush_mobile}` : '',
      },
      {
        key: 'pickup_time',
        label: '取件时间',
        value: formatDefaultTime(pickup_time),
      },
      {
        key: 'arrival_time',
        label: '送达时间',
        value: formatDefaultTime(arrival_time),
      },
      {
        key: 'cancel_time',
        label: '取消时间',
        value: formatDefaultTime(cancel_time),
      },
    ].filter((i) => !!i.value);
  }, [orderInfo]);

  return (
    <View className='kb-spacing-md'>
      <View className='d_delivery_address'>
        <View className='d_delivery_address__item'>
          <AtIcon
            prefixClass='kb-icon'
            value='delivery_name'
            className='kb-size__xl kb-color__grey-ddd'
          />
          <View className='d_delivery_address__item__content'>
            {receiver_name} （{receiver_phone}）
          </View>
          <View className={`d_delivery_address__item__status ${statusObj.cnName}`}>
            {statusObj.label}
          </View>
        </View>

        <View className='d_delivery_address__item'>
          <AtIcon
            prefixClass='kb-icon'
            value='delivery_location'
            className='kb-size__xl kb-color__grey-ddd'
          />
          <View className='d_delivery_address__item__content kb-color__greyer kb-size__base2'>
            {`${first_level_address}${
              second_level_address ? '-' + second_level_address : ''
            } ${receiver_address}`}
          </View>
        </View>
        <View className='d_delivery_address__item'>
          <AtIcon
            prefixClass='kb-icon'
            value='delivery_time'
            className='kb-size__xl kb-color__grey-ddd'
          />
          <View className='d_delivery_address__item__content kb-color__greyer kb-size__base2'>
            {dateCalendar(reserve_arrival_time, { timer: true })} 送达
          </View>
        </View>
      </View>

      <View className='d_delivery_package'>
        {package_list.map((item) => (
          <View className='d_delivery_package__list' key={item.waybill_no}>
            <KbBrand size='md' brand={item.brand} />
            <View className='d_delivery_package__list__content'>
              <View className='kb-color__black kb-size__base2'>{item.waybill_no}</View>
              <View className='kb-size__base kb-color__grey'>
                {item.express_mobile}{' '}
                {item.shelve_type_desc == '小件包裹' ? '' : item.shelve_type_desc || ''}
              </View>
            </View>
            <View className='kb-color__brand'>{item.pickup_code}</View>
          </View>
        ))}
      </View>
      <View className='d_delivery_package__total'>
        <View>共{package_count || '--'}件,</View>
        <View>
          合计 <Text className='kb-color__red'>{paid_price || '--'}</Text> 元
        </View>
      </View>
      <View className='d_delivery_info'>
        {columns.map((item) => (
          <View className='d_delivery_info__item' key={item.key}>
            <View className='d_delivery_info__item__label'>{item.label}</View>
            <View className='d_delivery_info__item__values'>
              <Text>{item.value}</Text>
              {item.isCopy && (
                <Text
                  className='d_delivery_info__item__values__copy'
                  onClick={setClipboardData.bind(null, item.value)}
                >
                  复制
                </Text>
              )}
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

DetailDeliveryIndex.defaultProps = {
  orderInfo: {},
};

DetailDeliveryIndex.options = {
  addGlobalClass: true,
};

export default DetailDeliveryIndex;
