/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { requestPayment } from '@/utils/qy';
import Taro from '@tarojs/taro';

// 订单状态映射
export const DELIVERY_ORDER_STATUS = [
  {
    key: '-9',
    label: '已退款',
    cnName: 'kb-color__grey',
  },
  {
    key: '0',
    label: '已取消',
    cnName: 'kb-color__grey',
  },
  {
    key: '1',
    label: '待支付',
    cnName: 'kb-color__brand',
  },
  {
    key: '2',
    label: '待派送',
    cnName: 'kb-color__brand',
  },
  {
    key: '3',
    label: '待派送',
    cnName: 'kb-color__brand',
  },
  {
    key: '4',
    label: '派送中',
    cnName: 'kb-color__brand',
  },
  {
    key: '5',
    label: '已完成',
    cnName: 'kb-color__greyer',
  },
];

// 获取支付签名
const getPaySign = (order_id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/minpost/Pay/legWorkPaySign',
      data: {
        batch_no: order_id,
        source: 'home_deliver',
      },
      toastError: true,
      onThen: ({ code, data }) => {
        if (`${code}` === '0') {
          resolve(data);
        }
      },
    });
  });
};

// 支付动作
const payHandle = (sign) => {
  return new Promise((resolve) => {
    requestPayment(sign)
      .then(() => {
        Taro.kbToast({ text: '支付成功' });
        resolve();
      })
      .catch(() => {
        Taro.kbToast({ text: '支付失败' });
      });
  });
};

export function deliveryOrderPay(id) {
  return new Promise(async (resolve) => {
    const sign = await getPaySign(id);
    await payHandle(sign);
    resolve();
  });
}
