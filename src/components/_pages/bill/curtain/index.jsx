/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import getAdConfig from '../_utils/indesx';
import { AtCurtain } from 'taro-ui';
import { useUpdate } from '@base/hooks/page';
import './index.scss';

const KbBillCurtain = () => {
  const [isOpened, updateIsOpened] = useState(false);
  const [adConfig, setAdConfig] = useState(null);

  const handleClick = () => {
    adNavigator(adConfig);
    handleClose();
  };
  const handleClose = () => {
    updateIsOpened(false);
  };

  // 登录状态变更
  useUpdate((loginData) => {
    if (!loginData.logined) return;
    getAdConfig({ position: '17' }).then((data) => {
      const [ad] = data;
      if (ad && ad.id) {
        setAdConfig(ad);
        updateIsOpened(true);
      }
    });
  }, []);

  return (
    <AtCurtain isOpened={isOpened} onClose={handleClose}>
      <View className='kb-curtain' onClick={handleClick}>
        <Image mode='widthFix' src={adConfig.imgUrl} />
      </View>
    </AtCurtain>
  );
};

export default KbBillCurtain;
