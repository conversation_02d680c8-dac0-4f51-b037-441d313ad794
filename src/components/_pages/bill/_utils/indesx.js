/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import isArray from 'lodash/isArray';

const getAdConfig = (params) => {
  let data = {
    type: 'miniapp',
    platform: process.env.PLATFORM_ENV == 'weapp' ? 'wkdmini' : 'wkdaliapp',
    ...params,
  };
  return new Promise((resolve) => {
    request({
      url: '/g_tbk/v2/AdConfig/getAdConfig',
      data,
      toastLoading: false,
      onThen: (res) => {
        const { data } = res || {};
        resolve(isArray(data) ? data : []);
      },
    });
  });
};

export default getAdConfig;
