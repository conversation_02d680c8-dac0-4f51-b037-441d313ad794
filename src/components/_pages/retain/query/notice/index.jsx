/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';
import { View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import { useGetFollowStatus } from '@/components/_pages/official-account/_utils';
import classNames from 'classnames';
import './index.scss';

const Index = (props) => {
  const { loginData, className, hold, refresh, custom, showFollow, ...rest } = props;
  const { followAccount: { is_focus } = {} } = useSelector((state) => state.global);
  const rootCls = classNames('kb-button__mini', className);
  const wrapperCls = classNames({
    'kb-notice__hold': hold,
  });

  const [unlockGet] = useGetFollowStatus();

  const handleToFollow = () => {
    unlockGet();
    Taro.navigateToDocument(5);
  };

  const showChild = (!is_focus || showFollow) && !process.env.MODE_ENV.includes('third');

  return showChild ? (
    custom ? (
      <View>{props.children}</View>
    ) : (
      <View className={wrapperCls}>
        <AtButton circle type='primary' className={rootCls} onClick={handleToFollow} {...rest}>
          {props.children}
        </AtButton>
      </View>
    )
  ) : (
    <Fragment />
  );
};

Index.defaultProps = {
  refresh: null,
  custom: false,
  showFollow: false,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
