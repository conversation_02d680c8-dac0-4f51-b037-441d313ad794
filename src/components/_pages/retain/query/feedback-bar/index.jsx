/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState, Fragment } from '@tarojs/taro';
import KbButton from '@base/components/button';
import request from '@base/utils/request';
import { View } from '@tarojs/components';
import apis from '@/utils/apis';
import { createListener, noop } from '@base/utils/utils';
import { formatComplaintDataAndInfo } from '../_utils/query.appointment';

const Index = (props) => {
  const { dakId: propsDakId, custom, onChange, type, data, updateList, ...reset } = props;
  const [buttonText, updateButtonText] = useState(null);
  const [complaintInfo, updateComplaintInfo] = useState(null);
  const [active, updateActive] = useState(true);

  const { dakId } = data || {};
  const isOpened = propsDakId || dakId;

  useEffect(() => {
    if (!isOpened) return;
    const { complaintData, complaintInfo } = formatComplaintDataAndInfo({
      dakId: propsDakId,
      ...data,
    });
    if (!complaintInfo) {
      getComplaint(complaintData);
    }
    updateComplaintInfo(complaintInfo);
  }, [data]);

  useEffect(() => {
    let label = null;
    if (complaintInfo) {
      const { status, desc } = complaintInfo;
      label = '投诉反馈';
      if (status != '0') {
        if (desc) {
          label = '查看投诉';
        } else {
          label = '已投诉';
          updateActive(false);
        }
      }
    }
    updateButtonText(label);
  }, [complaintInfo]);

  // 获取投诉处理结果
  const getComplaint = (data) => {
    const { dakId: dak_id, brand, waybill } = data || {};
    if (!dak_id) {
      return;
    }
    request({
      url: apis['complaint.get'],
      toastLoading: false,
      data: { dak_id, brand, waybill },
      onThen: ({ data }) => {
        const { complaintInfo } = formatComplaintDataAndInfo(data, 'update');
        updateComplaintInfo(complaintInfo);
        onChange(complaintInfo);
      },
    });
  };

  const handleClick = (e) => {
    e.stopPropagation();
    // 投诉
    const { desc, status } = complaintInfo || {};
    if (status != '0') {
      // 已投诉
      Taro.kbModal({
        ...(desc
          ? { title: '投诉结果', content: desc }
          : {
              content: '您这个包裹已提交过投诉，投诉处理后您可点击【查看投诉】获知详情',
            }),
        confirmText: '知道了',
      });
    } else if (status == '-1') {
      Taro.kbModal({
        content: '该订单没有对应的驿站信息',
        confirmText: '知道了',
      });
    } else {
      createListener('submitFeedback', () => {
        getComplaint();
        updateList && updateList();
      });
      const { waybill, brand, dakId = propsDakId } = data;
      Taro.navigator({
        url: 'query/feedback/reason',
        options: { waybill, brand, dakId },
      });
    }
  };

  return (
    <Fragment>
      {isOpened && (
        <Fragment>
          {!custom ? (
            buttonText && (
              <KbButton
                hoverStopPropagation
                type={type}
                circle
                size='small'
                onClick={handleClick}
                active={active}
                {...reset}
              >
                {buttonText}
              </KbButton>
            )
          ) : (
            <View hoverStopPropagation onClick={handleClick} hoverClass='kb-hover'>
              {props.children}
            </View>
          )}
        </Fragment>
      )}
    </Fragment>
  );
};

Index.defaultProps = {
  data: null,
  dakId: '',
  type: 'primary',
  custom: false,
  onChange: noop,
};

Index.options = {
  addGlobalClass: true,
};
