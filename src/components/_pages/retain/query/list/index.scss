/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$query-tag-height: 82px;

.kb-query {
  &__list {
    padding-top: $width-base;
  }

  // 取件列表状态切换工具条
  &__tab {
    &--holder {
      height: 122px;
    }

    &--box {
      right: 0;
      left: 0;
      z-index: 99;
      height: $query-tag-height;
      padding: $spacing-v-md $spacing-h-md;
      background-color: $color-grey-8;
    }
    height: 100%;
    overflow: hidden;
    background-color: $color-white;

    border-radius: $border-radius-lg;

    &--item {
      height: $query-tag-height;
      padding: 0 $spacing-h-md;
      color: $color-grey-2;
      line-height: $query-tag-height;
      white-space: nowrap;
    }

    &--active {
      color: $color-brand;
    }
  }
}
