/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import Kb<PERSON>utton from '@base/components/button';
import Kb<PERSON>opyRight from '@/components/_pages/copy-right';
import <PERSON><PERSON><PERSON>ongList from '@base/components/long-list';
import KbStatusWrap from '@/components/_pages/status-wrap';
import KbSwipeAction from '@base/components/swipe-action';
import KbBrand from '@/components/_pages/brand';
import KbFeedbackBar from '@/components/_pages/query/feedback-bar';
import {
  formatRequestQueryList,
  formatResponseQueryList,
  getApiUrlAndDataQueryList,
  getExpressStatus,
  mergeListAndTopData,
  swipeActionOperator,
} from '@/components/_pages/query/_utils/query.list';
import { formatParamsOut } from '@base/components/_utils';
import { setClipboardData } from '@/utils/qy';
import { dateCalendar, noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Fragment, useEffect, useRef, useState } from '@tarojs/taro';
import classNames from 'classnames';
import isUndefined from 'lodash/isUndefined';
import { AtIcon } from 'taro-ui';
import './list-content.scss';

const deleteItem = {
  key: 'delete',
  text: '删除',
  style: {
    backgroundColor: '#FF5A7A',
  },
};
// 左滑选项，根据是否制定区分显示
const swipeActionOptions = {
  top: [
    {
      key: 'top',
      text: '置顶',
    },
    deleteItem,
  ],
  toped: [
    {
      key: 'top-cancel',
      text: '取消置顶',
    },
    deleteItem,
  ],
};
const typesMap = {
  mini_unsign: '待取',
  mini_signed: '',
  all: '',
};
const Index = (props) => {
  const {
    show,
    height,
    brands,
    dakId,
    dakAddress,
    type,
    active,
    enableRefresh,
    loadMore,
    onGetted,
    onListReady,
    adUnitIdIndex,
    onReady,
    ...reset
  } = props;
  const service = '取件';
  const { shareKey } = reset;
  // 指定驿站的包裹
  const isAppointment = !!dakId;
  // 投诉
  const isComplaint = type === 'complaint';
  // 他人分享过来的
  const isProxyPickup = !!shareKey;
  // 首页
  const isFirstPage = !isAppointment && !isComplaint;
  const actionRef = useRef({});
  // 空数据图标
  const emptyImage = {
    value: 'no-package',
  };
  // 更新列表
  const [list, updateList] = useState([]);
  // @微快递特有，更新物流信息
  const [flowData, updateFlowData] = useState(null);

  // 传入驿站id则拉取当前驿站的待取列表，否则拉取用户的物流列表
  // 列表请求配置
  const storageKey =
    process.env.MODE_ENV === 'yz' && isFirstPage && type === 'mini_unsign'
      ? `queryList_${type}`
      : '';
  const listData = {
    cachePrimary: false,
    storageKey,
    pageKey: 'pageNum',
    openLocalPageMode: isAppointment || isComplaint,
    api: {
      ...getApiUrlAndDataQueryList({
        isAppointment,
        isComplaint,
        isProxyPickup,
        type,
        dakId,
        ...reset,
      }),
      onReady,
      formatRequest: formatRequestQueryList({ actionRef }),
      formatResponse: formatResponseQueryList(
        {
          actionRef,
          isComplaint,
          isAppointment,
          isProxyPickup,
          type,
        },
        (data) => {
          updateFlowData({
            ...flowData,
            ...data,
          });
        },
      ),
      onThen: (list, res, req) => {
        updateList(list);
        onGetted(list, res, req);
      },
    },
  };

  // 列表准备就绪
  const handleLongListReady = (ins) => {
    actionRef.current.listIns = ins;
    onListReady(ins);
  };

  // 跳转
  const handleNavigator = (item) => {
    const {
      brand,
      waybill,
      isLike,
      expressStatus,
      dakId: itemDakId = dakId,
      order_no,
      note,
    } = item;

    // 左滑开启，禁止触发点击
    if (openedId) return;
    if (isLike == '1') return;
    if (isProxyPickup) return;
    if (getExpressStatus(expressStatus) == '待取件' && isFirstPage && itemDakId) {
      Taro.navigator({
        url: 'query/appointment',
        options: {
          dakId: itemDakId,
        },
      });
      return;
    }
    Taro.navigator({
      url: `query/${brand ? 'detail' : 'match'}`,
      options: {
        waybill,
        brand,
        dakId: itemDakId,
        order_no,
        note,
      },
    });
  };

  // 加载更多
  useEffect(() => {
    // 通过父组件触发翻页
    if (loadMore) {
      actionRef.current.listIns.more();
    }
  }, [loadMore]);

  // 获取品牌列表
  useEffect(() => {
    props.get();
  }, []);

  const handleCatch = (e) => e.stopPropagation();

  // 更新列表
  const handleUpdateList = () => actionRef.current.listIns.loader();

  // 左滑与长按
  const createItemKey = ({ brand = '', waybill = '' } = {}, index = -1) => {
    const key = `${brand}-${waybill}`;
    if (index >= 0) {
      return `${key}-${index}`;
    }
    return key;
  };
  const handleSeeImage = (item, e) => {
    e.stopPropagation();

    // 前一项为驿站的兼容项，第二项为固定输出项
    const keys = [
      ['img_url', 'certificate_path'],
      ['waybill', 'waybillno'],
      ['order_id', 'order_number'],
    ];
    const {
      certificate_path,
      waybillno,
      order_number,
      brand: express_rand,
      in_time,
      out_time,
      inn_name,
    } = formatParamsOut({
      data: { ...item },
      keys,
    });
    Taro.navigator({
      url: 'order/voucher',
      options: {
        certificate_path,
        waybillno,
        order_number,
        brand: express_rand,
        inn_name,
        dak_id: dakId,
        outTime: out_time,
        inTime: in_time,
      },
    });
  };

  const [openedId, updateOpenedId] = useState(null);
  const handleSwitchSwipeAction = (item, index) => {
    console.log('item', item);
    updateOpenedId(item ? createItemKey(item, index) : null);
  };
  // 选项处理
  const handleSwipeActionClick = (item, index, e) =>
    swipeActionOperator(item, e)
      .then((res) => {
        const { topList } = res || {};
        const { key } = e;
        if (topList) {
          // 优化性能与体验，如果返回置顶数据，则优先设置置顶显示状态；且置顶设置不再重新拉取数据
          mergeListAndTopData(list, type, topList)
            .then(updateList)
            .catch((err) => console.log(err));
          if (key.startsWith('top')) {
            // 设置置顶信息
            return;
          }
        } else if (key === 'delete' && index >= 0) {
          // 删除，体验优化，先更新视图再更新数据
          list.splice(index, 1);
          updateList(list);
        }
        handleUpdateList();
      })
      .catch((err) => console.log(err));
  // 长按选项
  const swipeActionLongTapOptions = (item) => {
    return [
      { key: 'copy', text: '复制单号' },
      { key: 'note', text: `${!item.note ? '添加' : '修改'}备注` },
      ...(item.isTop ? swipeActionOptions.toped : swipeActionOptions.top),
    ];
  };
  const handleCopy = (text) => {
    setClipboardData(text);
  };
  return (
    <KbLongList
      active={active}
      data={listData}
      enableMore
      enableRefresh={enableRefresh}
      scrollY={height !== 'auto'}
      onReady={handleLongListReady}
      height={height}
      topSpaceFix
      noDataText={
        isAppointment
          ? '暂无待取包裹'
          : !isUndefined(typesMap[type])
          ? `暂无${typesMap[type]}包裹`
          : '呀~包裹是空的~'
      }
      emptyImage={isFirstPage ? false : emptyImage}
      renderEmptyAd={
        <View className='kb-spacing-md-lr'>
          <KbExternalAd adUnitIdIndex={adUnitIdIndex} />
        </View>
      }
    >
      {show && (
        <View className='kb-list'>
          {list.map((item, index) => {
            const itemKey = createItemKey(item, index);
            // @微快递更新物流信息对应数据
            const flowDataItem = flowData ? flowData[createItemKey(item)] : {};
            const { status = getExpressStatus(item.expressStatus) } = item;
            const statusData = {
              status,
              ...flowDataItem,
            };
            const brandName = brands[item.brand] && brands[item.brand].name;
            const disabled = item.isLike == '1';
            const itemCls = classNames('kb-list__item--wrapper', {
              'kb-list__item--disabled': disabled,
            });
            if ('zt-75804680192927-0' === itemKey) {
              console.log('openedId', openedId);
            }
            const mobileType =
              'notRelationMobile' in item ? item.notRelationMobile : item.mobileType;
            const swipeOpened = openedId === itemKey;
            return (
              <Fragment key={itemKey}>
                <View className={itemCls}>
                  <KbSwipeAction
                    autoClose
                    disabled={!isFirstPage}
                    isOpened={swipeOpened}
                    onOpened={handleSwitchSwipeAction.bind(null, item, index)}
                    onClosed={handleSwitchSwipeAction}
                    onClick={handleSwipeActionClick.bind(null, item, index)}
                    options={item.isTop ? swipeActionOptions.toped : swipeActionOptions.top}
                    longTapOptions={swipeActionLongTapOptions.bind(null, item)}
                    title={item.note ? item.note : `${brandName} ${item.waybill}`}
                  >
                    <View
                      hoverClass={disabled && isProxyPickup ? 'none' : 'kb-hover'}
                      onClick={handleNavigator.bind(null, item)}
                    >
                      <View className='kb-list__item'>
                        <View className='kb-image--brand'>
                          <KbBrand brand={item.brand} />
                        </View>
                        <View className='item-content kb-margin-sm-l'>
                          <View className='item-content__title'>
                            <View className='at-row at-row__align--center at-row__justify--between'>
                              <View
                                className='kb-size__base at-row at-row__align--center kb-width-auto'
                                onClick={handleCatch}
                              >
                                {brandName && <Text className='kb-spacing-sm-r'>{brandName}</Text>}
                                <View
                                  onClick={handleCopy.bind(null, item.waybill, (e) =>
                                    e.stopPropagation(),
                                  )}
                                  stopPropagation
                                >
                                  <Text className='kb-header__text'>{item.waybill}</Text>
                                  <AtIcon
                                    prefixClass='kb-icon'
                                    value='copy-text'
                                    className='kb-icon-size__sm'
                                  />
                                </View>
                              </View>
                              <View className='kb-size__xs kb-color__grey'>
                                {dateCalendar(item.lastTime, { timer: true })}
                              </View>
                            </View>
                          </View>
                        </View>
                      </View>
                      <View className='at-row at-row__align--center kb-query__extra--box'>
                        <View className='kb-query__extra kb-size__base kb-color__grey at-col'>
                          {item.isMark == '1' && (
                            <View className='kb-query__extra--appointment'>
                              <AtIcon
                                prefixClass='kb-icon'
                                value='appointment'
                                className='kb-color__brand kb-icon-size__xxl'
                              />
                            </View>
                          )}
                          {process.env.MODE_ENV === 'wkd'
                            ? item.note && <View className='kb-spacing-sm-b'>{item.note}</View>
                            : null}
                          <View className='kb-query__extra--pickup'>
                            <View className='at-row at-row__align--center'>
                              <KbStatusWrap
                                className='kb-spacing-sm-r'
                                status={statusData.status}
                              />
                              {item.pickupCode &&
                                item.expressStatus != '0' &&
                                statusData.status != '退回件' && (
                                  <Text className='kb-size__bold kb-color__black'>
                                    取件码 : {item.pickupCode}
                                  </Text>
                                )}
                              {item.specialMark && (
                                <Text className='kb-query__extra--mark kb-margin-sm-l'>
                                  {item.specialMark}
                                </Text>
                              )}
                            </View>

                            {item.retreatReason && (
                              <View className='kb-spacing-sm-t kb-color__black'>
                                <Text>退回原因：</Text>
                                <Text>{item.retreatReason}</Text>
                              </View>
                            )}
                            {item.remark && (
                              <View className='kb-spacing-sm-t kb-color__grey kb-size__sm'>
                                <Text>备注：</Text>
                                <Text>{item.remark}</Text>
                              </View>
                            )}
                          </View>
                          <View>
                            {item.mobile && mobileType === 0 && (
                              <View className='kb-spacing-sm-t'>
                                亲友包裹
                                {item.mobileNote && `（${item.mobileNote}）`}：{item.mobile}
                              </View>
                            )}
                            {item.applyStatus == '2' && (
                              <View className='kb-spacing-sm-t kb-color__red'>
                                此包裹上次预约未能出库，详情请咨询站点工作人员
                              </View>
                            )}
                            {!isAppointment && (
                              <View className='at-row at-row__align--center at-row__justify--between kb-spacing-sm-t'>
                                <View
                                  className={
                                    process.env.MODE_ENV === 'wkd'
                                      ? 'kb-query__logistics'
                                      : 'kb-query__in-msg'
                                  }
                                >
                                  {flowDataItem.lastLogistics || item.lastLogistics || ''}
                                </View>
                              </View>
                            )}

                            {(item.expressOrderMsg || isAppointment) && (
                              <View className='at-row at-row__align--center at-row__justify--between kb-spacing-sm-t'>
                                {item.expressOrderMsg && (!isAppointment || isProxyPickup) && (
                                  <View className='kb-query__in-msg'>
                                    {process.env.MODE_ENV === 'wkd' ? (
                                      item.expressOrderMsg
                                    ) : (
                                      <Fragment>
                                        {statusData.status === '退回件'
                                          ? '操作驿站：'
                                          : '入库驿站：'}
                                        {item.expressOrderMsg}
                                      </Fragment>
                                    )}
                                  </View>
                                )}
                                {isAppointment && !shareKey && (
                                  <View className='at-row  kb-opt-wrap'>
                                    {item.img_url && (
                                      <View
                                        className='kb-margin-lg-r'
                                        hoverClass='kb-hover-opacity'
                                        hoverStopPropagation
                                        onClick={handleCatch}
                                      >
                                        <KbButton
                                          hoverStopPropagation
                                          onClick={handleSeeImage.bind(null, item)}
                                          className='kb-button__link'
                                        >
                                          包裹图片
                                        </KbButton>
                                      </View>
                                    )}
                                    <View hoverStopPropagation onClick={handleCatch}>
                                      <KbFeedbackBar
                                        className='kb-button__link kb-clear__background-color'
                                        dakId={dakId}
                                        data={item}
                                        updateList={handleUpdateList}
                                      />
                                    </View>
                                  </View>
                                )}
                              </View>
                            )}
                          </View>
                        </View>
                        {isComplaint && (
                          <View
                            className='kb-spacing-md-r'
                            hoverStopPropagation
                            onClick={handleCatch}
                          >
                            <KbFeedbackBar
                              type='secondary'
                              dakId={dakId}
                              data={item}
                              updateList={handleUpdateList}
                            />
                          </View>
                        )}
                      </View>
                    </View>
                  </KbSwipeAction>
                </View>
                {(index === 1 || list.length === 1) && adUnitIdIndex && (
                  <KbExternalAd adUnitIdIndex={adUnitIdIndex} wrapper />
                )}
              </Fragment>
            );
          })}
          {!isAppointment && !isFirstPage && <KbCopyRight service={service} />}
        </View>
      )}
    </KbLongList>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  dakId: null,
  brands: {},
  type: 'all',
  enableRefresh: true,
  show: true,
  height: 'auto',
  onGetted: noop,
  onReady: noop,
  onListReady: noop,
};

export default connect(
  ({ global: { brands = {} } }) => ({
    brands,
  }),
  {
    get,
  },
)(Index);
