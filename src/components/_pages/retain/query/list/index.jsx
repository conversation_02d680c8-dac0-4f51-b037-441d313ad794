/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbNotice from '@/components/_pages/query/notice';
import { useBoundingClientRect, useObserver } from '@base/hooks/observer';
import { debounce, getPage, isAvailableValue } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { Fragment, useCallback, useEffect, useMemo, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { AtButton, AtTabs, AtTabsPane } from 'taro-ui';
import { getStateCount } from '../_utils';
import KbListContent from './list-content';
import './index.scss';

const Index = (props) => {
  const idName = 'tab-box';
  const { loadMore, active, dakId, dakAddress, topID, mode, ...rest } = props;
  const isAppointment = !!dakId;

  // @微快递才会用到
  // eslint-disable-next-line
  let mobileBound = false;

  const { loginData: { userInfo: { mobile } = {} } = {} } = useSelector((state) => state.global);
  if (process.env.MODE_ENV === 'wkd') {
    mobileBound = !!mobile;
  }

  // 更新翻页
  const [loadMore_, updateLoadMore] = useState(null);
  // 更新统计数据
  const [stateCounts, updateStateCounts] = useState(0);
  // 状态切换
  const [current, updateCurrent] = useState(0);
  // 状态样式切换
  const [tabStyles, updateTabStyles] = useState(null);
  // 状态切换标签配置
  const tabs = useMemo(
    () =>
      process.env.MODE_ENV === 'wkd'
        ? [
            {
              title: '未签收',
              count: true,
              key: 'unsigned',
            },
            {
              title: '已签收',
              key: 'signed',
            },
            {
              title: '全部',
              key: 'all',
            },
          ]
        : [
            {
              title: '待取件',
              count: true,
              key: 'mini_unsign',
            },
            {
              title: '已取件',
              key: 'mini_signed',
            },
            {
              title: '全部',
              key: 'all',
            },
          ],
    [stateCounts],
  );

  // 切换状态
  const handleSwitchTab = useCallback((current) => {
    // 清空翻页状态，防止切换其他tab引发翻页错误
    updateLoadMore(null);
    updateCurrent(current);
  }, []);

  // @微快递，设置统计
  const handleSetStateCount = useCallback((_, res, req) => {
    if (process.env.MODE_ENV === 'wkd') {
      if (req.pageNum > 1) {
        return;
      }
      const { data: { count } = {} } = res;
      updateStateCounts(count);
    }
  }, []);

  // 获取统计
  const handleGetStateCount = useCallback(
    debounce((req) => {
      if (process.env.MODE_ENV !== 'wkd') {
        if (isAppointment || req.pageNum > 1) {
          // 预约取件页屏蔽统计请求
          return;
        }
        getStateCount().then((data) => {
          updateStateCounts(data);
        });
      }
    }),
    [isAppointment],
  );

  const [triggerObserver] = useObserver(({ intersectionRatio }, { topHeight }) => {
    updateTabStyles(
      intersectionRatio === 0
        ? {
            position: 'fixed',
            top: `${topHeight}px`,
          }
        : null,
    );
  });

  const [triggerGetBoundingClientRect] = useBoundingClientRect((list) => {
    const { customNavBarHeight = 0 } = getPage();
    // 顶部高度，加上元素本身高度
    const height = list.reduce((total, cur) => total + cur.height, customNavBarHeight);
    const { height: topHeight = 0 } = list.find((item) => item.id === topID) || {};
    triggerObserver(
      {
        top: -1 * height,
      },
      `#${idName}`,
      { topHeight: topHeight + customNavBarHeight },
    );
  });

  // 监听更新翻页状态
  useEffect(() => {
    updateLoadMore(loadMore);
  }, [loadMore]);

  // 监听状态切换栏当前位置
  const { customNavBarHeight } = getPage();
  useEffect(() => {
    if (isAppointment || !isAvailableValue(customNavBarHeight)) return;
    triggerGetBoundingClientRect([`#${idName}`, { selector: `#${topID}`, component: false }]);
  }, [customNavBarHeight]);

  // 跳转绑定亲友
  const handleToOrder = () => {
    Taro.navigator({
      url: 'order',
    });
  };
  return !isAppointment ? (
    <View className='kb-query__list'>
      <View id={idName} className='kb-query__tab--holder'>
        <View className='kb-query__tab--box' style={tabStyles}>
          <View className='kb-query__tab at-row at-row__align--center at-row__justify--between'>
            <View className='at-col'>
              <View className='at-row'>
                {tabs.map((item, index) => {
                  const itemCls = classNames('kb-query__tab--item', {
                    'kb-query__tab--active': index === current,
                  });
                  return (
                    <View
                      key={item.key}
                      className={itemCls}
                      hoverClass='kb-hover'
                      onClick={handleSwitchTab.bind(null, index)}
                    >
                      {item.title}
                      {item.count && stateCounts > 0 && (
                        <Fragment>
                          (<Text className='kb-color__red'>{stateCounts}</Text>)
                        </Fragment>
                      )}
                    </View>
                  );
                })}
              </View>
            </View>
            {process.env.PLATFORM_ENV == 'weapp' ? (
              <View className='kb-spacing-md-r'>
                <KbNotice type='secondary'>包裹通知</KbNotice>
              </View>
            ) : null}
            {process.env.MODE_ENV == 'wkd' && (
              <View className='kb-spacing-md-r'>
                <AtButton
                  type='secondary'
                  circle
                  className='kb-button__mini'
                  onClick={handleToOrder}
                >
                  我的订单
                </AtButton>
              </View>
            )}
          </View>
        </View>
      </View>
      <AtTabs
        tabList={tabs}
        className='kb-tabs__hidetab'
        onClick={handleSwitchTab}
        current={current}
        height='auto'
        swipeable={false}
      >
        {tabs.map((item, index) => (
          <AtTabsPane key={item.key} current={current} index={index}>
            <View>
              <KbListContent
                {...rest}
                active={current === index && active}
                loadMore={current === index && loadMore_}
                type={item.key}
                onReady={handleGetStateCount}
                onGetted={handleSetStateCount}
                show={current === index}
              />
            </View>
          </AtTabsPane>
        ))}
      </AtTabs>
    </View>
  ) : (
    <KbListContent
      {...rest}
      active={active}
      dakId={dakId}
      dakAddress={dakAddress}
      loadMore={loadMore_}
      onGetted={props.onGetted}
    />
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  topID: '',
  dakId: null,
  mode: 'normal',
};

export default Index;
