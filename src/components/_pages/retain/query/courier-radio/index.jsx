/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { getPage } from '@base/utils/utils';
import { useUpdate } from '@base/hooks/page';
import KbAudio from '@base/components/audio';
import request from '@base/utils/request';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const [list, updateList] = useState(null);

  const getRadio = () => {
    let requestData = data;
    let url;
    if (!requestData) {
      // 兼容老的实现方案，应主导传入data
      const { $router: { params } = {} } = getPage(-1);
      requestData = params;
    }

    if (process.env.MODE_ENV === 'wkd') {
      url = '/g_order_core/v2/mina/Dak/wxCourierMessage';
    } else {
      if (!requestData.dakId) {
        // 未带入指定信息的，略过
        return;
      }
      url = 'https://m.kuaidihelp.com/WkdGzh/openDakParam';
    }

    request({
      url,
      toastLoading: false,
      data: requestData,
      formatResponse: ({ data }) => {
        if (process.env.MODE_ENV === 'wkd') {
          if (data) {
            const { informInfo, ivrInfo } = data;
            const { content = '' } = informInfo || {};
            const { voice_path = '', voice_url = voice_path, txt: voice_text = '' } = ivrInfo || {};
            return {
              data: { content, voice_url, voice_text },
            };
          }
        }
      },
      onThen: ({ data }) => {
        const { content = '', voice_url = '', voice_text = '' } = data || {};
        const list = [];
        if (voice_text || voice_url) {
          list.push({
            label: '语音',
            content: voice_text,
            src: voice_url.replace('http:', 'https:'),
          });
        }
        if (content) {
          list.push({
            label: '消息',
            content,
          });
        }
        updateList(list);
      },
    });
  };

  useUpdate((data) => {
    data.logined && getRadio();
  });

  return list ? (
    <View className='kb-courier-radio__group'>
      {list.map((item) => (
        <View className='kb-courier-radio__box' key={item.label}>
          <View className='kb-courier-radio__title'>快递员的{item.label}：</View>
          <View className='kb-courier-radio__content'>
            {item.content}
            {item.src && (
              <View className='kb-courier-radio__content-audio'>
                <KbAudio src={item.src} />
              </View>
            )}
          </View>
        </View>
      ))}
    </View>
  ) : null;
};

Index.defaultProps = {
  data: null,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
