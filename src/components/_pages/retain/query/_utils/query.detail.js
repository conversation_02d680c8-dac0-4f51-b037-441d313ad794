/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getFollowStatus, getOfficialConfig } from '@/components/_pages/official-account/_utils';
import request from '@base/utils/request';
import { dateCalendar, formatInfoByContact, makePhoneCall, noop } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';

/**
 *
 * @description 创建物流详情页按钮
 * @param {*} data
 * @returns
 */
export const createQueryBars = (data) => {
  const bars = [];
  const { express, dakId, orderData } = data;
  const { tel, brand } = express || {};
  if (tel || (process.env.PLATFORM_ENV === 'weapp' && brand === 'sto')) {
    // 申通或者有号码
    bars.push({
      key: 'service',
      label: '联系客服',
      type: 'primary',
    });
  }
  if (orderData && orderData.id && !dakId) {
    // 查看订单
    bars.unshift({
      key: 'order',
      label: '查看订单',
      type: 'primary',
    });
  }

  return bars;
};

/**
 *
 * @description 修正物流状态信息
 * @param {*} status
 * @returns
 */
export const fixQueryInfoStatus = (status) => {
  return !status || status === '未知' ? '暂无物流' : status;
};

/**
 *
 * @description 更新备注信息
 * @param {*} data
 * @returns
 */
export function updateNoteModal(data) {
  return new Promise((resolve, reject) => {
    const { waybill, brand, note, order_no } = data;
    Taro.kbModal({
      top: `${note ? '修改' : '添加'}备注`,
      template: [
        {
          tag: 'at-input',
          placeholder: '不超过20个字',
          value: note || '',
          circle: true,
          border: false,
          name: 'note',
          type: 'number',
        },
        {
          className: 'kb-color__grey kb-size__base kb-spacing-md-t',
          value: '注：备注信息将添加到查件记录',
        },
      ],
      onConfirm: (e, close) => {
        updateNote({
          order_no,
          brand,
          waybill,
          ...e.data,
        })
          .then((data) => {
            close();
            resolve(data);
          })
          .catch(reject);
        return true;
      },
    });
  });
}

/**
 *
 * @description 更新备注信息
 * @param {*} opts
 * @returns
 */
export function updateNote(opts) {
  return new Promise((resolve, reject) => {
    const { waybill, brand, note, order_no = '' } = opts;
    let url, data;
    if (process.env.MODE_ENV === 'wkd') {
      url = '/v1/record/memoEdit';
      data = {
        note,
        order_no,
        deliverNo: waybill,
        deliverCode: brand,
        info: `${brand}-${waybill}-remark-${note}-end-`,
      };
    } else {
      url = '/api/weixin/mini/waybill/record/update';
      data = {
        brand,
        waybill,
        note,
      };
    }
    request({
      url,
      data,
      form: {
        note: {
          tag: '备注信息',
          max: 20,
        },
      },
      toastError: true,
      toastSuccess: '备注更新成功',
      quickTriggerThen: true,
      onThen: ({ code, msg }) => {
        code == 0 ? resolve({ note }) : reject(new Error(msg));
      },
    });
  });
}

/**
 *
 * @description 检查是否需要输入手机号
 * @param {*} data
 * @param {Function} callback
 * @returns
 */
export function checkNeedMobile(data, callback = noop) {
  return new Promise((resolve) => {
    const { is_enter_phone } = data;
    if (!is_enter_phone) {
      callback();
      return;
    }
    Taro.kbModal({
      template: [
        {
          className: 'kb-color__grey kb-size__base kb-spacing-md-b',
          value: '请输入运单号对应寄/收件人的四位手机尾号，进行查询',
        },
        {
          tag: 'at-input',
          placeholder: '请输入手机后四位尾号',
          value: '',
          circle: true,
          border: false,
          inputType: 'number',
          maxLength: 4,
          name: 'mobile',
        },
      ],
      onConfirm: (e, close) => {
        const {
          data: { mobile },
        } = e;
        if (mobile) {
          close();
          resolve({ mobile });
        }
        return true;
      },
    });
  });
}

/**
 *
 * @description 获取
 * @param {*} params
 */
export function getQueryNote(data) {
  return new Promise((resolve) => {
    request({
      url: '/v1/record/detail',
      toastLoading: false,
      data,
      onThen: ({ code, data }) => {
        if (code == 0 && data) {
          resolve(data);
        }
      },
    });
  });
}

/**
 *
 * @description 查询数据请求配置
 * @param {*} params
 * @param {*} callback
 * @returns
 */
export const getQueryApiUrlAndData = (opt) => {
  const { brand, waybill, ...rest } = opt || {};
  let isNeedFixParam = waybill && !brand && process.env.PLATFORM_ENV === 'swan';
  let urlAndData = isNeedFixParam
    ? {
        url: '/g_wkd/v2/mina/Dak/getBaiduPushInfo',
        data: {
          waybill,
        },
      }
    : {
        url: '/v1/multinfo/getInfo',
        userkey: 'user',
        data: {
          express_company: brand,
          deliver_no: waybill,
          params: ['ElapsedTime', 'GetStatus', 'GetWuliu_phone'],
          version: 'v1',
          ...rest,
        },
        formatRequest: (req) => {
          req.deliver_no = req.deliver_no || req.waybill;
          req.express_company = req.express_company || req.brand;
          if (process.env.PLATFORM_ENV === 'swan') {
            req.source = 'baidu';
          }
          return req;
        },
      };
  return urlAndData;
};
export const createQueryListData = (params, callback) => {
  return {
    api: {
      ...getQueryApiUrlAndData(params),
      onThen: (_, { code, data, msg }) => {
        const { brand, waybill } = params;
        let isNeedFixParam = waybill && !brand && process.env.PLATFORM_ENV == 'swan';
        if (isNeedFixParam) {
          let options = {
            waybill: data.waybill,
            brand: data.brand,
            dak_id: data.dak_id,
            dakId: data.dak_id,
          };
          data.brand && callback(options);
          if (code > 0 && msg) {
            Taro.kbToast({
              text: msg,
            });
          }
          return;
        }
        const { GetWuliu_phone } = data;
        let reg_date = /\d{4}-\d{1,2}-\d{1,2}\s(\d{1,2}:?)+/,
          reg_restr = /(\/@|@)|(\:919,)|inside|\#|\$|(\:\d+,inside\$)|\:\d*,/g;
        let list = [];
        if (isArray(GetWuliu_phone)) {
          list = GetWuliu_phone.reverse().map(function (value) {
            var date = value.match(reg_date) || '',
              info = value.replace(date[0] + ' ', '');
            info = info.replace(reg_restr, '');
            return {
              content: [
                dateCalendar(date[0], {
                  timer: true,
                }),
              ],
              titles: formatInfoByContact(info),
              icon: 'chevron-up',
              color: 'grey',
            };
          });
        }

        callback({ list, data });
      },
    },
  };
};

export function getOrderDetail({ order_no, waybill, brand }) {
  return new Promise((resolve) => {
    /**
     * 获取订单详情
     */
    let url,
      data = {};
    if (order_no) {
      url = '/v1/WeApp/getSharedOrder';
      data.order_number = order_no;
    } else if (waybill) {
      url = '/v1/order/getOrderByWaybill';
      data.brand = brand;
      data.waybill_no = waybill;
    }
    request({
      url,
      data,
      toastLoading: false,
      formatResponse: ({ code, data }) => {
        waybill && (data.express_number = waybill);
        brand && (data.express_rand = brand);
        if (code == 0 && data && order_no) {
          const {
            user_id,
            send_address_city,
            send_user,
            receive_address_city,
            receive_user,
            express_number,
            express_rand,
            order_number,
          } = data;
          return {
            code: 0,
            data: {
              user_id,
              shipper_city: send_address_city,
              shipper_name: send_user,
              shipping_city: receive_address_city,
              shipping_name: receive_user,
              express_number,
              express_rand,
              id: order_number,
            },
          };
        }
      },
      onThen: resolve,
    });
  });
}

export const addWaybillRecord = (data) => {
  /**
   * 添加单号到历史记录中
   */
  const { express_number, express_rand: brand, order_no, note } = data;
  const requestData = {
    no: [express_number || ''],
    brand,
    status: [''],
    order_no,
  };
  if (note) {
    requestData.note = [note];
  }
  request({
    url: '/v1/record/add',
    toastLoading: false,
    userkey: 'user',
    data: requestData,
  });
};

/**
 *
 * @description 查件部分按钮操作
 * @param {*} key
 * @param {*} opts
 */
export function queryAction(key, opts) {
  const { service, order_id, dakId, express_number, orderSource, order_random, inn_info } = opts;
  switch (key) {
    case 'service':
      // 联系客服
      if (!service) return;
      let { tel: phoneNumber, brand } = service;
      // 存在驿站信息的时候，优先联系驿站
      if (inn_info && inn_info.inn_mobile) {
        phoneNumber = inn_info.inn_mobile;
      }
      if (
        brand === 'sto' &&
        process.env.PLATFORM_ENV === 'weapp' &&
        !(inn_info && inn_info.inn_mobile)
      ) {
        Taro.kbActionSheet({
          items: ['申通在线客服', '申通客服电话', '微快递平台客服'],
          onClick: (index) => {
            switch (index) {
              case 0:
                Taro.navigator({
                  url: 'webview',
                  options: {
                    src: encodeURIComponent(
                      `https://95543.qiyukf.com/client?k=51551590dbef83c8b969e4726877a5d1&wp=1&robotShuntSwitch=1&robotId=75059&t=${encodeURI(
                        '快宝微快递',
                      )}`,
                    ),
                  },
                });
                break;
              case 1:
                makePhoneCall(phoneNumber);
                break;
              case 2:
                Taro.navigator({
                  url: 'user/service',
                });
                break;
            }
          },
        });
      } else {
        // 拨打电话
        makePhoneCall(phoneNumber);
      }
      break;
    case 'order':
      // 查看订单
      Taro.navigator({
        url: 'order/detail',
        options: {
          order_id,
          fromShare: 1,
          express_number,
          orderSource,
          order_random,
        },
      });
      break;
    case 'appointment':
      if (!dakId) return;
      Taro.navigator({
        url: 'query/appointment',
        options: {
          dakId,
        },
      });
      break;

    default:
      break;
  }
}

/**
 *
 * @description 订阅提示
 */
export function subscriptionTips() {
  if (process.env.PLATFORM_ENV === 'weapp') {
    getFollowStatus({ refresh: true }).then(({ is_focus, name: gzh_name }) => {
      const isShowGZhBtn = process.env.MODE_ENV === 'wkd' && !is_focus;
      Taro.kbModal({
        content: [
          '1.订阅通知后，查询的物流信息发生变更后，可在微信“服务通知”内获取物流提醒（仅一次）',
          `2.需物流信息全程提醒，请关注“${gzh_name}”公众号，物流信息发生变更后，可在公众号内获得物流提醒`,
        ],
        onConfirm: () => {
          if (isShowGZhBtn) {
            const { h5, name } = getOfficialConfig();
            if (
              process.env.MODE_ENV === 'third.pro' ||
              process.env.MODE_ENV === 'third' ||
              process.env.MODE_ENV === 'third.post'
            ) {
              Taro.navigator({
                url: 'user/qrcode',
                options: {
                  mode: 'official-account',
                },
              });
            } else {
              Taro.navigator({
                url: h5,
                target: 'webview',
                report: {
                  key: 'foucs_gzh_ad',
                  options: `${name}点击跳转广告位`,
                },
              });
            }
          }
        },
        confirmText: isShowGZhBtn ? '关注公众号' : '知道了',
      });
    });
  }
}

/**
 *
 * @description 切换品牌
 * @param {*} opts
 * @returns
 */
export function switchBrand(opts) {
  const { id, status_en, from_channel } = opts;
  if (id) {
    return;
  }
  if (status_en === 'signed' || from_channel) {
    Taro.kbToast({
      text: from_channel ? '电商品牌无法切换' : '快件已签收，无法切换品牌',
    });
    return;
  }
  Taro.navigator({
    url: 'brand',
  });
}
