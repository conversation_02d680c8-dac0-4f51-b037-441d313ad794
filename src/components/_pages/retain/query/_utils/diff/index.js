/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 *
 * @description 创建头部bars
 * @param isVip
 */
export function createGridBars(isVip = 0) {
  const bars = [
    {
      icon: 'bind',
      label: '关联亲友',
      desc: '绑手机号代取包裹',
      key: 'bind',
      url: 'user/relation',
    },
    {
      icon: 'scan-package',
      label: '身份码',
      desc: '自助出库仪扫码',
      key: 'IDcode',
      url: 'IDcode',
      options: {
        action: 'turnstiles',
      },
    },
    {
      icon: 'service',
      label: '投诉反馈',
      desc: '包裹相关问题反馈',
      key: 'feeback',
      url: 'query/feedback',
    },
    {
      icon: 'help',
      label: '帮助中心',
      desc: '快递小助手',
      key: 'help',
      url: 'help',
    },
  ];
  if (isVip != 1) {
    // help前增加附近驿站
    bars.splice(-1, 1, {
      icon: 'logo',
      label: '附近驿站',
      desc: '查找附近驿站',
      key: 'shop',
      url: 'order/shop',
    });
  }
  return bars;
}
