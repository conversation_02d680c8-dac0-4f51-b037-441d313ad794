/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 *
 * @description 创建头部bars
 * @param isVip
 */
export function createGridBars() {
  let bars = [
    {
      icon: 'van',
      label: '大件寄',
      desc: '重货上楼 寄件无忧',
      url: 'order/relation',
      options: {
        action: 'djj',
        type: 'brand',
      },
    },
    {
      icon: 'collect',
      label: '放心寄',
      desc: '极速上门 时效放心',
      url: 'order/relation',
      options: {
        action: 'fxj',
        type: 'brand',
      },
    },
    {
      icon: 'lightning',
      label: '急送/拉货',
      desc: '同城急送 拉货搬家',
      url: 'order/relation',
      options: {
        type: 'tcjs',
      },
    },
    {
      icon: 'vip',
      label: '会员寄',
      desc: '优惠寄件 每单返现',
      new: 'NEW',
      url: 'user/member',
    },
  ];

  let removeKeys = [];
  if (process.env.PLATFORM_ENV === 'swan') {
    removeKeys = ['lightning', 'vip'];
  }
  bars = bars.filter((item) => !removeKeys.includes(item.icon));
  return bars;
}
