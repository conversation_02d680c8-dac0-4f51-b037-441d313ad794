/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { createItemInfo, formatFlowData, updateFlow } from '@/components/_pages/query/_utils';
import { fixQueryInfoStatus, updateNoteModal } from '@/components/_pages/query/_utils/query.detail';
import { setClipboardData } from '@/utils/qy';
import request from '@base/utils/request';
import { getStorage, noop, removeStorage, setStorage } from '@base/utils/utils';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';

/**
 *
 * @description 物流查询列表请求配置
 * @param {*} props
 * @returns
 */
export const getApiUrlAndDataQueryList = (props) => {
  const {
    isAppointment,
    isComplaint,
    isProxyPickup,
    dakId,
    type,
    waybill: waybill_no,
    ...reset
  } = props;
  // 传入驿站id则拉取当前驿站的待取列表，否则拉取用户的物流列表
  // 列表请求配置
  const allKeys = ['all', 'complaint'];
  let url = '/api/weixin/mini';
  let data = null;
  let nonceKey = '';
  // @微快递
  if (process.env.MODE_ENV === 'wkd') {
    if (isComplaint || isAppointment) {
      if (isProxyPickup) {
        url = '/g_wkd/v2/mina/Dak/proxyPickupPackageList';
        data = { waybill_no, ...reset };
        nonceKey = 'batchId,shareKey';
      } else {
        url = '/g_wkd/v2/mina/Dak/uncollectedPackageByDak';
        data = {
          cm_id: dakId,
          waybill_no,
        };
      }
    } else if (type === 'all') {
      url = '/v1/record/getAllList';
      data = { type: 'unsigned' };
    } else {
      url = '/v1/record/getList';
      data = { type };
    }
    data.page_size = 20;
  } else {
    if (isComplaint || isAppointment) {
      url = url + '/DakMini/Record/';
      if (isComplaint) {
        url = url + 'innRecordComplaintList';
      } else if (isProxyPickup) {
        url = url + 'proxyPickupPackageList';
        data = { ...reset };
        nonceKey = process.env.MODE_ENV === 'yz' ? 'batchId,shareKey' : '';
      } else {
        url = url + 'uncollectedPackageByDak';
        data = { cm_id: dakId };
      }
    } else {
      url = url + '/waybill/record/expressList';
      data = {
        pageSize: type === 'mini_unsign' ? 5 : 20,
        state: allKeys.includes(type) ? null : type,
      };
    }
  }
  return {
    url,
    data,
    nonceKey,
  };
};

/**
 *
 * @description 格式化请求数据
 * @param {*} req
 * @param {*} param1
 * @returns
 */
export const formatRequestQueryList = ({ actionRef }) => {
  return (req) => {
    // @微快递
    if (process.env.MODE_ENV === 'wkd') {
      // 第一页不用追加上次请求的记录
      if (req.pageNum === 1) {
        return req;
      }
      return {
        ...req,
        ...actionRef.current.req,
      };
    } else {
      return req;
    }
  };
};

/**
 *
 * @description 格式化列表
 * @param {*} param0
 * @returns
 */
function formatListItem({
  deliverNo: waybill,
  deliverCode: brand,
  time: lastTime,
  pickup_code: pickupCode,
  express_status: expressStatus,
  dak_id: dakId,
  currentFlow,
  status,
  sourceMessage,
  expressOrderMsg = sourceMessage,
  ...rest
}) {
  return {
    waybill,
    brand,
    lastTime,
    pickupCode,
    expressStatus,
    dakId,
    lastLogistics: formatFlowData(currentFlow),
    status: fixQueryInfoStatus(status),
    expressOrderMsg,
    ...rest,
  };
}

/**
 *
 * @description 格式化响应
 * @param {*} param0
 * @param {*} req
 * @param {*} param2
 * @returns
 */
export const formatResponseQueryList = (
  { actionRef, isAppointment, isProxyPickup, type: currentType },
  callback = noop,
) => {
  return async ({ data }, req) => {
    let list,
      count = 0;
    // @微快递
    if (process.env.MODE_ENV === 'wkd') {
      // @微快递触发物流更新
      if (!isAppointment) {
        const { data: dataLit, cut_time, type = req.type, unsigned_count } = data;
        actionRef.current.req = {
          cut_time,
          type,
        };
        list = isArray(dataLit)
          ? await mergeListAndTopData(dataLit.map(formatListItem), currentType)
          : [];
        count = unsigned_count;
        updateFlow(list)
          .then(callback)
          .catch((err) => console.log(err));
      } else if (isProxyPickup) {
        // 代取分享
        list = data.list;
      } else {
        // 预约列表
        list = data;
      }
    } else {
      list = isArray(data) ? data : isObject(data) ? data.list || [] : [];
    }
    const hasList = isArray(list) && list.length;
    if (hasList) {
      return {
        code: 0,
        data: { list, count },
      };
    }
    return {
      data: void 0,
    };
  };
};

/**
 *
 * @description 删除
 * @param {*} data
 */
function deleteQueryItem(item) {
  const { order_no, orderId: order_id } = item;
  return new Promise((resolve, reject) => {
    let data = order_no ? { order_no } : { info: [createItemInfo(item)] };
    if (process.env.MODE_ENV === 'wkd') {
      if (order_id) {
        data.order_id = [order_id];
      }
    }
    request({
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/v1/record/delete'
          : '/api/weixin/mini/waybill/record/pickupListDelete',
      data,
      toastSuccess: '删除成功',
      toastError: true,
      quickTriggerThen: true,
      onThen: (res) => {
        const { code, msg } = res;
        if (code == 0) {
          resolve(res);
        } else {
          reject(new Error(msg));
        }
      },
    });
  });
}

/**
 *
 * @description 左滑操作
 * @param {*} item
 * @param {*} e
 * @returns
 */

export const swipeActionOperator = (item, e) => {
  return new Promise((resolve, reject) => {
    const { key } = e;
    switch (key) {
      case 'delete':
        // 删除
        deleteQueryItem(item)
          .then((res) => {
            if (item.isTop) {
              setTopData(item).then(resolve).catch(reject);
            } else {
              resolve(res);
            }
          })
          .catch(reject);
        break;
      case 'top':
      case 'top-cancel':
        // 置顶、取消置顶
        setTopData(item).then(resolve).catch(reject);
        break;

      case 'copy':
        // 复制单号
        setClipboardData(item.waybill, '运单号已复制');
        break;
      case 'note':
        // 修改备注
        updateNoteModal(item).then(resolve).catch(reject);
        break;

      default:
        break;
    }
  });
};

/**
 *
 * @description 获取置顶数据
 * @returns
 */
export function getTopData() {
  return new Promise((resolve) => {
    getStorage({
      key: 'topData',
      success: (res) => {
        const { data } = res.data || {};
        const { list, check } = data || {};
        if (isArray(list) && list.length > 0) {
          // array存在为老数据，需要格式化，新数据需要增加isTop
          resolve(check ? list.map(formatListItem) : list);
        } else {
          resolve(null);
        }
      },
      fail: () => resolve(null),
    }).catch((err) => console.log(err));
  });
}

/**
 *
 * @description 设置置顶数据
 * @param {*} data
 */
export function setTopData(data) {
  return new Promise((resolve, reject) => {
    getTopData()
      .then((top) => {
        const topList = top || [];
        const key = 'topData';
        const { isTop = false } = data;
        if (isTop) {
          const index = topList.findIndex((item) => createItemInfo(data) === createItemInfo(item));
          index >= 0 && topList.splice(index, 1);
        } else {
          topList.unshift({
            ...data,
            isTop: true,
          });
        }
        if (topList.length === 0) {
          removeStorage({
            key,
            success: () => resolve({ topList: [] }),
            fail: reject,
          });
        } else {
          // 只取前20条，由于缓存数据有限，目前最大置顶20条
          const list = topList.slice(0, 20);
          setStorage({
            key,
            data: {
              list,
            },
            success: () => resolve({ topList: list }),
            fail: reject,
          });
        }
      })
      .catch(reject);
  });
}

/**
 *
 * @description 数据与置顶数据合并
 */
export function mergeListAndTopData(list, currentType, top) {
  return new Promise((resolve) => {
    if (isArray(top)) {
      // 过滤掉置顶列表
      const topList =
        currentType !== 'all'
          ? top.filter(({ status }) => {
              const isSigned = status === '已签收';
              return currentType === 'signed' ? isSigned : !isSigned;
            })
          : top;
      const noTopList = list
        .filter((item) => !item.isTop)
        .filter((item) => {
          const index = topList.findIndex(
            (iitem) => createItemInfo(item) === createItemInfo(iitem),
          );
          if (index >= 0) {
            // 更新置顶数据
            topList[index] = {
              ...topList[index],
              ...item,
              isTop: true,
            };
            return false;
          }
          return true;
        });
      resolve([...topList, ...noTopList]);
    } else {
      getTopData()
        .then((topList) => {
          mergeListAndTopData(list, currentType, topList || [])
            .then(resolve)
            .catch(() => resolve(list));
        })
        .catch(() => resolve(list));
    }
  });
}

/**
 *
 * @description 根据expressStatus生成状态兼容
 * @param {*} expressStatus
 * @returns
 */
export const getExpressStatus = (expressStatus) => {
  switch (String(expressStatus)) {
    case '1':
      return '待取件';
    case '0':
      return '已签收';
  }
};
