/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbButton from '@base/components/button';
import KbCanvas from '@base/components/canvas';
import KbModal from '@base/components/modal';
import KbRadio from '@base/components/radio';
import KbSubscribe from '@base/components/subscribe';
import {
  getLegWorkMoney,
  proxyPickupBatchCreate,
} from '@/components/_pages/query/_utils/query.appointment';
import { Text, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { Fragment, useRef, useState } from '@tarojs/taro';
import { AtIcon, AtInput } from 'taro-ui';
import { checkExpressInfo } from '../_utils';
import './index.scss';

const Index = (props) => {
  const { list, data, onSubscribe, ...rest } = props;
  const actionRef = useRef({});
  const [template, updateTemplate] = useState(null);
  const { loginData } = useSelector((state) => state.global);
  const templateCanvasConfig = {
    width: 420,
    height: 336,
  };
  const [isOpened, upOpened] = useState(false);
  const [isOpenDelivery, upIsOpenDelivery] = useState(false);
  const [checked, upChecked] = useState('appointment');
  const [legMoney, upLegMoney] = useState(0);
  const [remark, upRemark] = useState('');
  const [showRemark, upShowRemark] = useState(false);
  // 触发绘制
  const triggerDraw = ({ dakAddress }) => {
    let shareSrc = `https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/share-appointment${
      process.env.MODE_ENV === 'third.post' ? '.third.post' : ''
    }.png`;

    updateTemplate([
      {
        action: 'image',
        data: {
          value: shareSrc,
        },
      },
      {
        action: 'text',
        data: {
          value: [!!dakAddress ? `驿站地址：${dakAddress}` : ''],
          color: '#ffffff',
          textAlign: 'left',
          baseLine: 'top',
          size: 20,
          coor: [20, 20, templateCanvasConfig.width - 40],
        },
      },
    ]);
  };

  // 关闭toast提示
  const closeToast = () => {
    actionRef.current.toastIns.close();
  };

  // 触发分享弹窗
  const triggerOpenShareModal = () => {
    const { image, info } = actionRef.current;
    if (image && info) {
      const { userInfo: { nickname } = {} } = loginData || {};
      closeToast();
      Taro.kbModal({
        content: [{ src: image }],
        confirmText: '立即分享',
        confirmButtonProps: {
          page: 'query.appointment',
          info: {
            title: `您的亲友(${nickname})给您发了一个取件包裹`,
            ...info,
          },
          image,
        },
      });
      Taro.kbSetGlobalData('dakInfo', {
        tempFilePath: image,
        dakAddress: data.dakAddress,
      });
    }
  };

  // 绘制完成
  const handleDrawComplete = (e) => {
    actionRef.current.image = e.tempFilePath;
    triggerOpenShareModal();
  };

  // 阻止冒泡
  const handleCatchClick = (e) => e.stopPropagation();

  // 订阅
  const { label, action } = checkExpressInfo(data);

  // list存在，则为预约取件列表
  const isPickup = list || action === 'pickup' || action == 'appointment';

  const handleSubscribe = () => {
    if (!list) {
      isPickup && handlePickUp();
    } else {
      const { dakId } = data;
      getLegWorkMoney(dakId)
        .then((money) => {
          upOpened(true);
          upLegMoney(money);
          upIsOpenDelivery(true);
        })
        .catch(() => {
          upIsOpenDelivery(false);
          upOpened(true);
        });
    }
  };

  const handlePickUp = () => {
    const { dakAddress, dakId, brand, waybill, isMarked } = data;
    const shareList = list || [{ brand, waybill, isMarked }];
    actionRef.current.toastIns = Taro.kbToast({
      status: 'loading',
      sleepOn: true,
      text: '正在生成分享图',
    });

    if (dakId) {
      // 获取分享标记
      proxyPickupBatchCreate({ dakId, list: shareList })
        .then((data) => {
          actionRef.current.info = data;
          triggerOpenShareModal();
        })
        .catch(closeToast);
    } else {
      closeToast();
    }

    const { tempFilePath, dakAddress: dakAddressUsed } = Taro.kbGetGlobalData('dakInfo') || {};
    if (tempFilePath && dakAddressUsed === dakAddress) {
      // 已有缓存的信息
      handleDrawComplete({ tempFilePath });
      return;
    }

    triggerDraw({ dakAddress });
  };
  const handlePickUpSelectChange = (key) => {
    upChecked(key);
  };

  const handleModalClose = () => {
    upOpened(false);
  };
  const handleModalConfirm = () => {
    upOpened(false);
    if (checked === 'pickup') {
      handlePickUp();
    } else if (checked === 'appointment') {
      onSubscribe(remark);
    } else {
      const { dakId, start_time: startTime, end_time: endTime, dakPhone } = data;
      console.log('data', data);
      Taro.navigator({
        url: 'query/appointment/delivery',
        options: {
          dakId,
          startTime,
          endTime,
          legMoney,
          remark,
          dakPhone,
        },
      });
    }
  };
  const handleRemarkChange = (text) => {
    upRemark(text);
  };
  const pickupSelectGroup = [
    {
      key: 'appointment',
      label: '预约取件',
    },
    {
      key: 'pickup',
      label: '好友、同事、家人代取',
    },
    {
      key: 'delivery',
      label: `送货上门(每件${legMoney}元)`,
    },
  ];
  return data ? (
    <Fragment>
      <View onClick={handleCatchClick} hoverStopPropagation>
        {!!label && (
          <KbSubscribe
            subscribe={isPickup}
            onSubscribe={handleSubscribe}
            openType={isPickup ? '' : 'share'}
            action='appointment'
            page='query.detail'
            info={data}
            {...rest}
          >
            {label}
          </KbSubscribe>
        )}

        {
          <KbCanvas
            template={template}
            {...templateCanvasConfig}
            hidden
            toImage
            onDrawComplete={handleDrawComplete}
          />
        }
      </View>
      <KbModal
        top='请选择代取人'
        isOpened={isOpened}
        onCancel={handleModalClose}
        onClose={handleModalClose}
        onConfirm={handleModalConfirm}
      >
        <View>
          {pickupSelectGroup.map((item) => {
            const { key, label } = item;
            return (
              <View>
                {!isOpenDelivery && key === 'delivery' ? null : (
                  <KbRadio
                    label={label}
                    className='kb-spacing-lg-t'
                    labelClassName='kb-size__lg'
                    checked={checked === key}
                    onChange={handlePickUpSelectChange.bind(null, key)}
                  />
                )}
              </View>
            );
          })}
        </View>
        <View className='kb-form--box'>
          {checked != 'pickup' && (
            <Fragment>
              {showRemark ? (
                <View className='at-row kb-form-container at-row__justify--between at-row__align--center'>
                  <View className='at-col at-col-9'>
                    <AtInput
                      maxLength={16}
                      placeholderClass='kb-color__grey kb-size__sm kb-line-height--40'
                      onChange={handleRemarkChange}
                      className='kb-input__circle kb-size__sm kb-background__grey kb-spacing-md-lr'
                      placeholder='填写备注（16个字以内）'
                      value={remark}
                    />
                  </View>
                  <View className='at-col-2 kb-spacing-xs-l'>
                    <KbButton
                      className='kb-button kb-button__small kb-spacing-sm-tb'
                      onClick={upShowRemark.bind(null, false)}
                      type='secondary'
                    >
                      取消
                    </KbButton>
                  </View>
                </View>
              ) : (
                <View
                  className='at-row kb-spacing-lg-tb at-row__align--center'
                  onClick={upShowRemark.bind(null, true)}
                >
                  <Text className='kb-spacing-sm-r kb-color__grey'>添加备注</Text>
                  <AtIcon
                    className='kb-color__brand kb-icon-size__base '
                    prefixClass='kb-icon'
                    value='edit-square'
                  />
                </View>
              )}
            </Fragment>
          )}
        </View>
      </KbModal>
    </Fragment>
  ) : null;
};

Index.defaultProps = {
  data: null,
  list: null,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
