/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { requestPayment } from '@/utils/qy';
import request from '@base/utils/request';
import WebSocket from '@base/utils/request/webSocket';
import { noop } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import Taro from '@tarojs/taro';
import MD5 from 'crypto-js/md5';
import dayjs from 'dayjs';

let ws;
let loading;
let pickup_code;
let timer;
let device_id;
export const usePickupWebsocket = (
  data,
  {
    url = 'wss://cabinet.kuaidihelp.com:19501',
    pathname = '/web',
    action = '/v1/Cabinet/heartbeat',
    onOpen = noop,
    onError = noop,
  } = {},
) => {
  const { loginData = {} } = useSelector((state) => state.global);
  const { userInfo } = loginData || {};
  const { sessionid, openid, nickname, mobile } = userInfo || {};
  const { type, ...cabinetData } = data || {};

  const showLoading = (text) => {
    loading = Taro.kbToast({
      status: 'loading',
      text,
    });
  };

  const connect = (v, isPay, deviceId) => {
    if (
      !isPay &&
      pickup_code &&
      (pickup_code == v || pickup_code == data.pickup_code) &&
      dayjs().diff(dayjs(timer), 'second') < 2
    ) {
      return;
    }
    pickup_code = v || data.pickup_code;
    device_id = deviceId || (cabinetData.cabinet_info && cabinetData.cabinet_info.device_id);
    timer = Date.now();
    if (ws) {
      sendMessage({
        type:
          type == 'input' ? 'customer_pickup_deliver_open' : 'customer_pickup_list_deliver_open',
        data: {
          pickup_code,
        },
      });
      return;
    }
    ws = new WebSocket({
      url,
      pathname,
      heart: {
        action,
        data: { timestamp: Date.now() / 1000, id: `${MD5(openid)}`, sessionid },
      },
      onOpen: () => {
        showLoading('正在连接 ...');
        sendMessage({
          type:
            type == 'input' ? 'customer_pickup_deliver_open' : 'customer_pickup_list_deliver_open',
          data: {
            pickup_code,
          },
        });
      },
      onMessage: (res) => {
        getMessage(res);
      },
      onStatusChange: (status) => {
        console.log('链接快递柜状态', status);
        if (status == 'close') {
          ws = null;
          if (loading) {
            loading.close();
          }
        }
      },
    });
  };

  const sendMessage = (params) => {
    const { type, data } = params;
    console.log('发送消息==>', params, device_id);

    const msg = {
      action: 'forward',
      data: {
        id: `${MD5(openid)}`,
        device_id,
        type,
        form: 'customer',
        courier_id: `${MD5(openid)}`,
        timestamp: Date.now() / 1000,
        data: {
          sid: `${MD5(openid)}`,
          courier_id: `${MD5(openid)}`,
          ecId: device_id,
          channel: 'web',
          phone: mobile,
          name: nickname,
          ...data, // 交互发送信息
        },
      },
    };
    showLoading('请稍后');
    ws && ws.sendMessage(msg);
  };

  const getMessage = (res) => {
    if (!res) return;
    const { data: latestMessageData = {}, code, msg } = res;
    const { type = '', data } = latestMessageData;
    console.info('type=====>', type);

    console.log('==>收到消息', res, latestMessageData, data);

    if (loading) {
      loading.close();
    }
    if (code != 0) {
      Taro.kbToast({ text: msg || '操作失败' });
      onError && onError();
      return;
    }

    switch (type) {
      // 开柜成功
      case 'customer_pickup_success':
        console.log('柜门已开==>');
        // Taro.kbToast({ text: '柜门已开' });
        onOpen && onOpen(latestMessageData.data);
        break;
      // 开柜查询是否存放超时需要支付
      case 'customer_pickup_out_of_date_payoff':
        Taro.kbToast({ text: '超时需要支付' });
        handlePay(data.timeoutInfo.payData).then(() => {
          connect(pickup_code, true, device_id);
        });
        // onError && onError();
        break;
      // 柜子查询不到该件
      case 'customer_pickup_fail':
      // 柜子再次开柜超过十分钟不允许再开，上报给c端
      case 'customer_pickup_out_of_date':
        onError &&
          onError({
            type,
            data: data,
          });
        const { shelfPackageList = [] } = data || {};

        // 如果是货架包裹，就不用toast,会有弹窗接管
        const isShelfPackage = type == 'customer_pickup_fail' && !!shelfPackageList.length;

        if (!isShelfPackage) {
          Taro.kbToast({ text: data.message || '操作失败' });
        }

        break;
      default:
        // Taro.kbToast({ text: msg || data.message || '未知操作类型' });
        console.log('未知操作类型');
        // dealWithFail('未知操作类型');
        break;
    }
  };

  return {
    connect,
  };
};

export const transformDakInfo = (data) => ({
  ...data,
  name: data.name || data.inn_name,
  address: data.address || data.inn_address,
  phone: data.phone || data.inn_mobile,
  longitude: data.longitude || data.inn_lng,
  latitude: data.latitude || data.inn_lat,
});

export const getPickUpList = (p) => {
  const { data = [], brands, search } = p;

  const { current = [], else_escrow_list = [], shelves_list = [] } = data[0] || {};

  const mapItem = (item, brands) => ({
    ...item,
    pickupCode: item.pickup_code,
    brandName:
      item.brand == 'qita' ? '其他' : (brands[item.brand] && brands[item.brand].name) || item.brand,
    cabinet_info: {
      id: item.ec_id || item.dak_id,
      device_id: item.device_id,
    },
    outTimeInfo: item.suspected > 0 ? null : item.outTimeInfo,
  });

  const list = search
    ? current.map((item) => mapItem(item, brands)).filter((item) => item.waybill_no == search)
    : current.map((item) => mapItem(item, brands));
  const else_list = else_escrow_list.map((item) => mapItem(item, brands));
  const shelves = shelves_list.map((item) => mapItem(item, brands));
  const other_list = current
    .map((item) => mapItem(item, brands))
    .filter((item) => item.waybill_no != search);

  return {
    list,
    else_list,
    shelves,
    other_list,
  };
};

export const removeItem = (data, cur) => {
  let { current = [], else_escrow_list = [], shelves_list = [] } = data[0] || {};
  current = current.filter((item) => item.pickup_code != cur.pickup_code);
  else_escrow_list = else_escrow_list.filter((item) => item.pickup_code != cur.pickup_code);
  shelves_list = shelves_list.filter((item) => item.pickup_code != cur.pickup_code);
  return [{ ...data[0], current, else_escrow_list, shelves_list }];
};

const handlePay = (data) =>
  new Promise((resolve, reject) => {
    const params = {
      ...data,
      out_trade_no: data.order_number,
      pay_method: 'wechat',
    };
    request({
      url: '/api/weixin/mini/DakMini/Record/cabinetPackageTimeoutPay',
      data: params,
    }).then((res) => {
      if (res.code == 0) {
        requestPayment(res.data)
          .then(() => {
            Taro.kbToast({ text: '支付成功' });
            resolve();
          })
          .catch(() => {
            Taro.kbToast({ text: '支付失败' });
            reject();
          });
      } else {
        reject();
        Taro.kbToast({ text: res.msg });
      }
    });
  });

// 扫码进入主副柜标识
export const CABINET_SUB_KEY = 'cabinet_is_sub';
export const OPENEDPACKAGE = 'openedPackage'
