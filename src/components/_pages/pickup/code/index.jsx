/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import classNames from 'classnames';
import './index.scss';

const KbCode = ({ handleOpenKeyBoard, value = '', length = 6 }) => {
  return (
    <View className='kb-codeWrap'>
      <View className='kb-codeWrap__code'>
        <View className='at-row at-row__justify--between' onClick={() => handleOpenKeyBoard()}>
          {Array.from({ length }).map((_, index) => (
            <View
              // eslint-disable-next-line react/no-array-index-key
              key={index}
              className={classNames({
                ['kb-codeWrap__input__text']: true,
                'kb-cursor': index == value.length,
              })}
            >
              {value[index]}
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

KbCode.options = {
  addGlobalClass: true,
};

export default KbCode;
