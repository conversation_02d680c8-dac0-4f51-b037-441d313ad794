/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-codeWrap {
  text-align: center;
  &__code {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  &__input__text {
    width: 80px;
    height: 80px;
    color: $color-black-0;
    font-size: 48px;
    line-height: 80px;
    text-align: center;
    border: 1px solid $color-grey-3;
    border-radius: $border-radius-md;
  }
  .kb-cursor {
    position: relative;
    background-color: #f2fbfa;
    border-color: $color-brand;
    &::after {
      position: absolute;
      top: $spacing-h-md;
      right: 50%;
      bottom: $spacing-h-md;
      width: 1px;
      background-color: $color-grey-1;
      transform: translateX(-50%);
      animation: cursor 1s infinite;
      content: '';
    }
  }
}

@keyframes cursor {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
