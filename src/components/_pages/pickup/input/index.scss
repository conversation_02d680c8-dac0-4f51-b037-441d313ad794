/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.pucInput {
  position: relative;
  display: flex;
  gap: $spacing-v-xs;
  align-items: center;
  justify-content: flex-start;
  height: 80rpx;
  margin-bottom: $spacing-v-lg;
  padding: 0 $spacing-v-md;
  font-size: $font-size-xxl;
  border: $border-lightest;
  border-radius: $border-radius-md;
  .placeholder {
    position: absolute;
    top: 50%;
    left: $spacing-v-md;
    color: $color-grey-3;
    font-size: $font-size-lg;
    transform: translate(0, -50%);
  }
  .kb-cursor {
    position: relative;
    width: 1px;
    height: $font-size-xxl;
    background-color: $color-brand;
    animation: cursor 1s infinite;
  }
}

.pucInputActive {
  background-color: #f2fbfa;
  border-color: $color-brand;
}
.pcInputAffix {
  position: absolute;
  top: 50%;
  right: $spacing-v-md;
  font-size: $font-size-sm;
  transform: translate(0, -50%);
}

@keyframes cursor {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
