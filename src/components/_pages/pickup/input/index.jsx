/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';

import './index.scss';

const CabinetPickupCodeInput = (props) => {
  const { visible, value, handleOpenKeyBoard, handleCopy } = props;

  return (
    <View
      className={`pucInput kb-margin-md-t ${visible ? 'pucInputActive' : ''}`}
      onClick={handleOpenKeyBoard}
    >
      {value ? <View>{value}</View> : <View className='placeholder'>请输入取件码</View>}
      <View className='pcInputAffix'>
        <View
          className='at-row at-row__justify--end kb-color__brand'
          hoverClass='kb-hover-opacity'
          onClick={(e) => {
            e.stopPropagation();
            handleCopy();
          }}
        >
          粘贴
        </View>
      </View>
      {visible && <View className='kb-cursor' />}
    </View>
  );
};

CabinetPickupCodeInput.options = {
  addGlobalClass: true,
};

export default CabinetPickupCodeInput;
