/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import './index.scss';

const KbKeyboard = ({ visible, setVisible, setValue, isCheckShelves }) => {
  const key = [1, 2, 3, 4, 5, 6, 7, 8, 9, isCheckShelves ? '-' : '', 0];
  return (
    <AtFloatLayout className='kb-maxZIndex' isOpened={visible} onClose={() => setVisible(false)}>
      <View className='kb-keyboard'>
        <View
          hoverClass='kb-hover'
          className='kb-keyboard__close'
          onClick={() => setVisible(false)}
        >
          <AtIcon className='kb-color__grey' prefixClass='kb-icon' value='dropDown' size='14' />
        </View>
        <View className='kb-keyboard__wrap'>
          {key.map((item) => {
            return (
              <View
                key={item}
                className='kb-keyboard__item'
                hoverClass='kb-hover'
                onClick={() => {
                  setValue(item);
                }}
              >
                {item}
              </View>
            );
          })}
          <View
            hoverClass='kb-hover'
            className='kb-keyboard__item'
            onClick={() => setValue('delete')}
          >
            <AtIcon className='kb-color__grey' prefixClass='kb-icon' value='keydelete' size='20' />
          </View>
        </View>
      </View>
    </AtFloatLayout>
  );
};

KbKeyboard.options = {
  addGlobalClass: true,
};
export default KbKeyboard;
