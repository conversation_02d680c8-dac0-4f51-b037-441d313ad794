/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import { View } from '@tarojs/components';
import request from '@base/utils/request';
import { AtIcon } from 'taro-ui';
import '../index.scss';

const elemList = [
  { title: '本月', index: 'this_month', num: '0' },
  { title: '上月', index: 'yester_month', num: '0' },
  { title: '昨日', index: 'yester_day', num: '0' },
  { title: '今日', index: 'this_day', num: '0' },
];
const Index = (props) => {
  const { customerInfo = {} } = props;
  const [orderInfo, setOrderInfo] = useState([]);

  useEffect(() => {
    if (customerInfo.id) {
      request({
        url: '/order/Order/getCustomerOrderInfo',
        toastLoading: false,
        data: { customer_id: customerInfo.id },
        onThen: ({ code, data }) => {
          if (code == 0) {
            elemList.map((item) => (item.num = data[item.index]));
            setOrderInfo([...elemList]);
          }
        },
      });
    }
  }, [customerInfo.id]);
  return (
    <View className='^page_view'>
      <View className='^view_height-lg  ^flex-row-between'>
        <View className='^view_title font-bold'>我的订单</View>
        <View
          className='^view_content ^flex-row'
          onClick={() =>
            Taro.navigator({
              url: 'customerManage/order?id=' + customerInfo.id,
            })
          }
        >
          <View className='^mlr-10'>订单管理</View>
          <AtIcon prefixClass='kb-icon-xyt' value='arrow' color='#999' size='12' />
        </View>
      </View>
      <View className='border-t ^flex-row'>
        {orderInfo.map((item, index) => (
          <View className='flex-c1 ^flex-column ptb-30' key={index}>
            <View className='^view_title font-bold'>{item.num}</View>
            <View className='^view_title color-dark mt-10'>{item.title}</View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default Index;
