/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect, useCallback } from "@tarojs/taro";
import { View } from "@tarojs/components";
import "../index.scss";
import { AtIcon } from "taro-ui";
const _tabList = [
  {
    title: "企业寄件码",
    index: "qrcode",
    icon: "margin_qrcode",
    path: "customerManage/customerInfo"
  },
  {
    title: "成员管理",
    index: "member",
    icon: "xiaoyoutongtubiao-441",
    path: "customerManage/member"
  },
  {
    title: "企业收发薄",
    index: "address",
    icon: "xiaoyoutongtubiao-45",
    path: "address"
  },
  {
    title: "短信通知",
    index: "message",
    icon: "xiaoyoutongtubiao-46",
    path: "customerManage/message"
  },
  {
    title: "自助打印",
    index: "print",
    icon: "xiaoyoutongtubiao-48",
    path: "customerManage/print"
  },
  {
    title: "数据对账",
    index: "bill",
    icon: "xiaoyoutongtubiao-47",
    path: "customerManage/orderExport"
  }
];

const Index = props => {
  const { isAdmin, customerInfo = {} } = props;
  const [tabList, setTabList] = useState([]);
  useEffect(() => {
    if (customerInfo.id) {
      if (isAdmin === false) {
        setTabList([..._tabList.filter(item => item.index === "qrcode")]);
      }
      if (isAdmin) {
        const list = [
          ..._tabList.filter(item => {
            switch (item.index) {
              case "print":
                return (
                  customerInfo.customer_type !== "agent_customer" &&
                  customerInfo.is_print == 1
                );
              case "bill":
                return customerInfo.allow_export == "1";
              default:
                return true;
            }
          })
        ];
        setTabList(list);
      }
    }
  }, [customerInfo.id]);

  const onNavigator = useCallback(
    async item => {
      let path = `${item.path}?id=${customerInfo.id}`;
      if (item.index === "print" && !customerInfo.customer_agent) {
        await openModal();
      }
      if(item.index === "address"){
        path = path +'&pageType=companyManage'
      }
      Taro.navigator({
        url: path
      });
    },
    [customerInfo]
  );

  const openModal = () => {
    return new Promise(resolve => {
      Taro.showModal({
        title: "自助打印",
        content:
          "自助打印:联系快递员为您设置单号源并开启打印权限后，可下载快宝云打印软件，将电脑上连接的热敏打印机，共享给企业成员进行自助式电子面单打印",
        confirmText: "去设置",
        success: res => {
          res.confirm && resolve();
        }
      });
    });
  };

  return (
    <View className="kbcm-options ^page_view">
      {tabList.map(item => (
        <View
          key={item.index}
          className="kbcm-options-tab"
          onClick={() => onNavigator(item)}
        >
          <AtIcon
            prefixClass="kb-icon-xyt"
            value={item.icon || ""}
            color="#39b54a"
            size="28"
          />
          <View className="kbcm-options-tab-title">{item.title || ""}</View>
        </View>
      ))}
    </View>
  );
};
export default Index;
