/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import { Button, View } from '@tarojs/components';
import request from '@base/utils/request';
import { setClipboardData } from '@/utils/qy';
import '../index.scss';

const Index = (props) => {
  const { customerInfo = {} } = props;
  const [pageInfo, setPageInfo] = useState({});
  useEffect(() => {
    request({
      url: '/api/weixin/mini/Customer/Customer/customerMiniUrlinfo',
      data: { customer_id: customerInfo.id },
      onThen: ({ code, data }) => {
        code == 0 && setPageInfo(data);
      },
    });
  }, [customerInfo.id]);

  return (
    <View className='^page_view kbcm-linkCard'>
      <View className='kbcm-linkCard__title'>
        <View>将专属寄件链接插入公众号/小程序</View>
      </View>
      <View className='kbcm-linkCard__card'>
        <View>小程序路径：/pages/index/index?source...</View>
        <View>
          <Button
            type='ghost'
            size='w_120_h_50_s_12'
            onClick={() => setClipboardData(pageInfo.page)}
          >
            复制
          </Button>
        </View>
      </View>
      <View className='kbcm-linkCard__card'>
        <View>小程序AppId：{pageInfo.appid}</View>
        <View>
          <Button
            type='ghost'
            size='w_120_h_50_s_12'
            onClick={() => setClipboardData(pageInfo.appid)}
          >
            复制
          </Button>
        </View>
      </View>
    </View>
  );
};

export default Index;
