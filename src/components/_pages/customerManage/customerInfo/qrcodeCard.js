/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useCallback } from '@tarojs/taro';
import { Button, View } from '@tarojs/components';
import request from '@base/utils/request';
import '../index.scss';

const Index = (props) => {
  const { customerInfo: { customer, qrcode_url, id } = {} } = props;
  const printCustomerQrcode = useCallback(
    () =>
      request({
        url: '/api/weixin/mini/Customer/Customer/printCustomerQrcode',
        data: { customer_id: id },
        onThen: ({ code }) => {
          code == 0 &&
            Taro.showToast({
              title: '打印成功',
              duration: 1000,
            });
        },
      }),
    [id],
  );

  const onSaveImage = useCallback(() => {
    var src =
      'https://upload.kuaidihelp.com/minipost/mini/qrcode/2019/08/06/minipost_100113513.png';
    wx.getImageInfo({
      src: src,
      success: function (res) {
        wx.getSetting({
          success(ress) {
            if (!ress.authSetting['scope.writePhotosAlbum']) {
              wx.authorize({
                scope: 'scope.writePhotosAlbum',
                success() {
                  wx.saveImageToPhotosAlbum({
                    filePath: res.path,
                    success() {
                      Taro.showToast({
                        title: '保存成功',
                        duration: 1000,
                      });
                    },
                  });
                },
              });
            } else {
              wx.saveImageToPhotosAlbum({
                filePath: res.path,
                success() {
                  Taro.showToast({
                    title: '保存成功',
                    duration: 1000,
                  });
                },
              });
            }
          },
        });
      },
    });
  }, []);

  const onShare = useCallback(
    () =>
      request({
        url: '/api/weixin/mini/Customer/Customer/getShareOrSendImage',
        data: {
          type: 'send_order',
          customer_id: id,
        },
        onThen: (res) => {
          if (res.code == 0) {
            Taro.previewImage({
              current: res.data,
              urls: [res.data],
            });
          }
        },
      }),
    [id],
  );
  return (
    <View className='^page_view kbcm-customerInfo'>
      <View className='kbcm-customerInfo__title'>{customer} · 寄件专用</View>
      <image src={qrcode_url} />
      <View className='^flex-row'>
        <Button
          type='primary'
          size='w_120_h_50_s_12'
          className='^mlr-10'
          onClick={printCustomerQrcode}
        >
          打印
        </Button>
        <Button type='primary' size='w_120_h_50_s_12' className='^mlr-10' onClick={onSaveImage}>
          保存
        </Button>
        <Button type='primary' size='w_120_h_50_s_12' className='^mlr-10' onClick={onShare}>
          分享
        </Button>
      </View>
    </View>
  );
};

export default Index;
