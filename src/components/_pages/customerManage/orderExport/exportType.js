/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useCallback, useState } from "@tarojs/taro";
import { Button } from "@tarojs/components";
import "../index.scss";
const _proItem = [
  "note",
  "package",
  "weight",
  "brand",
  "print_time",
  "source",
  "sender",
  "receiver"
];
const optionItem = [
  [
    {
      value: "note",
      name: "备注"
    },
    {
      value: "package",
      name: "物品"
    },
    {
      value: "weight",
      name: "重量"
    },
    {
      value: "brand",
      name: "快递品牌"
    }
  ],
  [
    {
      value: "print_time",
      name: "时间(创建/打印)"
    },
    {
      value: "source",
      name: "来源(订单/单号)"
    }
  ],
  [
    {
      value: "sender",
      name: "发件人(姓名/电话/省市区/地址)"
    }
  ],
  [
    {
      value: "receiver",
      name: "收件人(姓名/电话/省市区/地址)"
    }
  ]
];

const Index = props => {
  const { onChange } = props;
  const [proItem, setProItem] = useState(_proItem);

  useEffect(() => {
    onChange(proItem);
  }, [proItem]);

  const onchange = useCallback(
    value => {
      if (proItem.includes(value)) {
        if (proItem.length == 1) {
          Taro.showToast({
            title: "至少选择一项",
            duration: 1000,
            icon: "none"
          });
          return;
        }
        setProItem([...proItem.filter(item => item != value)]);
      } else {
        proItem.push(value);
        setProItem([...proItem]);
      }
    },
    [proItem]
  );
  return (
    <View className="kbcm_order_exportType">
      {optionItem.map((item, index) => (
        <View key={item} className="kbcm_order_exportType_buttonGrounp">
          {item.map(itemm => (
            <View key={itemm.value}>
              <Button
                type={proItem.includes(itemm.value) ? "primary" : "default"}
                onClick={onchange.bind(null, itemm.value)}
              >
                {itemm.name}
              </Button>
            </View>
          ))}
        </View>
      ))}
    </View>
  );
};

export default Index;
