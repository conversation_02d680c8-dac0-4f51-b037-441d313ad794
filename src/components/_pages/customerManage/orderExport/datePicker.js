/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useCallback, useState } from "@tarojs/taro";
import { Button, View, Picker } from "@tarojs/components";
import "../index.scss";
import dayjs from "dayjs";
import isEmpty from "lodash/isEmpty";
const dateButtonList = [
  { text: "今天", value: "today" },
  { text: "昨天", value: "yesterday" },
  { text: "上个月", value: "lastMonths" },
  { text: "历史订单", value: "history" }
];
const format = "YYYY-MM-DD";
const limitDate = {
  start: dayjs()
    .subtract(1, "year")
    .format(format),
  end: dayjs().format(format)
};
const Index = props => {
  const { onChange } = props;
  const [dateType, setDateType] = useState("today");
  const [dateValue, setDateValue] = useState({});
  const [pickerCurrentType, setPickerCurrentType] = useState("");
  useEffect(() => {
    !isEmpty(dateValue) && onChange(dateValue);
  }, [dateValue]);

  useEffect(() => {
    let _dateValue;
    if (dateType === "history" || !dateType) return;
    switch (dateType) {
      case "today":
        _dateValue = {
          start: dayjs().format(format),
          end: dayjs().format(format)
        };
        break;
      case "yesterday":
        _dateValue = {
          start: dayjs()
            .subtract(1, "day")
            .format(format),
          end: dayjs()
            .subtract(1, "day")
            .format(format)
        };
        break;
      case "lastMonths":
        _dateValue = {
          start: dayjs()
            .subtract(1, "month")
            .startOf("month")
            .format(format),
          end: dayjs()
            .subtract(1, "month")
            .endOf("month")
            .format(format)
        };
        break;
    }
    setDateValue({ ..._dateValue });
  }, [dateType]);
  const onPickerChange = useCallback(
    event => {
      const { value } = event.detail;
      dateValue[pickerCurrentType] = value;
      setDateValue({ ...dateValue });
      checkDateType(dateValue);
    },
    [pickerCurrentType, dateValue]
  );
  const checkDateType = useCallback(
    dateValue => {
      if (!isEmpty(dateValue)) {
        const today = dayjs().format(format);
        const yesterday = dayjs()
          .subtract(1, "day")
          .format(format);
        const lastMonths = {
          start: dayjs()
            .subtract(1, "month")
            .startOf("month")
            .format(format),
          end: dayjs()
            .subtract(1, "month")
            .endOf("month")
            .format(format)
        };
        if (dayjs(dateValue.start).isSame(dateValue.end, "day")) {
          dayjs(dateValue.start).isSame(today, "day") &&
            dateType != "today" &&
            setDateType("today");
          dayjs(dateValue.start).isSame(yesterday, "day") &&
            dateType != "yesterday" &&
            setDateType("yesterday");
          return;
        }
        if (dayjs(dateValue.start).isSame(lastMonths.start, "day")) {
          dayjs(dateValue.end).isSame(lastMonths.end, "day") &&
            dateType != "lastMonths" &&
            setDateType("lastMonths");
          return;
        }
        dateType != "history" && setDateType("");
      }
    },
    [dateType]
  );
  const onHistoryPickerChange = useCallback(event => {
    const { value } = event.detail;
    const curMonth = dayjs().isSame(value, "month");
    let _dateValue;
    if (curMonth) {
      _dateValue = {
        start: dayjs()
          .startOf("month")
          .format(format),
        end: dayjs().format(format)
      };
    } else {
      _dateValue = {
        start: dayjs(value)
          .startOf("month")
          .format(format),
        end: dayjs(value)
          .endOf("month")
          .format(format)
      };
    }
    setDateType("history");
    setDateValue({ ..._dateValue });
  }, []);

  return (
    <View className="kbcm_order_datepicker">
      <View className="kbcm_order_datepicker_title">时间段设置</View>
      <View className="kbcm_order_datepicker_buttonGrounp">
        {dateButtonList.map(item =>
          item.value === "history" ? (
            <View key={item.value}>
              <Picker
                mode="date"
                onChange={onHistoryPickerChange}
                fields="month"
                start={limitDate.start}
                end={limitDate.end}
              >
                <Button
                  key={item.value}
                  type={item.value == dateType ? "primary" : "default"}
                >
                  {item.text}
                </Button>
              </Picker>
            </View>
          ) : (
            <Button
              key={item.value}
              type={item.value == dateType ? "primary" : "default"}
              onClick={() => setDateType(item.value)}
            >
              {item.text}
            </Button>
          )
        )}
      </View>
      <View className="kbcm_order_datepicker_picker">
        <Picker
          mode="date"
          onChange={onPickerChange}
          value={dateValue.start || ""}
          start={limitDate.start}
          end={dateValue.end || limitDate.end}
          disabled={dateType === "history"}
        >
          <View
            className="kbcm_order_datepicker_picker_start"
            onClick={() => setPickerCurrentType("start")}
          >
            <Text>{dateValue.start}</Text>
            {dateType !== "history" && <Text className="arrow"></Text>}
          </View>
        </Picker>
        <View style={{ margin: "0 20rpx", color: "#666" }}>到</View>
        <Picker
          mode="date"
          onChange={onPickerChange}
          value={dateValue.end || ""}
          start={dateValue.start || limitDate.start}
          end={limitDate.end}
          disabled={dateType === "history"}
        >
          <View
            className="kbcm_order_datepicker_picker_start"
            onClick={() => setPickerCurrentType("end")}
          >
            <Text>{dateValue.end}</Text>
            {dateType !== "history" && <Text className="arrow"></Text>}
          </View>
        </Picker>
      </View>
    </View>
  );
};

export default Index;
