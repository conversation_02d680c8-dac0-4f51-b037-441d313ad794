/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kbcm-options {
  display: flex;
  justify-content: start;
  flex-wrap: wrap;
  padding-bottom: 40rpx !important;

  &-tab {
    width: 33%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 40rpx;

    &-title {
      font-size: 28rpx;
      color: #666;
      margin-top: 10rpx;
    }
  }
}

.kbcm-customerInfo {
  padding-top: 40rpx !important;
  padding-bottom: 40rpx !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &__title {
    font-size: 34rpx;
    text-align: center;
    font-weight: bold;
  }

  image {
    width: 280rpx;
    height: 280rpx;
    margin-top: 20rpx;
    margin-bottom: 40rpx;
  }
}

.kbcm-linkCard {
  &__title {
    display: flex;
    justify-content: start;
    align-items: center;
    height: 90rpx;
  }

  &__card {
    border-top: $width-base solid #e6e6e6;
    height: 90rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    color: #999;
  }
}

.kbcm_footer_customet {
  display: flex;
  margin-top: 10rpx;

  button {
    flex: 1;
    margin: 0 20rpx;

    &:nth-child(2) {
      margin-left: 0 !important;
    }
  }

  button[type='ghost'] {
    border: $width-base solid $color-brand;
    background: $color-white;
    color: $color-brand;
  }
}

.kbcm_order_datepicker {
  margin: 20rpx 20rpx 0;
  padding: 10rpx 20rpx;
  background: #fff;

  &_title {
    padding-top: 20rpx;
    margin-left: 10rpx;
    font-size: 30rpx;
    font-weight: bold;
    line-height: 50rpx;
  }

  &_buttonGrounp {
    height: 90rpx;
    display: flex;
    padding: 0 10rpx;
    justify-content: start;
    align-items: center;

    >view {
      flex: 1;
    }

    button {
      width: 140rpx;
      height: 50rpx;
      line-height: 48rpx;
      font-size: 26rpx;
      margin-right: 20rpx;
      padding: 0;
    }

    button[type='default'] {
      color: #999;
    }
  }

  &_picker {
    display: flex;
    justify-content: start;
    align-items: center;
    height: 90rpx;
    margin-top: -10rpx;

    &_start {
      width: 250rpx;
      height: 50rpx;
      border-radius: 8rpx;
      background: #f2f2f2;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15rpx;
      margin-left: 10rpx;
      color: #666;
      font-size: small;

      .arrow {
        border-left: 10rpx solid $color-brand;
        border-top: 10rpx solid #f2f2f2;
        border-bottom: 10rpx solid #f2f2f2;
        transform: translate(200%) rotateZ(45deg)
      }
    }
  }
}

.kbcm_order_exportType {
  margin: 20rpx;
  padding: 20rpx;
  background-color: $color-white;

  &_buttonGrounp {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
    height: 90rpx;

    button {
      font-size: 26px;
      height: 60rpx;
      line-height: 58px;
      padding-left: 30rpx;
      padding-right: 30rpx;
      margin-right: 30rpx;
    }

    button[type='default'] {
      color: #999;
    }
  }
}

.font-bold {
  font-weight: bold;
}

.border-t {
  border-top: $width-base solid #e6e6e6;
}

.mt-10 {
  margin-top: 10rpx;
}

.ptb-30 {
  padding: 30rpx 0;
}

.flex-c1 {
  flex: 1;
}

.color-dark {
  color: #ccc !important;
}

.kbcm_order {

  &__footer {
    &__pageview {
      background-color: #fff;
      height: 120rpx;
    }

    .pl-40 {
      padding-left: 40rpx;
    }

    .ml-10 {
      margin-left: 10rpx;
    }

    .mr-10 {
      margin-right: 10rpx;
    }

    .mlr-20 {
      margin: 0 20rpx;
    }

    .width-120 {
      width: 120rpx;
    }

    .flex-c1 {
      flex: 1;
    }

  }

  &__listcard {
    padding-top: 20rpx;

    .mr-10 {
      margin-right: 10rpx;
    }

    .mt-5 {
      margin-top: 5rpx;
    }

    &__card {
      height: 160rpx;
      margin: 0 20rpx 20rpx;
      background-color: #fff;
      border-radius: 8rpx;
      padding: 0 10rpx 0 20rpx;
      position: relative;

      &__mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        color: #fff;
        font-size: 30rpx;
        font-weight: bold;
      }

      &__brand {
        width: 60rpx;
        height: 60rpx;
      }

      &__address {
        justify-content: start;
        flex: 1;
        margin: 0 20rpx;
      }

      &__order_arrow {
        width: 80rpx;
        height: 2rpx;
        background-color: #ccc;
        position: relative;
        margin: 0 20rpx;

        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          border-bottom: 8rpx solid #ccc;
          border-left: 10rpx solid #fff;
          border-right: 10rpx solid #fff;
          transform: translate(0, -100%);
        }
      }

      &__status {
        text-align: right;
        font-size: 30rpx;
        font-weight: bold;
      }

      &__createTime {
        font-size: 24rpx;
        color: #999;
        text-align: right;
        margin-top: 5rpx;
      }

      &__order_fresh {
        background-color: $color-brand;
        font-size: 20rpx;
        text-align: center;
        color: #fff;
        line-height: 1;
        padding: 6rpx;
        border-radius: 8rpx;
        margin-right: 5rpx;
      }

      &__arrow {
        font-size: 36rpx;
        color: #999;
      }
    }
  }
}
