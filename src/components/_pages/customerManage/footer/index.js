/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from "@tarojs/taro";
import { Button, View } from "@tarojs/components";
import "../index.scss";

const Index = props => {
  const { buttonOption } = props;
  return (
    <View className="kbcm_footer_customet">
      {buttonOption.map(item => (
        <Button
          key={item.text}
          type={item.type || "primary"}
          openType={item.openType || ""}
          disabled={item.disabled || false}
          onClick={() => (item.cb ? item.cb() : Taro.navigateBack())}
        >
          {item.text}
        </Button>
      ))}
    </View>
  );
};

Index.defaultProps = {
  buttonOption: [{ text: "返回" }]
};

export default Index;
