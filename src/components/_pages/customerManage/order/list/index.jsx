/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import { createOrderStatusInfo } from '@/components/_pages/order/_utils/order.detail';
import '@/pages/customerManage/index.scss';
import KbCheckbox from '@base/components/checkbox';
import KbLongList from '@base/components/long-list';
import { Image, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import qs from 'qs';
import '../../index.scss';

const orderStatus = {
  已下单: 'color-main',
  已完成: 'color-orange-dark',
  已打印: 'color-blue-light',
  已取消: 'color-light',
  未完成: 'color-light',
};

@connect(
  ({ global }) => ({
    brands: global.brands,
  }),
  {
    get,
  },
)
class Index extends Component {
  static options = {
    addGlobalClass: true,
  };
  static defaultProps = {
    searchData: null,
    active: false,
    onGetted: () => {},
    onClickItem: () => {},
    selectted: [],
  };
  constructor(props) {
    super(props);
    this.state = {
      list: [],
    };
    const { dataParams = {} } = props;
    this.listData = {
      pageKey: 'page_num',
      api: {
        url: dataParams.customer_id
          ? '/order/Order/getAllCustomerOrder'
          : '/api/weixin/mini/minpost/order/history',
        data: dataParams,
        formatResponse: ({ data: { list } }) => {
          const hasList = isArray(list) && list.length > 0;
          if (hasList) {
            return {
              data: {
                list: list.map((item) => ({
                  ...item,
                  statusInfo: createOrderStatusInfo(item),
                })),
              },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list) => {
          const { onGetted } = this.props;
          this.setState({
            list,
          });
          onGetted(list, () => {
            this.changeApiParams();
          });
        },
      },
    };
  }

  componentDidMount() {
    this.props.get();
  }
  changeApiParams = (listParams) => {
    const { dataParams } = this.props;
    this.listData.api.data = listParams || dataParams;
  };

  // 跳转订单详情
  onClickItem = (item) => {
    const { onClickItem, pageType } = this.props;
    const { order_id, disabled, third_platform } = item;
    if (!order_id) return;
    if (pageType) {
      if (disabled) return;
      if (pageType == 'print' && third_platform === 'dak') return;
      onClickItem(item);
      return;
    }
    Taro.navigator({
      url: `order/detail?${qs.stringify({ order_id })}`,
    });
  };

  // 列表准备就绪
  handleReady = (ins) => {
    this.listIns = ins;
    this.props.bindRef(ins);
  };

  render() {
    const { active, searchData, selectList = [], pageType, dataParams } = this.props;
    const { list } = this.state;
    return (
      <KbLongList
        active={active && searchData}
        data={this.listData}
        enableMore
        onReady={this.handleReady}
      >
        <View className='kbcm  kbcm_order__listcard'>
          {list.map((item, index) => {
            return (
              <View
                key={item.order_id + index}
                className='kbcm_order__listcard__card flex-row-between'
                onClick={this.onClickItem.bind(this, item)}
              >
                {!!(pageType == 'print' && item.third_platform == 'dak') && (
                  <View
                    className='kbcm_order__listcard__card__mask flex-row'
                    style={{ background: 'rgba(0,0,0,0.4)' }}
                  >
                    <Text>此订单请联系驿站工作人员打印</Text>
                  </View>
                )}
                {pageType && (
                  <View className='item-checkbox mr-10'>
                    <KbCheckbox
                      disabled={!!item.disabled}
                      checked={selectList.includes(item.order_id)}
                      onChange={this.onClickItem.bind(this, item)}
                    />
                  </View>
                )}
                <Image
                  lazyLoad
                  className='kbcm_order__listcard__card__brand'
                  src={`https://cdn-img.kuaidihelp.com/brand_logo/icon_${item.brand}.png?v=20230314`}
                />
                <View className='kbcm_order__listcard__card__address flex-row'>
                  <View className='flex-column'>
                    <View className='view_title'>{item.send_city}</View>
                    <View className='view_content mt-5 '>{item.send_name.slice(0, 4)}</View>
                  </View>
                  <View className='kbcm_order__listcard__card__order_arrow' />
                  <View className='flex-column '>
                    <View className='view_title'>{item.receive_city}</View>
                    <View className='view_content mt-5 '>{item.receive_name.slice(0, 4)}</View>
                  </View>
                </View>
                <View className='flex-row'>
                  <View className='flex-column  mr-10'>
                    <View
                      className={`kbcm_order__listcard__card__status ${
                        orderStatus[item.order_status]
                      }`}
                    >
                      {item.order_status}
                    </View>
                    <View className='kbcm_order__listcard__card__createTime'>
                      {item.create_date}
                    </View>
                    {dataParams.customer_id && (
                      <View className='flex-row view_content'>
                        {item.fresh != '0' && (
                          <Text className='kbcm_order__listcard__card__order_fresh'>鲜</Text>
                        )}
                        <Text>下单人/</Text>
                        <Text>{item.nickName}</Text>
                      </View>
                    )}
                  </View>
                  {!pageType && (
                    <Text className='kb-icon kb-icon-arrow kbcm_order__listcard__card__arrow' />
                  )}
                </View>
              </View>
            );
          })}
        </View>
      </KbLongList>
    );
  }
}
export default Index;
