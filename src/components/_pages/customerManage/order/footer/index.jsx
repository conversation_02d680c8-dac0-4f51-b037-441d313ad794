/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useCallback, useEffect } from '@tarojs/taro';
import { Button, View, Text } from '@tarojs/components';
import KbCheckbox from '@base/components/checkbox';
import request from '@base/utils/request';
import { setClipboardData } from '@/utils/qy';
import { orderAction } from '@/components/_pages/order/_utils';
import '@/pages/customerManage/index.scss';
import '../../index.scss';

const Index = (props) => {
  const { pageType, selectList = [], list = [], onSetState, onLoadMore, dataParams } = props;
  const [allStatus, setAllStatus] = useState(false);
  const [buttonDisabled, setButtonDisabled] = useState(true);
  useEffect(() => {
    if (list.length) {
      setAllStatus(list.length == selectList.length);
    }
    setButtonDisabled(selectList.length == 0);
  }, [list, selectList]);
  const onAllChoose = useCallback(() => {
    if (pageType === 'print') {
      onSetState({
        selectList: allStatus
          ? []
          : [...list.filter((item) => item.third_platform !== 'dak').map((item) => item.order_id)],
      });
    } else {
      onSetState({
        selectList: allStatus ? [] : [...list.map((item) => item.order_id)],
      });
    }
  }, [allStatus, list, pageType]);

  const onExport = useCallback(() => {
    request({
      url: '/api/weixin/mini/minpost/export/orderIds',
      toastError: true,
      data: {
        order_ids: JSON.stringify(selectList),
      },
      onThen: ({ data }) => {
        const { file_url } = data || {};
        if (file_url) {
          Taro.kbModal({
            title: '复制数据下载地址',
            content:
              '生成数据为xlsx文件,请点击“复制数据下载”按钮,在手机浏览器中打开即可下载数据文件',
            confirmText: '复制链接',
            cancelText: '',
            onConfirm: () => {
              setClipboardData(file_url);
              onSetState({ pageType: '', selectList: [] });
            },
          });
        }
      },
    });
  }, [selectList]);

  const onPrint = useCallback(
    (action) => {
      let _list;
      if (action === 'pprint') {
        _list = list
          .filter((item) => selectList.includes(item.order_id) && !item.waybill)
          .map((item) => item.order_id);
        if (_list.length == 0) {
          Taro.kbToast({
            text: '所选订单已全部打印，无法预打印',
          });
          return;
        } else {
          Taro.kbToast({
            text: '已自动剔除不可预打印订单',
          });
          onSetState({ selectList: [..._list] });
        }
      } else {
        _list = selectList;
      }
      orderAction({
        action,
        data: {
          order_id: _list.join(','),
        },
      }).then(() => {
        console.info('打印完成');
      });
    },
    [selectList, list],
  );

  return list.length ? (
    <View className='kbcm  kbcm_order__footer'>
      {pageType ? (
        <View className='flex-row-between  kbcm_order__footer__pageview  pl-40'>
          <View className='flex-row'>
            <View className='flex-row' onClick={onAllChoose}>
              <KbCheckbox checked={allStatus} onChange={onAllChoose} />
              <View className='ml-10 view_content'>
                已选
                <Text className='color-main mlr-5'>{selectList.length}</Text>个
              </View>
            </View>
            <Button
              type='primary'
              size='w_120_h_50_s_12'
              className='ml-10 width-120'
              onClick={onLoadMore}
            >
              加载20条
            </Button>
          </View>
          <View className='pr-10 flex-row'>
            {pageType == 'print' && (
              <Button
                type='primary'
                size='w_120_h_50_s_12'
                className='mr-10 width-120'
                onClick={() => onPrint('pprint')}
                disabled={buttonDisabled}
              >
                预打印
              </Button>
            )}
            {pageType == 'print' && (
              <Button
                type='primary'
                size='w_120_h_50_s_12'
                className='mr-10 width-120'
                onClick={() => onPrint('print')}
                disabled={buttonDisabled}
              >
                打印
              </Button>
            )}
            {pageType == 'export' && (
              <Button
                type='primary'
                size='w_120_h_50_s_12'
                className='mr-10 width-120'
                onClick={onExport}
                disabled={buttonDisabled}
              >
                导出
              </Button>
            )}
            <Button
              type='ghost'
              size='w_120_h_50_s_12'
              className='width-120'
              onClick={() => onSetState({ pageType: '', selectList: [] })}
            >
              取消
            </Button>
          </View>
        </View>
      ) : dataParams.customer_id ? (
        <View className='flex-row kbcm_order__footer__pageview'>
          <Button type='ghost' className='flex-c1 mlr-20' onClick={() => Taro.navigator()}>
            返回
          </Button>
          <Button
            type='primary'
            className='flex-c1 mlr-20'
            onClick={() =>
              Taro.navigator({
                url: 'customerManage/queryCode?id=' + dataParams.customer_id,
              })
            }
          >
            自助查件码
          </Button>
        </View>
      ) : (
        <View className='flex-row kbcm_order__footer__pageview'>
          <Button
            type='primary'
            className='flex-c1 mlr-20'
            onClick={() => onSetState({ pageType: 'export' })}
          >
            导出数据
          </Button>
          <Button
            type='primary'
            className='flex-c1 mlr-20'
            onClick={() => onSetState({ pageType: 'print' })}
          >
            打印面单
          </Button>
        </View>
      )}
    </View>
  ) : (
    <View />
  );
};
export default Index;
