/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { getStorageSync, setStorageSync, jsonParse, reportAnalytics } from '@base/utils/utils';
import isString from 'lodash/isString';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import dayjs from 'dayjs';
import isArray from 'lodash/isArray';

//解析时间结构
export function getOpenPrizeTime(data, isPopup, lottery_type = '0') {
  if (!data) return '';
  var data1 = `${data}`.split(' ')[0],
    data2 = `${data}`.split(' ')[1],
    MM = data1.split('-')[1],
    DD = data1.split('-')[2],
    hh = data2.split(':')[0],
    mm = data2.split(':')[1];
  if (isPopup) {
    return MM + '月' + DD + '日' + ' ' + hh + ':' + mm;
  } else {
    const suffix = lottery_type == '0' ? '自动开奖' : '活动截止';
    return MM + '月' + DD + '日' + ' ' + hh + ':' + mm + suffix;
  }
}

//参与抽奖状态
export function getHasActivity(data) {
  const hasKb_id = data && !!data.kb_id;
  return hasKb_id;
}

//时间格式
export function myFormat(time) {
  if (!time) return;
  return new Date(Date.parse(time.replace(/-/g, '/'))).getTime();
}

//解析一等奖金额
export function getRankMoney(data) {
  if (!data) return;
  const parseData = isString(data) ? JSON.parse(data) : [data];
  const [money] = parseData.map((item) => item.money);
  return money;
}

//动态数据列表
export function getAssistList(data = []) {
  return [
    ...data,
    { avatar_url: '//osscdn-kbad.kuaidihelp.com/admin/ad/2022/04/24/6264d6db76b66/icon_my1.png' },
    { avatar_url: '//osscdn-kbad.kuaidihelp.com/admin/ad/2022/04/24/6264d6db76b66/icon_my1.png' },
    { avatar_url: '//osscdn-kbad.kuaidihelp.com/admin/ad/2022/04/24/6264d6db76b66/icon_my1.png' },
  ].splice(0, 3);
}

//设置缓存
export function setTimeAdShow(key, hour = 1) {
  let creatTime = getStorageSync(key).data;
  let now = +new Date();
  const time = hour * 60 * 60 * 1000;
  if (creatTime) {
    let time_difference = now - creatTime;
    if (time_difference >= time) {
      setStorageSync(key, now);
      return true;
    } else {
      return false;
    }
  } else {
    setStorageSync(key, now);
    return true;
  }
}

export const lotteryLevel = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];

export function getActivityKey(data, key) {
  if (data && !isArray(data)) {
    if (data[key]) {
      return jsonParse(data[key]);
    } else {
      return jsonParse(data);
    }
  } else {
    return false;
  }
}

export function goPage() {
  reportAnalytics({
    key: 'welfare.lotterys',
    title: '未中奖弹窗按钮',
  });
  Taro.switchTab({
    url: '/pages/welfare/continuity/index',
  });
}

export function getStatus(status, date) {
  return dayjs(date) < dayjs() && status === '1';
}

export function formatLotteryDetail(defaultData = {}, data = {}) {
  if (!data.id) return {};
  const { id, lid, lLst, params: rankPrize, me, lottery_time, lottery_type, is_lottery } = data;
  return {
    ...defaultData,
    ...data,
    activityId: lid || id,
    lLst: getAssistList(lLst),
    money: getRankMoney(rankPrize), // 获取活动奖励
    whetherHasActivity: getActivityKey(me, 'kb_id'),
    openPrizeTime: getOpenPrizeTime(lottery_time, false, lottery_type),
    isLottery: getStatus(is_lottery, lottery_time),
  };
}

export function formatCountdownSuffix(lottery_type = '0') {
  return lottery_type == '0' ? '后开奖' : '后截止';
}

export function getRankLevel(data) {
  const { ranking = '' } = getActivityKey(data, 'reason');
  return ranking ? lotteryLevel[Number(ranking) - 1] + '等奖' : '';
}

export function getLotteryStatus(data) {
  const { lottery_time, is_lottery, me, is_winning, lottery_type = '0', lotteryHistory } = data;
  let remaining = dayjs(lottery_time) - dayjs();
  const whetherHasActivity = getActivityKey(me, 'kb_id');
  let rankLevel = '';
  //  0 未参与 1 开奖时间已过，未开奖 2 已开奖,未参与 3 已中奖 4 未中奖 5 待开奖 6 即抽即开
  let status = 0;
  //是否到开奖时间
  if (remaining < 0) {
    // 是否开奖
    if (lottery_type === '1') {
      if (whetherHasActivity) {
        if (isArray(lotteryHistory) && lotteryHistory.length > 0) {
          status = 7;
        } else {
          status = 4;
        }
      } else {
        status = 2;
      }
    } else {
      if (is_lottery === '1') {
        // 是否参与
        if (whetherHasActivity) {
          // 是否中奖
          if (is_winning) {
            status = 3;
          } else {
            status = 4;
          }
        } else {
          status = 2;
        }
      } else {
        status = 1;
      }
    }
  } else {
    // 抽奖类型
    if (lottery_type === '0') {
      if (whetherHasActivity) {
        status = 5;
      }
    } else {
      status = 6;
    }
  }
  if (is_lottery === '1' && me) {
    rankLevel = getRankLevel(me);
  }
  return [status, rankLevel];
}

export const lotteryJump = (data) => {
  const { prize_type = '' } = data;
  if (prize_type == 'url' || prize_type == 'pic') {
    const { appid: appId = '', page = '', half_open } = data;
    adNavigator({
      adUrl: page + `${appId ? ',' + appId : ''}` + `${half_open ? ',half' : ''}`,
    });
    reportAnalytics({
      key: 'welfare.lotterys',
      title: `跳转小程序奖品-点击${half_open ? '-半屏' : ''}`,
    });
  }
};
