/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import KbCanvas from '@base/components/canvas';
import { mergeBySpace, noop } from '@base/utils/utils';

const Index = (props) => {
  const { type, data, onDrawComplete } = props;
  const [template, updateTemplate] = useState(null);
  const templateCanvasConfig = {
    normal: {
      width: 345,
      height: 510,
    },
    share: {
      width: 210,
      height: process.env.PLATFORM_ENV === 'weapp' ? 168 : 231,
    },
  };

  // 生成图片
  const createCard = (data) => {
    // 下载图片
    const { company, qrcode, express_code, mobile, tel, name, province, city, country, address } =
      data;
    const templatesMap = {
      normal: [
        {
          action: 'image',
          data: [
            {
              value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/ecode-hb.png',
            },
            {
              value: qrcode,
              coor: [97, 190, 150, 150],
            },
          ],
        },
        {
          action: 'text',
          data: [
            {
              value: `${company || '我的'}的快递码`,
              coor: [172, 50],
              size: 24,
              color: 'white',
              textAlign: 'center',
              baseLine: 'center',
            },
            {
              value: `快递码`,
              coor: [172, 132],
              size: 20,
              color: '#333',
              textAlign: 'center',
              baseLine: 'center',
            },
            {
              value: express_code,
              coor: [172, 165],
              size: 24,
              color: '#009fff',
              textAlign: 'center',
              baseLine: 'center',
            },
          ],
        },
      ],
      share: [
        {
          action: 'image',
          data: [
            {
              value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/ecode-fx.png',
              coor: [0, 0, 210, 168],
            },
            {
              value: qrcode,
              coor: [12, 28, 44, 44],
            },
          ],
        },
        {
          action: 'text',
          data: [
            {
              value: `地址信息`,
              size: 14,
              color: '#999',
              textAlign: 'left',
              baseLine: 'center',
              coor: [65, 28],
            },
            {
              value: `${name} ${mergeBySpace(mobile, tel)}`,
              size: 14,
              color: '#333',
              textAlign: 'left',
              baseLine: 'center',
              coor: [65, 52, 130],
            },
            {
              value: `${province}${city}${country}${address}`,
              size: 14,
              color: '#333',
              textAlign: 'left',
              baseLine: 'center',
              coor: [65, 70, 125],
              maxLine: 2,
            },
          ],
        },
      ],
    };
    updateTemplate(templatesMap[type]);
  };

  useEffect(() => {
    if (data) {
      createCard(data);
    }
  }, [data]);

  return (
    <KbCanvas
      template={template}
      {...templateCanvasConfig[type]}
      toastLoading={type === 'normal'}
      saveImage={type === 'normal'}
      toImage={type === 'share'}
      onDrawComplete={onDrawComplete}
    />
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  type: 'normal',
  data: null,
  onDrawComplete: noop,
};

export default Index;
