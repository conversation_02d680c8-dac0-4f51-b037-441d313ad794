/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import { extractData, scanParse } from '@base/utils/utils';

// 获取快递码信息
export function getEcodeInfo(data, api) {
  return new Promise((resolve, reject) => {
    const { sign, content } = data || {};
    if (!sign && !content) {
      reject(new Error('请扫描正确的快递码'));
      return;
    }
    request({
      ...api,
      url: '/g_wkd/v2/Account/getExpressCodeAddressInfo',
      data,
      nonceKey: 'id,token_code,sign',
      onThen: ({ data, msg }) => {
        const { express_code } = data || {};
        if (!express_code) {
          reject(new Error(msg));
          return;
        }
        resolve(
          extractData(data, [
            'name',
            'mobile',
            'province',
            'city',
            ['district', 'country'],
            'address',
            'express_code',
            'notice',
          ]),
        );
      },
    });
  });
}

export function addEcode(data, api) {
  return new Promise((resolve, reject) => {
    const { express_code } = data;
    request({
      ...api,
      url: '/g_wkd/v2/Account/addExpressCodeShare',
      data: { express_code },
      onThen: ({ code, msg }) => {
        code == 0 ? resolve(data) : reject(new Error(msg));
      },
    });
  });
}

export const getAndAddEcode = (result) => {
  const { query } = scanParse(result);
  return getEcodeInfo(query).then(addEcode);
};

export const showNotice = (notice) => {
  // 快递码包含寄件提醒
  if (notice) {
    Taro.kbModal({
      content: ['如果给我寄快递，请注意：', { text: notice, className: 'kb-color__black' }],
      confirmText: '知道了',
    });
  }
};
