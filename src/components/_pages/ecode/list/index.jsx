/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '@base/components/long-list';
import KbEcodeCard from '@/components/_pages/ecode/card';
import { formatAddress, orderAction } from '@/components/_pages/order/_utils';
import request from '@base/utils/request';
import { extractData, importFieldHide, mergeBySpace, noop } from '@base/utils/utils';
import { Button, Image, Text, View } from '@tarojs/components';
import Taro, { Fragment, useMemo, useRef, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtButton, AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { type, org, onReady } = props;
  const [list, updateList] = useState([]);
  const [current, updateCurrent] = useState(null);
  const actionRef = useRef({});

  const listData = {
    api: {
      url: `/g_wkd/v2/Account/getExpressCode${type === 'address' ? 'Share' : 'AddressList'}`,
      formatResponse: ({ data: list }) => {
        if (isArray(list) && list.length > 0) {
          return {
            data: {
              list,
            },
          };
        }
        return {
          data: void 0,
        };
      },
      onThen: (list) => {
        if (list.length === 0 && type === 'ecode') {
          handleHelp('blank');
          return;
        }
        updateList(list);
      },
    },
  };

  // 跳转帮助页
  const handleHelp = (target) => {
    if (process.env.PLATFORM_ENV === 'weapp') {
      Taro.navigateToDocument('ecode', target);
    } else {
      // 支付宝版直接进入编辑页
      Taro.navigator({
        url: 'user/ecode/edit',
      });
    }
  };

  // 返回寄快递页面
  const backToOrderEdit = (item) => {
    let data = extractData(item, [
      'id',
      'name',
      ['mobile', ({ mobile, tel }) => mergeBySpace(mobile, tel)],
      'province',
      'city',
      ['district', 'country'],
      'address',
    ]);
    if (type === 'address' && org === 'common_address') {
      // 常用地址列表点进来
      const { data: address } = formatAddress(data, 'receive', {
        reverse: true,
      });
      orderAction({
        action: 'edit',
        data: {
          address,
        },
      });
      return;
    }
    Taro.navigator({
      post: {
        type: 'addressSelect',
        data: {
          data,
          list: [data],
          source: 'list',
        },
      },
    });
  };

  const handleReady = (ins) => {
    actionRef.current.list = ins;
    onReady(ins);
  };

  const handleAction = (action, item) => {
    const { id, express_code, sign, token_code, url, qrcode, ...rest } = item;

    switch (action) {
      case 'detail':
        // 详情
        if (type === 'ecode') {
          Taro.navigator({
            url: 'user/ecode/detail',
            options: { id },
          });
        } else {
          backToOrderEdit(item);
        }
        break;
      case 'edit':
        // 编辑
        Taro.navigator({
          url: 'user/ecode/edit',
          options: {
            ...rest,
            id,
            express_code,
          },
        });
        break;
      case 'delete':
        // 删除
        const apisMap = ((data) => data[type])({
          address: {
            url: 'delExpressCodeShare',
            data: { express_code },
          },
          ecode: {
            url: 'deleteExpressCodeAddress',
            data: { id },
          },
        });
        Taro.kbModal({
          content: '是否确认删除该快递码？',
          onConfirm: () => {
            request({
              url: `/g_wkd/v2/Account/${apisMap.url}`,
              data: apisMap.data,
              toastError: true,
              onThen: ({ code }) => {
                if (code == 0) {
                  actionRef.current.list.loader();
                }
              },
            });
          },
        });
        break;
      case 'download':
        // 下载图片
        updateCurrent({ ...item });
        break;
      case 'send':
        // 寄快递
        backToOrderEdit(item);
        break;

      default:
        break;
    }
  };

  // 操作按钮
  const bars = useMemo(() => {
    const list = [
      {
        key: 'edit',
        icon: 'edit-square',
        label: '编辑',
      },
      {
        key: 'delete',
        icon: 'delete',
        label: '删除',
      },
      {
        key: 'download',
        icon: 'download',
        label: '保存快递码图片',
      },
      {
        key: 'share',
        icon: 'share',
        label: '分享',
      },
      {
        key: 'send',
        icon: 'send-2',
        label: '寄快递',
      },
    ];
    let enableKeys = [];
    if (type === 'address') {
      // 收件人列表页
      enableKeys = ['delete', 'send'];
    } else {
      enableKeys = ['edit', 'delete', 'download', 'share'];
    }
    return list.filter(({ key }) => enableKeys.includes(key));
  }, [type]);
  return (
    <Fragment>
      <KbLongList data={listData} onReady={handleReady}>
        <View className='kb-list'>
          {list.map((item) => (
            <View className='kb-list__item--wrapper' key={item.id}>
              <View
                className='kb-list__item'
                hoverClass='kb-hover'
                onClick={handleAction.bind(null, 'detail', item)}
              >
                <View className='item-icon'>
                  <Image className='item-icon__image' src={item.qrcode} mode='widthFix' lazyLoad />
                </View>
                <View className='item-content'>
                  <View className='item-content__title kb-color__brand'>
                    <View className='at-row at-row__align--center'>
                      <View className='at-col'>
                        {item.company} {item.express_code}
                      </View>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='arrow'
                        className='kb-icon-size__sm kb-color__grey'
                      />
                    </View>
                  </View>
                  <View className='item-content__desc'>
                    {item.name}/{importFieldHide(item.mobile, 3, 7)}
                    {item.tel}/{item.province} {item.city} {item.country}
                    {item.address}
                  </View>
                </View>
              </View>
              <View className='kb-list__bar'>
                {bars.map((barItem) =>
                  barItem.key === 'share' ? (
                    <Button
                      openType='share'
                      className='kb-list__bar-item'
                      hoverClass='kb-hover'
                      dataInfo={item}
                      dataPage='order.ecode'
                      key={item.key}
                    >
                      <AtIcon
                        prefixClass='kb-icon'
                        value={barItem.icon}
                        className='kb-color__grey kb-icon-size__sm'
                      />
                      <Text className='kb-icon__text--ml'>{barItem.label}</Text>
                    </Button>
                  ) : (
                    <View
                      className='kb-list__bar-item'
                      hoverClass='kb-hover'
                      onClick={handleAction.bind(null, barItem.key, item)}
                      key={item.key}
                    >
                      <AtIcon
                        prefixClass='kb-icon'
                        value={barItem.icon}
                        className='kb-color__grey kb-icon-size__sm'
                      />
                      <Text className='kb-icon__text--ml'>{barItem.label}</Text>
                    </View>
                  ),
                )}
              </View>
            </View>
          ))}
        </View>
        {process.env.PLATFORM_ENV === 'weapp'
          ? type === 'ecode' && (
              <View className='kb-list__help'>
                <AtButton
                  className='kb-button__link'
                  onClick={handleHelp.bind(null, 'blank')}
                  size='small'
                >
                  使用帮助
                </AtButton>
              </View>
            )
          : null}
        <KbEcodeCard data={current} />
      </KbLongList>
    </Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  type: 'ecode', //ecode：我的快递码 address：我收到的快递码
  onReady: noop,
};

export default Index;
