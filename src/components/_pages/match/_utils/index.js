/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import Taro from '@tarojs/taro';

/**
 * @description  ownership 1:本人，2:亲友，3:疑似，4:他人
 */
export function createTips(ownership, phone) {
  const ownershipMap = {
    1: `本人包裹（手机尾号${phone}）`,
    2: `亲友包裹（手机尾号${phone}）`,
    3: `手机尾号${phone}的包裹，请注意核对`,
    4: `其他人包裹，请注意核对`,
  };
  return ownershipMap[ownership] || '';
}

/**
 *
 * @description 查看他人取件码输入手机号
 * @param {*} data
 * @param {Function} callback
 * @returns
 */
export function getPickupCode({ dakId, waybill_no }) {
  return new Promise((resolve) => {
    Taro.kbModal({
      template: [
        {
          className: 'kb-color__grey kb-size__base kb-spacing-md-b',
          value: '请输入四位手机尾号，进行查询',
        },
        {
          tag: 'at-input',
          placeholder: '请输入手机后四位尾号',
          value: '',
          circle: true,
          border: false,
          inputType: 'number',
          maxLength: 4,
          name: 'mobile',
        },
      ],
      onConfirm: (e, close) => {
        const {
          data: { mobile },
        } = e;
        if (mobile) {
          request({
            url: '/api/weixin/mini/DakMini/Record/confirmMobile',
            toastError: true,
            data: {
              cm_id: dakId,
              waybill_no,
              express_phone: mobile,
            },
            onThen: (res) => {
              const { code, data } = res;
              if (code == 0 && data) {
                close();
                resolve({ code: data });
              }
            },
          });
        }
        return true;
      },
    });
  });
}

export function createQueryListItemKey(dakId, iitem) {
  return `${dakId}-${iitem.brand}-${iitem.waybill_no}`;
}
