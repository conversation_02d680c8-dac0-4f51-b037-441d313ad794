/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { noop } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import classNames from 'classnames';
import { useGetFollowStatus } from './_utils';
import './index.alipay.scss';

const Index = (props) => {
  const { onFollow, isOpened, fixed } = props;
  const [show, upShow] = useState(false);
  const { followAccount: { is_focus, name: officialName } = {} } = useSelector(
    (state) => state.global,
  );
  const handleFollowed = () => {
    upShow(false);
    onFollow(true);
  };

  useGetFollowStatus();

  useEffect(() => {
    upShow(!is_focus);
  }, [is_focus]);

  const publicId =
    process.env.MODE_ENV === 'yz'
      ? '****************'
      : process.env.MODE_ENV === 'wkd'
      ? '****************'
      : '';

  const rootCls = classNames('kb-lifeStyle', {
    [`kb-lifeStyle__fixed--${fixed}`]: !!fixed,
  });

  return publicId && show && isOpened ? (
    !fixed ? (
      <View className=' kb-margin-lg'>
        <lifestyle publicId={publicId} onFollow={handleFollowed} />
      </View>
    ) : (
      <View className={rootCls}>
        <View className='kb-lifeStyle__lifestyle'>
          <lifestyle
            style='width:100%;height:100%;'
            publicId={publicId}
            onFollow={handleFollowed}
          />
        </View>
        <View className='kb-lifeStyle__show-view'>
          <View className='kb-lifeStyle__show-view__close' onClick={handleFollowed}>
            <Text className='kb-icon kb-icon-wrong' />
          </View>
          <View className='kb-lifeStyle__show-view__content kb-size__sm'>
            关注{officialName}生活号，有快递早知道~
          </View>
          <View className='kb-lifeStyle__show-view__btn kb-size__sm'>立即关注</View>
        </View>
      </View>
    )
  ) : (
    <Fragment />
  );
};

Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  onFollow: noop,
  isOpened: process.env.MODE_ENV === 'wkd' ? false : true,
  fixed: '',
};
export default Index;
