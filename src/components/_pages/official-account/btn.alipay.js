/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { noop } from '@base/utils/utils';
import Taro, { useState, Fragment } from '@tarojs/taro';

const Index = (props) => {
  const { onCheckFollow, onClose, sceneId, ...reset } = props;
  const [followed, setFollowed] = useState(true);
  const handleCheckFollow = (e) => {
    const { followed } = e.detail;
    setFollowed(followed);
    onCheckFollow(e);
  };
  const handleClose = (e) => {
    onClose(e);
  };
  return (
    <Fragment>
      <life-follow
        {...reset}
        sceneId={sceneId}
        checkFollow={followed}
        onCheckFollow={handleCheckFollow}
        onClose={handleClose}
      />
    </Fragment>
  );
};
Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  onCheckFollow: noop,
  onClose: noop,
  sceneId: '689a6fe9e2f1419ab5ff8bf0d12605a2',
};
export default Index;
