/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-lifeStyle {
  z-index: 3;
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-around;
  width: 100%;
  height: 80px;
  background: rgba(0, 0, 0, 0.7);
  // margin-bottom: 0;
  // margin-bottom: constant(safe-area-inset-bottom);
  // margin-bottom: env(safe-area-inset-bottom);
  &__fixed {
    &--bottom {
      position: fixed;
      right: 0;
      bottom: 0;
      left: 0;
    }
  }
  .kb-icon-wrong {
    font-size: $icon-font-size-xs / 1.5;
  }
  &__lifestyle {
    position: absolute;
    top: 0;
    left: 90px;
    width: 83%;
    height: 80px;
    opacity: 0;
  }
  &__show-view {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 42px !important;
    height: 100%;
    color: $color-white;
    font-size: 26px;
    line-height: 42px;
    &__close {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 30px;
      height: 30px;
      margin: 0 0 0 30px;
      color: $color-white;
      border: 2px solid $color-white;
      border-radius: $border-radius-circle;
      &::after {
        position: absolute;
        top: 5px;
        left: 50%;
        display: block;
        width: 2px;
        height: 70%;
        margin: -$width-base;
        background-color: $color-grey-3;
        transform: rotate(45deg);
        content: '';
      }
      &::before {
        position: absolute;
        top: 5px;
        left: 50%;
        display: block;
        width: 2px;
        height: 70%;
        margin: -1px;
        background-color: $color-grey-3;
        transform: rotate(-45deg);
        content: '';
      }
    }
    &__content {
      padding: 0 60px 0 10px;
    }
    &__btn {
      right: 13px;
      height: 42px;
      margin-right: 13px;
      padding: 0 20px;
      line-height: 42px;
      background: $color-red;
      border-radius: 21px;
      border-radius: 21px;
    }
  }
}
