/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isFresh } from '@/components/_pages/order/_utils';
import { getApiUrlAndDataCourierDetail } from '@/components/_pages/order/_utils/courier.detail';
import { getPickUpApi } from '@/components/_pages/query/_utils/query.appointment';
import apis from '@/utils/apis';
import { relationStorageKey } from '@/utils/config';
import {
  checkQueryIsMatch,
  checkUrlPathIsMatch,
  qrCodePathMap,
  qrCodeQueryMap,
} from '@/utils/scan';
import logger from '@base/utils/logger';
import request from '@base/utils/request';
import {
  debounce,
  extractData,
  getPage,
  getStorage,
  scanParse,
  setStorage,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isBoolean from 'lodash/isBoolean';
import isObject from 'lodash/isObject';
import numeral from 'numeral';
import { RequestOnceManager, requestGetOnce } from '~base/utils/request/once';

const createRelationApi = '/api/weixin/mini/minpost/relation/addRelation';
const createRelationNonceKey = process.env.MODE_ENV === 'yz' ? 'dak_id,customer_id,courier_id' : '';

// 下单关系绑定成功：需要清除获取默认下单关系的缓存，以便能获取最新的
function createRelationSuccess() {
  RequestOnceManager.reset('/g_mp/api/weixin/mini/minpost/relation/getDefaultRelation');
}

// 通过二维码获取并绑定下单关系：兼容带有dakId的二维码
function createStoreInfoFromQrCodeApi(data) {
  const { dakId, dak_id = dakId, ...restData } = data || {};
  const hasDakId = !!dak_id;
  return hasDakId
    ? {
        url: createRelationApi,
        nonceKey: createRelationNonceKey,
        data: {
          ...restData,
          dak_id,
        },
      }
    : {
        url:
          process.env.MODE_ENV == 'wkd'
            ? '/g_wkd/v2/scan/QrCode/scan'
            : '/api/weixin/mini/handle/QrCode/scan',
        nonceKey: 'md5:text',
        data: { ...(!isObject(data) ? { text: data } : data) },
      };
}

export const isCurrentPage = (page) => {
  const currentPage = getPage(-1);
  const { $router: { path } = {} } = currentPage;
  return path.includes(page);
};

export const getRelationInfoByQrCode = (qrcodeContent, mastMessage) => {
  return new Promise((resolve, reject) => {
    if (process.env.MODE_ENV === 'wkd') {
      // @微快递
      reject();
    } else {
      request({
        url: '/api/weixin/mini/handle/QrCode/innInfoByCnt',
        mastHasMobile: false,
        data: {
          qrcodeContent,
        },
        onThen: (res) => {
          const { code, data, msg } = res;
          if (code === 0) {
            console.info('扫码内容=====>', data);
            const { shop_id = '' } = data;
            setStorage({
              key: 'HYLSHOP',
              data: shop_id,
            });

            resolve(data);
          } else {
            if (mastMessage) {
              setTimeout(() => {
                console.log('msg', msg);
                Taro.kbToast({ text: msg });
              }, 500);
            }
            reject();
          }
        },
      });
    }
  });
};

export const getKdgInfo = ({ cabinetToken }, mastMessage, then) => {
  return new Promise((resolve, reject) => {
    if (process.env.MODE_ENV === 'wkd') {
      // @微快递
      reject();
    } else {
      requestGetOnce({
        url: '/api/weixin/mini/MailCabinet/createOrderRelation',
        mastHasMobile: false,
        data: {
          cabinetToken,
        },
        onThen: (res) => {
          const { code, data, msg } = res;
          if (then) {
            then(res);
          }
          if (code === 0) {
            resolve(data);
          } else {
            if (mastMessage) {
              setTimeout(() => {
                Taro.kbToast({ text: msg });
              }, 500);
            }
            reject();
          }
        },
      });
    }
  });
};

export const checkCabinetOrder = (relation) => {
  return new Promise((resolve, reject) => {
    const { relation_id } = relation || {};
    if (relation_id) {
      request({
        url: '/api/weixin/mini/minpost/MiniDak/getCabinetReserveOrder',
        data: {
          relation_id,
        },
        onThen: ({ code, data }) => {
          if (code == 0 && !!data) {
            Taro.kbModal({
              closable: false,
              title: '温馨提示',
              content: '您存在尚未存柜的订单，跳转订单列表开柜？',
              confirmText: '跳转订单列表',
              cancelText: '取消',
              onConfirm: () => {
                Taro.navigator({
                  url: 'order',
                  target: 'tab',
                  // options: {
                  //   order_id: data,
                  //   relation: JSON.stringify(relation),
                  // },
                });
                reject();
              },
              onCancel: () => {
                resolve();
              },
            });
          } else {
            resolve();
          }
        },
      });
    } else {
      resolve();
    }
  });
};

// 扫瞄普通二维码时只返回关系id
export function isUniversalCode(data) {
  if (typeof data == 'number' || typeof data == 'string') {
    return data !== '' && data >= 0;
  }
  return false;
}
const navToEdit = (qrCode) => {
  console.log('qrCode', qrCode);
  const url = 'order/edit';
  qrCode && Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo({ qrCodeContent: qrCode });
  if (isCurrentPage(url)) return;
  Taro.navigator({
    url: 'order/edit',
    target: 'tab',
  });
};

// 根据type类型判断业务逻辑
// 绑定下单关系与扫码寄-调用时普通二维码会绑定下单关系，并返回关系id，扫码寄内容返回下单关系对象
// data.text : 二维码内容
// query - false 非查件， {type} - 查件类型
export function getStoreInfoFromQrCode(data, query = false) {
  let qrCode = data;
  const requestCfg = createStoreInfoFromQrCodeApi(data);

  return new Promise((resolve, reject) => {
    requestGetOnce({
      ...requestCfg,
      toastLoading: false,
      mastHasMobile: false,
      toastError: false,
      onThen: ({ code, data: res, msg }, req) => {
        const blockErrToast =
          process.env.MODE_ENV != 'wkd'
            ? req.text
              ? decodeURIComponent(req.text).includes('/kdg/')
              : true
            : false;
        if (code != 0 && msg && !blockErrToast) {
          setTimeout(() => {
            Taro.kbToast({
              text: msg,
            });
          }, 800);
        }
        // 普通扫码绑定下单关系对象
        if (isUniversalCode(res)) {
          createRelationSuccess();
          const { data: scanData } = requestCfg; // 扫码数据直接获取到了dakId，通过dakId获取数据
          const { dak_id: dakId, ...restScanData } = scanData;
          const storeInfoParams = dakId ? { ...restScanData, dakId } : 'last';
          getStoreInfo(storeInfoParams).then(resolve).catch(reject);
          return;
        }
        const {
          relation_info: {
            inn_id,
            dak_id: dakId = inn_id,
            relation_id: relationId,
            type,
            is_collection,
            specified_goods_category: goods_name,
            brand: dynamicBrand,
          } = {},
          service,
          waybill,
          brand,
          order_id,
        } = res || {};

        if (service !== 'ship_code' || code > 0) {
          reject(new Error('无效服务'));
          return;
        }
        const navToDetail = () => {
          const options = { waybill, brand, order_id, order_no: order_id };
          let url = 'query/detail';
          if (isCurrentPage(url)) {
            resolve(options);
            return;
          }
          Taro.navigator({
            url,
            options,
          });
        };

        const queryModal = () => {
          setTimeout(() => {
            Taro.kbModal({
              content: [
                {
                  text: '该码已下过单，不可重复使用，请不要把此码贴在包裹上 !',
                  className: 'kb-color__red',
                },
                '如需查询此码物流信息，请点击【查件】',
              ],
              confirmText: '查件',
              onConfirm: () => {
                navToDetail();
              },
            });
          }, 800);
        };
        // 寄件
        let isSMJSend = query && query.type == 'send';
        if (!query || isSMJSend) {
          // 扫码寄支持自定义物品类型，和增值服务
          let dynamicForms = {};
          if (dynamicBrand) {
            dynamicForms.brand = {
              value: dynamicBrand,
              locked: true,
            };
          }
          if (goods_name) {
            dynamicForms.goods_name = {
              value: goods_name,
              locked: true,
            };
          }
          if (is_collection) {
            dynamicForms.service = {};
            switch (is_collection) {
              case 1:
                dynamicForms.service = {
                  ...dynamicForms.service,
                  is_collection: { value: 1 },
                  collection: { required: true },
                };

                break;
              case 2:
                dynamicForms.service = {
                  ...dynamicForms.service,
                  is_arrive_pay: { value: 1 },
                  arrive_pay: { required: true },
                };

                break;
              case 3:
                const customFresh = !isFresh(goods_name);
                dynamicForms.goods_name = {
                  value: goods_name || '生鲜',
                  customFresh,
                  locked: true,
                };
                break;
            }
          }
          // 寄件处理
          if (!order_id || isSMJSend) {
            // 未下单
            if (process.env.MODE_ENV == 'wkd') {
              let smjData = res;
              let customer = '';
              if (
                res.relation_info &&
                (res.relation_info.type == 'customer' || res.relation_info.customer_id > 0)
              ) {
                const { customer_id, customer: customer_name } = res.relation_info || {};
                customer = {
                  id: customer_id,
                  customer: customer_name,
                };
              }
              resolve({
                ...formatShopInfo({
                  ...res.relation_info,
                  courier_brand: brand, // 快递员所属品牌
                  smjData,
                  customer,
                  type: dakId ? 'dak' : 'courier',
                }),
                notUniversalCode: true,
                dynamicForms,
              });
            } else {
              if (type == 'dak') {
                getStoreInfo(dakId)
                  .then((dakInfo) => {
                    resolve({
                      ...dakInfo,
                      relation_id: relationId,
                      notUniversalCode: true,
                      dynamicForms,
                    });
                  })
                  .catch(reject);
              } else {
                resolve({
                  ...formatShopInfo({
                    ...res.relation_info,
                  }),
                  notUniversalCode: true,
                  dynamicForms,
                });
              }
            }
          }
          if (order_id) {
            // 已下单
            queryModal();
          }
        } else {
          const { type: scanType } = query;
          // 查件处理
          if (order_id) {
            if (scanType == 'find') {
              navToDetail();
            } else {
              queryModal();
            }
            resolve();
          } else {
            if (scanType == 'find') {
              setTimeout(() => {
                Taro.kbModal({
                  content: [
                    {
                      text: '该码尚未下单，无法查件。下单请扫上联寄件码！或点击下方【寄件】按钮，进行下单。完成后请将上联寄件码贴在包裹上',
                      className: 'kb-color__blue',
                    },
                  ],
                  confirmText: '寄件',
                  onConfirm: () => {
                    navToEdit(qrCode);
                  },
                });
              }, 300);
            } else {
              resolve();
              navToEdit(qrCode);
            }
            resolve();
          }
        }
      },
    });
  });
}
// 出库仪专用
export function oiSendOrderQrcodeScan(qrCodeContent, mode = 'general') {
  const {
    query: { instrumentSendOrder },
  } = scanParse(qrCodeContent);
  if (mode === 'general') {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/weixin/mini/handle/QrCode/OISendOrderQrcodeScan',
        toastLoading: false,
        data: { scene_val: instrumentSendOrder },
        onThen: ({ code, data: { weight, packageImages }, msg }) => {
          if (code == 0) {
            getStoreInfo('last').then((data) => {
              resolve({
                ...data,
                dynamicForms: {
                  goods_weight: { value: weight },
                  package_images: { value: [packageImages] },
                },
              });
            });
          } else {
            reject({ message: msg });
          }
        },
      });
    });
  }
  navToEdit(qrCodeContent);
}
// "dynamicForms":{"goods_weight":{"value":1}},
// 获取驿站信息
export function getStoreInfo(dakId, com, extraData) {
  if (!dakId || dakId == 0) {
    return Promise.reject(new Error('缺少驿站ID，或二维码内容'));
  }
  const { qrCodeContent } = dakId;
  if (checkQueryIsMatch(qrCodeContent, qrCodeQueryMap.cky)) {
    // 出库仪
    return oiSendOrderQrcodeScan(qrCodeContent);
  }
  if (checkUrlPathIsMatch(qrCodeContent, qrCodePathMap.generalSend)) {
    return getStoreInfoFromQrCode(qrCodeContent);
  }
  return new Promise((resolve, reject) => {
    let url = '';
    let data = null;
    let nonceKey = '';
    if (dakId === 'last') {
      // 最后一次使用的
      url = 'minpost/relation/getDefaultRelation';
    } else if (qrCodeContent) {
      // 二维码扫描的
      return getStoreInfoFromQrCode(qrCodeContent).then((scanData) => {
        if (isUniversalCode(scanData)) {
          return getLastUseRelation(com, extraData).then((dakInfo) => resolve(dakInfo));
        } else {
          resolve(formatShopInfo(scanData));
        }
      });
    } else {
      // 驿站id获取
      data = isObject(dakId)
        ? dakId
        : {
            dakId,
          };
      url = 'waybill/record/dakInfo';
      nonceKey = process.env.MODE_ENV === 'yz' ? 'dakId' : '';
    }
    url = `/api/weixin/mini/${url}`;
    if (process.env.MODE_ENV === 'wkd') {
      url = '/g_order_core/v2/mina/Dak/getDakInfo';
      const inn_id = dakId;
      data = {
        inn_id,
      };
      nonceKey = 'inn_id';
    }
    requestGetOnce(
      {
        url: url,
        data: {
          ...extraData,
          ...data,
        },
        nonceKey,
        toastLoading: false,
        mastHasMobile: false,
        loadingStatusKey: 'loading',
        nonceKey,
        onThen: ({ code, data: res, msg }) => {
          if (code == 0) {
            resolve(formatShopInfo(res));
          } else {
            reject(new Error(code > 0 ? msg || '无法获取驿站信息' : ''));
          }
        },
      },
      com,
    );
  });
}

/**
 *
 * @description 获取驿站信息携带是否开启预约取件
 * @param {*} params
 */
export function getStoreInfoWithPickUp(dakId) {
  return new Promise((resolve, reject) => {
    request({
      url: '/g_wkd/v2/mina/Dak/dakInfo',
      data: {
        dakId,
      },
      nonceKey: 'dakId',
      toastLoading: false,
      onThen: (res) => {
        const { code, data, msg } = res;
        code == 0 ? resolve(formatShopInfo(data)) : reject(new Error(msg));
      },
    });
  });
}

/**
 *
 * @description 从列表中获取快递员
 * @returns
 */
function getCourierFromList() {
  return new Promise((resolve, reject) => {
    request({
      url: apis['courier.list'],
      toastLoading: false,
      onThen: ({ data }) => {
        const { list } = data;
        const [courierData] = isArray(list) ? list : [];
        if (courierData) {
          resolve(courierData);
        } else {
          reject(new Error('暂无常用下单对象'));
        }
      },
    });
  });
}

/**
 * @description 从缓存中获取快递员
 * @returns
 */
export function getCourierFromStorage() {
  return new Promise((resolve) => {
    getStorage({
      key: relationStorageKey,
    })
      .then((res) => {
        const { data = null } = res.data || {};
        resolve(data);
      })
      .catch(() => resolve(null));
  });
}

/**
 *
 * @description 定制驿站-支付宝版本：获取-定制驿站信息
 * @returns
 */
export function getDakInfoByPlatform() {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/DakMini/Config/getDakInfoByPlatform',
      toastLoading: false,
      onThen: (res) => {
        const { data } = res;
        resolve(data && data.cm_id ? formatShopInfo(data) : null);
      },
    });
  });
}

// 获取最近使用过的驿站
export function getLastUseRelation(com, extraData) {
  if (process.env.MODE_ENV === 'wkd') {
    return new Promise((resolve) => {
      getCourierFromStorage().then((data) => {
        data
          ? resolve(data)
          : getCourierFromList()
              .then(resolve)
              .catch(() => resolve({ brand: 'yjkd' }));
      });
    });
  } else if (process.env.MODE_ENV === 'third.pro' && process.env.PLATFORM_ENV === 'alipay') {
    // 驿站定制-支付宝版
    return getDakInfoByPlatform();
  } else {
    return getStoreInfo('last', com, extraData);
  }
}

// 格式化数据
export function formatShopInfo(data) {
  if (!data) return {};
  const {
    cm_id: dakId = data.inn_id || data.cmId || data.dak_id || '',
    brand_id,
    brandId = brand_id,
    address,
    concat_location = data.innAddress ||
      (data.inn_address && `${data.inn_address}`.replace(/\s/g, '')),
    longitude = data.inn_lng || data.lng,
    latitude = data.inn_lat || data.lat,
    workday = data.inn_time || data.inn,
    phone = data.inn_mobile || data.concat_phone || '',
    province = '',
    city = '',
    district = '',
    town,
    start_time = '',
    end_time = '',
    distance = 0,
    courier_id,
    courier_mobile,
    courier_name,
    brand,
    notUniversalCode,
    smjData,
    customer,
    isKdg,
    courier_brand,
    type,
    ...rest
  } = data;
  let time = '';
  if (workday) {
    time += workday;
  }
  if (start_time && end_time) {
    time += `${start_time} - ${end_time}`;
  }
  const area = province + city + district;
  const name = data.inn_name || data.innName || data.dak_name || data.label || data.cabinet_name;

  return {
    ...rest,
    type,
    dakId,
    brandId,
    dak_id: dakId,
    name,
    desc: data.cabinet_address,
    phone,
    account_name: courier_name,
    account_phone: courier_mobile,
    account_company: type == 'courier' ? courier_brand : brand, // 快递员取外层的品牌
    courier_id,
    address:
      address ||
      (concat_location && concat_location.includes(area)
        ? concat_location
        : [area, town, concat_location || ''].filter((item) => !!item).join('')),
    latitude: 1 * latitude,
    longitude: 1 * longitude,
    time,
    distance: distance > 1 ? `${numeral(distance).format('0.0')}k` : Math.floor(distance * 1000),
    courierDetail: courier_mobile && courier_name && courier_name + '' + courier_mobile,
    placeOrderConfig: data.placeOrderConfig,
    smjData,
    customer,
    notUniversalCode: isBoolean(notUniversalCode)
      ? notUniversalCode
      : isObject(data.placeOrderConfig)
      ? true
      : false,
    start_time,
    end_time,
    isKdg,
  };
}
// 检查代理关系合法性
export function checkIsBrand(data, brand) {
  const brand_id = data && (data.brand_id || data.brandId);
  return !!(data && brand_id && (!!brand ? brand_id === brand : true));
}

// 检查时快递柜
export function checkIsKdg(data) {
  return !!(data && data.type === 'cabinet');
}

// 检查驿站合法性
export function checkIsShop(data) {
  return !!(data && (data.dakId || data.dak_id));
}
// 检查快递员合法性
export function checkIsCourier(data) {
  return !!(data && data.courier_id);
}
// 检查代理关系合法性
export function checkIsAgent(data) {
  return !!(data && data.agent_id);
}

// 建立绑定关系
export function createRelation(req) {
  return new Promise((resolve, reject) => {
    request({
      url: createRelationApi,
      toastLoading: false,
      data: req,
      nonceKey: createRelationNonceKey,
      mastHasMobile: false,
      onThen: (res) => {
        const { data } = res;
        if (isUniversalCode(data)) {
          createRelationSuccess();
          resolve(data);
        } else {
          reject(res);
        }
      },
    });
  });
}
// 查询快递员详情
export function getCourierInfo(idOrPhone, index_shop_id) {
  return new Promise((resolve, reject) => {
    request({
      ...getApiUrlAndDataCourierDetail({ idOrPhone, index_shop_id }),
      toastLoading: false,
      toastError: true,
      // toastSuccess: true,
      onThen: ({ code, data, msg }) => {
        if (code == 0 && data) {
          resolve(data);
        } else {
          reject(new Error(msg || '未获取到快递员信息'));
        }
        logger.info('已获取信息-快递员接口', code, data && data.account_phone, msg);
      },
    });
  });
}

// 预约取件
export function appointmentPickup(data) {
  return new Promise((resolve, reject) => {
    request({
      ...getPickUpApi(data),
      toastError: true,
      toastSuccess: true,
      onThen: ({ code, data: res }) => {
        if (code == 0 && res) {
          resolve(res);
        } else {
          reject(new Error('预约失败'));
        }
      },
    });
  });
}

// 地图导航
export function openLocation(data) {
  const { name, address, longitude, latitude } = data;
  if (longitude && latitude) {
    Taro.openLocation({
      longitude: Number(longitude),
      latitude: Number(latitude),
      name,
      address,
    });
  }
}
export const tagMap = {
  courier: '快递员',
  agent: '云打印机',
  dak: '驿站',
  customer: '大客户',
};

/**
 *
 * @description 获取团队信息
 */
export function getTeamInfo(join_code) {
  return new Promise((resolve, reject) => {
    request({
      url: apis['team.detail'],
      toastLoading: false,
      data: {
        join_code,
      },
      onThen: ({ code, data, msg }) => {
        if (code == 0 || code == 2911) {
          // 2911 - 团队已解散
          resolve({ ...data, code });
        } else {
          reject(new Error(msg));
        }
      },
    });
  });
}

/**
 *
 * @description 获取快递品牌信息
 * @param brand 存在就是某个品牌信息，不存在就是全部品牌列表
 */
let brandsWithDescribe = null;
export function getBrandInfo(brand) {
  return new Promise((resolve, reject) => {
    // 处理品牌查找并响应
    const triggerResolve = (list) => {
      const data = brand ? list.find((item) => item.brand === brand) : list;
      if (data) {
        resolve(data);
      } else {
        reject(new Error('快递品牌不存在'));
      }
    };

    if (brandsWithDescribe) {
      triggerResolve(brandsWithDescribe);
      return;
    }

    const loader = debounce(
      function () {
        request({
          url: apis['brand.list'],
          toastLoading: false,
          data: {
            is_new: '1',
          },
          onThen: ({ code, data, msg }) => {
            logger.info('快递公司配置', code, data);
            if (code == 0 && isArray(data)) {
              brandsWithDescribe = data;
              triggerResolve(data);
            } else {
              reject(new Error(msg));
            }
          },
        });
      },
      100,
      {
        trailing: true,
      },
    );
    loader();
  });
}

/**
 *
 * @description 获取同城急送品牌信息
 */
export function getTcjsBrandInfo(brand) {
  return new Promise((resolve, reject) => {
    request({
      url: apis['tcjs.list'],
      onThen: ({ code, data, msg }) => {
        if (code == 0) {
          let aRes = null;
          for (let key in data) {
            data[key].map((item) => {
              if (item.brand == brand) {
                aRes = item;
              }
            });
          }
          if (aRes) {
            resolve(aRes);
          } else {
            reject(new Error('急送品牌不存在'));
          }
        } else {
          reject(new Error(msg));
        }
      },
    });
  });
}

/**
 * @description 获取大客户信息
 */
export function getCustomerInfo(opts) {
  const { customer_id } = opts || {};
  return new Promise((resolve, reject) => {
    request({
      url: '/g_wkd/v2/work/Customer/getCustomerInfo',
      toastLoading: false,
      data: { customer_id },
      nonceKey: 'customer_id',
      onThen: (res) => {
        // console.log("getCustomerInfo.res", res);
        if (res.code == 0) {
          addCustomerRelation({ customer_id });
          resolve(formatCustomerData(res.data));
        } else {
          reject(new Error(res.msg));
        }
      },
    });
  });
}

/**
 * @description 格式化小邮筒下单对象数据
 * @param service
 * 驿站 dak                      =>  https://m.kuaidihelp.com/cpm/qrc/minipost_1001/13672
 * 打印机大客户 minpost_customer  =>  https://m.kuaidihelp.com/cpm/qrc/minipost_1001/13667
 * 打印机 cloud_print            =>  https://m.kuaidihelp.com/cpm/qrc/minipost_1001/13665
 * 快递员大客户 courier_customer  =>  https://m.kuaidihelp.com/cpm/qrc/minipost_1001/13647
 * 快递员 courier                =>  https://m.kuaidihelp.com/cpm/qrc/minipost_1001/19
 * 驿站大客户 dak_customer        =>  https://m.kuaidihelp.com/cpm/qrc/minipost_1001/13805
 */
export function formatMiniPostData(data) {
  const { service, data: originData } = data || {};
  let formatData = extractData(originData, [
    'dak_id',
    'courier_id',
    ['inn_name', 'dak_name'],
    ['dak_mobile', 'dak_mobile'],
    ['account_name', 'courier_name'],
    ['account_phone', 'courier_mobile'],
    [
      'customer',
      ({ id }) =>
        (service == 'courier_customer' || service == 'dak_customer') && id ? originData : '',
    ],
    ['originData', () => originData],
  ]);
  if (formatData.customer && formatData.originData) {
    delete formatData.originData;
  }
  return {
    type: service,
    ...formatData,
  };
}

/**
 * @description 格式化快递员大客户数据
 */
export function formatCustomerData(data) {
  const { courier_id, courier_name, courier_mobile, customer_type, dak_id, dak_name, dak_mobile } =
    data || {};
  let res =
    customer_type === 'dak_customer'
      ? {
          dak_id,
          inn_name: dak_name,
          dak_mobile: dak_mobile,
        }
      : {
          courier_id,
          account_name: courier_name,
          account_phone: courier_mobile,
        };
  res.customer = data && data.id ? data : '';
  return res;
}

/**
 * 整合获取微快递下单对象信息
 */
export function getWkdStoreInfo(opts) {
  const {
    courier_phone,
    phone = courier_phone,
    join_code,
    dak_id,
    brand,
    index_shop_id,
    qrCodeContent,
    customer_id,
  } = opts || {};
  return Promise.resolve(
    phone // 快递员
      ? getCourierInfo(phone, index_shop_id)
      : join_code // 团队
      ? getTeamInfo(join_code)
      : dak_id // 驿站
      ? getStoreInfo(dak_id)
      : brand //快递公司
      ? getBrandInfo(brand)
      : qrCodeContent //扫码-扫码寄
      ? getStoreInfoFromQrCode(qrCodeContent)
      : customer_id //大客户
      ? getCustomerInfo({ customer_id })
      : null,
  );
}

export const triggerUpdateInfo = (data) => {
  return new Promise((resolve, reject) => {
    const {
      dakId,
      relation_id,
      notUniversalCode,
      isFromDefault,
      is_vip,
      courier_id,
      isDefault,
      brandId,
    } = data || {};
    let disabled = !!notUniversalCode || is_vip == 1;
    if (!dakId && !courier_id && !brandId)
      return isDefault ? resolve(data) : reject({ code: 1001, msg: '错误的下单对象' });
    // vip时设置为默认下单关系, 非扫码寄，并且没有关系id的情况下触发绑定下单关系接口
    if (
      (!relation_id && !notUniversalCode) ||
      (is_vip == 1 && !notUniversalCode && !isFromDefault)
    ) {
      // 是否需要创建下单关系
      const req = {
        dak_id: dakId,
        courier_id,
      };
      createRelation(req)
        .then((id) => {
          const { name } = data;
          resolve({
            ...data,
            disabled,
            relation_id: id,
            label: `快递员:${name}`,
          });
        })
        .catch((err) => reject(err));
    } else {
      resolve({
        ...data,
        disabled,
      });
    }
  });
};

export const fixRelationInfo = (relationInfo) => {
  const {
    name = '',
    dakId = '',
    courier_id,
    courierId = courier_id,
    qrCodeContent,
    brandId,
    relation_id,
  } = relationInfo || {};
  return new Promise((resolve, reject) => {
    if (relation_id && dakId) {
      return resolve(relationInfo);
    }
    if (name) {
      resolve(formatShopInfo(relationInfo));
    } else if (dakId) {
      getStoreInfo(dakId)
        .then((data) => {
          resolve(data);
        })
        .catch(() => reject);
    } else if (courierId) {
      getCourierInfo(courierId)
        .then((data) => resolve(data))
        .catch(() => reject);
    } else if (brandId) {
      getRelationByBrand(brandId).then((data) => {
        resolve(data);
      }, reject);
    } else if (qrCodeContent) {
      getStoreInfo({
        qrCodeContent,
      })
        .then((data) => resolve(data))
        .catch(() => reject);
    } else {
      getStoreInfo('last')
        .then((data) => resolve({ isDefault: true, ...data }))
        .catch(() => reject);
    }
  });
};
// 获取品牌下单关系
export const getRelationByBrand = (brand) => {
  if (!brand) return Promise.reject();
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/minpost/relation/getRelationByBrand',
      data: {
        brand,
      },
      onThen: (res) => {
        const { code, data } = res;
        if (code === 0) {
          resolve(formatShopInfo(data));
        } else {
          reject();
        }
      },
    });
  });
};

/**
 * @description 解析并获取小邮筒下单关系
 */
export function getMiniPostRelation(opts) {
  const { text } = opts || {};
  return new Promise((resolve, reject) => {
    request({
      url: '/g_wkd/v2/work/Customer/qrcode',
      data: {
        text,
      },
      toastError: true,
      onThen: (res) => {
        if (res.code == 0) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
    });
  });
}

/**
 * @description 绑定大客户下单关系，返回大客户id
 */
export function addCustomerRelation(opts) {
  const { customer_id } = opts || {};
  return new Promise((resolve, reject) => {
    request({
      url: '/g_wkd/v2/work/CustomerUser/joinCustomerUser',
      data: {
        customer_id,
      },
      nonceKey: 'customer_id',
      toastError: true,
      onThen: (res) => {
        // console.log("addCustomer.res", res);
        if (res.code == 0) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
    });
  });
}

/**
 * @description 检查大客户对该用户授权状态
 */
export function checkCustomerAuthStatus(customer_id) {
  return new Promise((resolve, reject) => {
    request({
      url: '/g_wkd/v2/work/CustomerUser/getUserAuthStatus',
      data: {
        customer_id,
      },
      toastError: true,
      onThen: ({ code, data: { auth_status, customer_name, root_name, refresh } = {}, msg }) => {
        if (code == 0) {
          // authStatus 0已拒绝/1已授权/2审核中;
          let authStatus = refresh ? 0 : auth_status ? 1 : 2;
          switch (authStatus) {
            case 0:
              Taro.kbModal({
                content: [
                  {
                    text: '您的审核未通过，已为您切换至默认下单关系！',
                  },
                ],
              });
              break;
            case 2:
              Taro.kbModal({
                content: [
                  {
                    text: `您加入【${customer_name}】进行企业协同下单的申请，管理员【${root_name}】正在审核，请审核后开始协同下单。`,
                  },
                  {
                    className: 'kb-size__base kb-color__grey',
                    text: "获取审核结果通知，请点击下方按钮关注'微快递'即可",
                  },
                ],
                confirmText: '去关注',
                onConfirm: () => {
                  Taro.navigator({
                    url: 'https://mp.weixin.qq.com/s/siOVe7nMKiCVD-TxEeqkdg',
                    target: 'webview',
                  });
                },
              });
              break;
          }
          resolve(authStatus);
        } else {
          reject(msg);
        }
      },
    });
  });
}

/**
 * @description 检查用户是否为该大客户管理员
 */
export function checkCustomerIsAdmin(opts) {
  const { customer_id } = opts || {};
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/work/CustomerUser/getCustomerAdmin',
      data: {
        customer_id,
      },
      toastError: true,
      onThen: (res) => {
        resolve(res.code == 0 && res.data);
      },
    });
  });
}

/**
 * 获取默认驿站头像
 *  */
export const getDefaultDakAvatarUrl = (type = process.env.MODE_ENV) => {
  switch (type) {
    case 'third.post':
      return 'https://cdn-img.kuaidihelp.com/yz/miniapp/logo_post.jpg?v=1';
    default:
      return '';
  }
};
