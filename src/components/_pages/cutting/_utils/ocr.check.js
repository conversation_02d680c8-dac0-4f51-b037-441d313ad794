/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import isPlainObject from 'lodash/isPlainObject';
import isArray from 'lodash/isArray';
import { getCities } from '../../city/_utils';

/**
 *
 * @description 移除空格
 * @param {*} subtext
 */
export function subtextNoSpace(subtext) {
  return `${subtext || ''}`.replace(/\s/g, '');
}

/**
 *
 * @description 收集列表
 * @param {*} data
 */
function collectList(data) {
  const list = [];
  if (isPlainObject(data)) {
    Object.keys(data).forEach((key) => {
      const items = data[key];
      list.push(...items.map((item) => item.name));
    });
  }

  return list;
}

/**
 *
 * @description 格式化城市
 * @param {*} data
 */
function formatCities(data) {
  const { province, city, district } = data || {};
  Object.keys(city);
  return {
    province: isArray(province) ? province.map((item) => item.name) : [],
    city: collectList(city),
    district: collectList(district),
  };
}

/**
 *
 * @description 按照长度过滤
 * @param {{subtext:string}} item
 */
function filterByLength(item) {
  return subtextNoSpace(item.subtext).length > 5;
}

/**
 *
 * @description 兼容异常城市，例如亳州-》毫州；
 */
function patchCities(text) {
  return `${text}`.replace(/毫州/g, '亳州');
}

/**
 *
 * @param {string} text
 * @param {string[]} by
 */
function checkCities(text, by) {
  const subtext = patchCities(text);
  return by.some((item) => {
    let has = `${subtext}`.includes(item);
    if (has) return has;
    let byItem = item.replace(/省|市|区|县|自治县|自治区|特别行政区/, '');
    if (byItem.length === 1) {
      byItem = item;
    }
    has = `${subtext}`.includes(byItem);
    return has;
  });
}

/**
 *
 * @description 补充区域信息
 * @param {*} item
 * @param {*} condition
 * @param {*} key
 */
function patchItem(item, condition, key) {
  item[key] = condition ? 1 : 0;
}

/**
 *
 * @description 按照城市过滤
 * @param {{subtext:string}} item
 * @param {{province:string[];city:string[];district:string}} cities
 */
function filterByCities(item, cities) {
  const { subtext } = item;
  const { province, city, district } = cities;

  const hasProvince = checkCities(subtext, province);
  const hasCity = checkCities(subtext, city);
  const hasDistrict = checkCities(subtext, district);

  patchItem(item, hasProvince, 'province');
  patchItem(item, hasCity, 'city');
  patchItem(item, hasDistrict, 'district');

  // 省市或者省区或者市区认为是合法的城市信息
  return (hasProvince && hasCity) || (hasProvince && hasDistrict) || (hasCity && hasDistrict);
}

/**
 *
 * @description 检查是否含有城市信息
 * @param {{subtext:string}[]} list
 * @returns {boolean}
 */
export async function checkHasAddress(list) {
  const cities = await getCities().then(formatCities);
  const filterList = list.filter(filterByLength);
  const hasAddressList = filterList.filter((item) => filterByCities(item, cities));
  return hasAddressList;
}

/**
 *
 * @param {{subtext:string}} item
 */
export function checkItemContact(item) {
  const subtext = subtextNoSpace(item.subtext);
  const contactMap = {
    contact: /(0\d{2,3}(-|\s?))?[2-9]{1}\d{6,7}((-|\s?)\d{1,4})?/, // 固话
    phone: /1[\d\*]{10}/, // 手机号
  };
  return Object.values(contactMap).some((reg) => reg.test(subtext));
}

/**
 *
 * @description x相近
 * @param {*} cur
 * @param {*} next
 */
export function checkNearX(cur, next, fix1 = 0, fix2 = 0) {
  if (subtextNoSpace(next.subtext).length > 1) return true;
  let {
    origin: { x: x1 },
  } = cur;
  let {
    origin: { x: x2 },
  } = next;
  x1 -= fix1;
  x2 -= fix2;
  return x1 > x2 || Math.abs(x1 - x2) < 5;
}

/**
 *
 * @description 检查在同一的两组数据
 * @param {*} cur
 * @param {*} next
 */
export function checkNearY(cur, next) {
  const {
    size: size1,
    subtext: subtext1,
    origin: { y: y1 },
  } = cur;
  const {
    size: size2,
    subtext: subtext2,
    origin: { y: y2 },
  } = next;
  // 当前两行y轴相差不超过最小高度的1/3;
  const isNearY = Math.abs(y1 - y2) < Math.min(size1.height, size2.height) / 3;
  if (isNearY) {
    const check = (t1, t2) => {
      const nameLength = t1.length;
      const isName = nameLength > 1 && nameLength < 8; // 长度不超过8
      const hasContact = checkItemContact({ subtext: t2 });
      return isName && hasContact;
    };
    return check(subtext1, subtext2) || check(subtext2, subtext1);
  }
  return false;
}

/**
 *
 * @description 检查是否含有联系方式
 * @param {{subtext:string}[]} list
 * @returns {boolean}
 */
export function checkHasContact(list) {
  return list.some(checkItemContact);
}

/**
 *
 * @description 最后一行的宽度，比倒数第二行2/3小，认为是最后一行
 * @param {*} subs
 */
export function checkIsEndLine(subs) {
  const subsLen = subs.length;
  const last = subs[subsLen - 1];
  const lastPre = subs[subsLen - 2];
  if (last && lastPre) {
    const lastWidth = last.size.width; // 最后一的宽度
    const lastPreWidth = lastPre.size.width; // 倒数第二行的宽度
    return lastWidth < (lastPreWidth * 2) / 3;
  }
  return false;
}

/**
 *
 * @description 无中文
 */
export function checkHasNoChinese(item) {
  const reg = /[\u4e00-\u9fa5]/;
  return !reg.test(item.subtext);
}

/**
 *
 * @description 子列查找结束
 * @param {*} cur
 * @param {*} next
 * @param {*} subs
 * @param {*} dir
 */
export function checkSubsEnd(cur, next, subs, dir) {
  if ((dir === 'pre' && checkItemContact(cur)) || checkHasNoChinese(next)) {
    // 向上找：当前是联系方式，直接false
    return true;
  }

  if (dir === 'next') {
    // 向下找：
    if (checkItemContact(next)) {
      // 下一行是联系方式
      if (checkHasContact(subs)) {
        // 但已经有了；
        return true;
      }
    } else {
      // 下一行不是联系方式
      if (checkIsEndLine(subs) && checkHasContact(subs)) {
        // 但已经认为是结束了，且已经有联系方式了；
        return true;
      }
    }
  }
  return false;
}

/**
 *
 * @description 子列超过限制
 * @param {*} subs
 */
export function checkSubsOverrun(subs) {
  const subsLen = subs.length;
  if (subsLen >= 4) {
    if (checkHasContact(subs)) {
      // 已经有联系方式
      return true;
    }
    return checkIsEndLine(subs);
  }
  return false;
}
