/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isString from 'lodash/isString';
import request from '@base/utils/request';
import { formatParseAddressData } from '@base/utils/addressFormat';

// 清理base64，去除类型标记前缀
function clearBase64Data(base64) {
  return `${base64}`.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
}

// 图片上传并识别
export async function triggerOcrImage(files) {
  console.log('图片上传并识别==>files', files);
  const filePaths = isArray(files) ? files.filter((item) => item && item.filePath) : null;
  if (!filePaths || filePaths.length === 0) {
    Taro.kbToast({ text: '请先选择图片！' });
    return [];
  }
  const ins = Taro.kbToast({ status: 'loading', text: '解析中' });
  const errMsg = [];
  const collectErrMsg = (res) => {
    const { code, msg } = res;
    if (code > 0 && msg) {
      errMsg.push(msg);
    }
    return res;
  };
  const res = await Promise.all(
    filePaths.map(
      ({ filePath, isBase64, ...restItem }) =>
        new Promise((resolve) => {
          if (process.env.MODE_ENV === 'wkd') {
            return request({
              url: '/v1/WeApp/uploadAttachments',
              ...(!isBase64
                ? {
                    data: {
                      type: 'minaOcr',
                      filePath,
                    },
                    requestDataType: 'file',
                  }
                : {
                    // 微信本地文件保存失败，改为base64上传解析
                    data: {
                      type: 'minaOcr',
                      b_file: clearBase64Data(filePath),
                    },
                  }),
              toastLoading: false,
              onThen: (res1) => {
                const { data: res1Data } = res1;
                const { file_path } = res1Data || {};
                if (file_path) {
                  request({
                    url: '/v1/Ocr/ocrNamePhoneAddress',
                    data: {
                      img: file_path,
                    },
                    toastLoading: false,
                    onThen: (res2) => {
                      const { data: res2Data } = res2;
                      const { original: text } = res2Data || {};
                      if (text && isString(text)) {
                        // 直接将图片内容返回
                        resolve({
                          ...restItem,
                          text,
                          img: `https://upload.kuaidihelp.com/minaOcr/${file_path}`,
                          address: formatParseAddressData(
                            isArray(res2Data) ? res2Data : [res2Data],
                          )[0],
                        });
                      } else {
                        collectErrMsg(res2);
                        resolve(null);
                      }
                    },
                  });
                } else {
                  collectErrMsg(res1);
                  resolve(null);
                }
              },
            });
          } else {
            return request({
              url: '/api/weixin/mini/address/parse/img',
              ...(!isBase64
                ? {
                    data: {
                      filePath,
                    },
                    requestDataType: 'file',
                  }
                : {
                    // 微信本地文件保存失败，改为base64上传解析
                    data: {
                      b_file: clearBase64Data(filePath),
                    },
                  }),
              toastLoading: false,
              onThen: (res1) => {
                const { data: res1Data } = res1;
                const { text } = res1Data || {};
                if (text && isString(text)) {
                  // 直接将图片内容返回
                  resolve({
                    ...restItem,
                    text,
                    img: filePath,
                  });
                } else {
                  collectErrMsg(res1);
                  resolve(null);
                }
              },
            });
          }
        }),
    ),
  );
  const list = res.filter((item) => !!item);
  if (list.length === 0) {
    ins.update({ text: errMsg.join('、') || '未识别出地址信息' });
  } else {
    ins.close();
  }
  return list;
}
