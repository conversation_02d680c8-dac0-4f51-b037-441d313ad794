/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isFunction from 'lodash/isFunction';
import isArray from 'lodash/isArray';
import { getStorage, randomCode, setStorage } from '@base/utils/utils';
import { Ocr, adjustedAngleInCircle, transDegToAngle } from './ocr';
import { rotateIcon, delIcon } from './icon';
import { triggerOcrImage } from './ocr.upload';

/**
 *
 * @description 初始化画布
 */
export class CuttingCanvas {
  constructor({ id, pixelRatio, src, max = 5 }) {
    this.canvasId = id;
    this.maxSelectionBox = max; // 手动添加时最大条数
    this.lockNumber = 0; // 调用this.clean后剩余的条数会锁定，自动添加框，最多只允许等于 lockNumber
    this.dragInfo = {}; // 当前激活的识别框
    this.selectionBoxes = []; // 识别框
    this.OCRBoxes = []; // ocr识别出的数据
    this.pixelRatio = pixelRatio || Taro.getSystemInfoSync().pixelRatio; // 分辨率
    // 初始化
    this.init().then(() => {
      if (src) {
        // 初始传入图片直接触发更新
        this.updateImage(src);
      }
    });
  }
  init() {
    return new Promise((resolve) => {
      const query = Taro.createSelectorQuery();
      query
        .select(`#${this.canvasId}`)
        .fields({ node: true, size: true, rect: true })
        .exec((res) => {
          const [{ node, left, top, width, height }] = res || [];
          const canvas = node;
          const ctx = canvas.getContext('2d');
          const dpr = this.pixelRatio;
          canvas.originalWidth = width;
          canvas.originalHeight = height;
          canvas.width = res[0].width * dpr;
          canvas.height = res[0].height * dpr;
          canvas.offsetLeft = left;
          canvas.offsetTop = top;
          ctx.scale(dpr, dpr);
          this.ctx = ctx;
          this.canvas = canvas;
          this.ocr = new Ocr();
          // 回到ready
          this.runCallback(this.readyCallback, this);
          resolve(this);
        });
    });
  }
  // 更新图片
  async updateImage(src) {
    if (!src) return;
    this.imageInfo = {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      scaleWidth: 0,
      scaleHeight: 0,
      distance: 0,
      initScale: 1,
      scale: 1,
      scaling: 0, // 缩放中
      angle: 0, // 旋转角度
      lastAngle: 0, // 最后一次旋转
      startAngle: 0, // 起始角度
      pinchCenter: {
        x: 0,
        y: 0,
      },
      dragStart: { x: 0, y: 0 }, // 起始点
      isUpdateImage: true, // 更新图片
      isDraggingImage: false, // 拖动图片
      isResizingImage: false, // 缩放图片
      isRotatingImage: false, // 旋转图片
    };
    this.lockNumber = 0; // 更新图片，重置锁定数量
    const [image, imageRotate, imageDel] = await this.loadImage([src, rotateIcon, delIcon]);
    // 记录图片元素尺寸
    // 背景图
    this.image = image;
    // 旋转/删除图标
    this.imageHandler = {
      rotate: imageRotate,
      del: imageDel,
    };
    this.initImageInfo();
    this.draw();
    this.runOCR();
    // 绘制完
    this.imageInfo.isUpdateImage = false;
  }
  // 加载图片
  loadImage(urls, canvas = this.canvas) {
    return Promise.all(
      urls.map((item) => {
        const image = canvas.createImage();
        return new Promise((resolve) => {
          image.onload = () => resolve(image);
          image.src = decodeURIComponent(item);
        });
      }),
    );
  }
  // 初始图片位置
  initImageInfo() {
    const { imageInfo, canvas, image } = this;
    // 缩放图片
    const { width, height } = image;
    const scale = Math.min(canvas.originalWidth / width, 1);
    imageInfo.width = width;
    imageInfo.height = height;
    imageInfo.scaleWidth = width * scale;
    imageInfo.scaleHeight = height * scale;
    imageInfo.initScale = scale;
    imageInfo.scaling = 0;
    imageInfo.scale = scale;

    // 图片位置
    const center = this.getImageCenterPosition();
    imageInfo.x = center.x;
    imageInfo.y = center.y;
  }
  // 绘制图片
  drawImage(ctx = this.ctx, imageInfo = this.imageInfo, image = this.image) {
    if (!image) return;
    const { scaleWidth, scaleHeight, angle, x, y } = imageInfo;
    ctx.save();
    // 绘制图像
    this.rotateCanvas(
      angle,
      {
        x,
        y,
        width: scaleWidth,
        height: scaleHeight,
      },
      ctx,
    );
    ctx.drawImage(image, x, y, scaleWidth, scaleHeight);
    ctx.restore();
  }
  // 绘制入口
  draw() {
    const { ctx, canvas, image } = this;
    if (!image) return;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    this.drawImage();
    // 绘制蒙层
    this.drawMask();
    // 绘制选择框
    this.drawSelectionBoxes();
  }
  // 绘制选择框区域图片
  drawSelectionImage(dragInfo, ctx = this.ctx, imageInfo = this.imageInfo) {
    const { x: dragX, y: dragY, width: dragWith, height: dragHeight, angle } = dragInfo;
    ctx.save();
    ctx.beginPath();
    this.rotateCanvas(angle, dragInfo, ctx); // 旋转
    ctx.moveTo(dragX, dragY);
    ctx.lineTo(dragX + dragWith, dragY);
    ctx.lineTo(dragX + dragWith, dragY + dragHeight);
    ctx.lineTo(dragX, dragY + dragHeight);
    ctx.closePath();
    ctx.clip();

    this.rotateCanvas(-1 * angle, dragInfo, ctx); // 回正绘制图片
    // 绘制截取的图片内容到选择框区域
    this.drawImage(ctx, imageInfo);
    ctx.restore();
  }
  // 计算选择框区域的中心点坐标
  getBoxCenterPosition(dragInfo = this.dragInfo) {
    const { canvas } = this;
    const { width, height } = dragInfo;
    const centerX = (canvas.originalWidth - width) / 2;
    const centerY = (canvas.originalHeight - height) / 2;
    return {
      x: centerX,
      y: centerY,
    };
  }
  // 绘制选择框
  drawSelectionBox(dragInfo) {
    const { ctx } = this;
    // ctx.globalCompositeOperation = 'destination-out';
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.strokeRect(dragInfo.x, dragInfo.y, dragInfo.width, dragInfo.height);
    ctx.globalCompositeOperation = 'source-over';
  }
  // 绘制所有选择框
  drawSelectionBoxes() {
    const { selectionBoxes, ctx, dragInfo } = this;
    if (!isArray(selectionBoxes)) return;
    const { id: activeId } = dragInfo || {};
    selectionBoxes.forEach((item) => {
      const isActive = activeId === item.id;
      ctx.save();
      ctx.globalAlpha = isActive ? 1 : 0.6;
      // 绘制选择框-图片
      this.drawSelectionImage(item);
      // 旋转
      this.rotateCanvas(item.angle, item);
      // 绘制选择框
      this.drawSelectionBox(item);
      // 绘制手柄
      this.drawHandles(item);
      ctx.restore();
    });
  }
  // 手柄尺寸
  getResizeHandles(dragInfo) {
    const { x, y, width, height } = dragInfo;
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    return [
      { x: x, y: y, poi: 'lt' },
      { x: centerX, y: y, poi: 'tc' },
      { x: x + width, y: y, poi: 'rt' },
      { x: x + width, y: centerY, poi: 'rc' },
      { x: x + width, y: y + height, poi: 'rb' },
      { x: centerX, y: y + height, poi: 'bc' },
      { x: x, y: y + height, poi: 'lb' },
      { x: x, y: centerY, poi: 'lc' },
    ];
  }
  // 旋转/删除手柄
  getRotateHandle(dragInfo) {
    const { selectionBoxes } = this;
    const { x, y, width, height } = dragInfo;
    const poi = selectionBoxes.length === 1 ? 'rotate' : 'del';
    const image = this.imageHandler[poi];
    const { width: w, height: h } = image;
    const scale = 0.7; // 手柄缩放大小
    const rotateW = w * scale;
    const rotateH = h * scale;
    const xSpace = 10;
    return {
      image,
      x: x + width + xSpace,
      y: y + height / 2 - rotateH / 2,
      width: rotateW,
      height: rotateH,
      poi,
    };
  }
  // 绘制调整手柄
  drawHandles(dragInfo) {
    const { ctx } = this;
    const handles = this.getResizeHandles(dragInfo);
    ctx.fillStyle = '#ffffff';
    handles.forEach((handle) => {
      ctx.beginPath();
      ctx.arc(handle.x, handle.y, 5, 0, 2 * Math.PI);
      ctx.closePath();
      ctx.fill();
    });
    const { x, y, width, height, image } = this.getRotateHandle(dragInfo);
    ctx.drawImage(image, x, y, width, height);
  }
  // 绘制蒙层
  drawMask() {
    const { ctx } = this;
    const { canvas } = ctx;
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }
  // 调用对应callback
  runCallback(callback, ...arg) {
    if (isFunction(callback)) {
      callback(...arg);
    }
  }
  // 准备就绪
  onReady(callback) {
    this.readyCallback = callback;
  }
  // 识别
  onOCR(callback) {
    this.OCRCallback = callback;
  }
  // 清除
  onClean(callback) {
    this.cleanCallback = callback;
  }
  // 缩放选择框
  resizeSelectionBox(mouseX, mouseY) {
    const { dragInfo, imageInfo } = this;
    const { handle, angle } = dragInfo;
    if (!handle) {
      return;
    }
    const { poi } = handle;
    let isCenterPoi = poi.includes('c'); // 边上中心点，仅支持横向或竖向放大
    let isTop = poi.includes('t'); // 向上
    let isBottom = poi.includes('b'); // 向下
    let isLeft = poi.includes('l'); // 向左
    let isRight = poi.includes('r'); // 向右

    const isUpLeft = angle <= transDegToAngle(-80) && angle >= transDegToAngle(-110);
    const isDownLeft = angle >= transDegToAngle(80) && angle <= transDegToAngle(110);

    const isOverturn =
      (angle >= transDegToAngle(90) && angle <= transDegToAngle(180)) ||
      (angle <= transDegToAngle(-90) && angle >= transDegToAngle(-180));
    let dx = mouseX - imageInfo.dragStart.x;
    let dy = mouseY - imageInfo.dragStart.y;
    if (isUpLeft || isDownLeft) {
      // 上下拖动左右缩放按钮
      const by = isUpLeft ? -1 : 1;
      [dx, dy] = [by * dy, by * dx];
    }

    if (isOverturn) {
      isTop = poi.includes('b');
      isBottom = poi.includes('t');
      isLeft = poi.includes('r');
      isRight = poi.includes('l');
    }

    const isVerticalCenter = isCenterPoi && (isTop || isBottom);
    const isHorizontalCenter = isCenterPoi && (isLeft || isRight);

    if (!isCenterPoi || isLeft || isRight) {
      // 缩放选择框
      if (isLeft) {
        dragInfo.width -= dx;
        dragInfo.x += dx;
      } else if (!isVerticalCenter) {
        dragInfo.width += dx;
      }
    }
    if (!isCenterPoi || isTop || isBottom) {
      if (isTop) {
        dragInfo.height -= dy;
        dragInfo.y += dy;
      } else if (!isHorizontalCenter) {
        dragInfo.height += dy;
      }
    }

    imageInfo.dragStart = { x: mouseX, y: mouseY };
    this.draw();
  }
  // 移动选择框
  moveSelectionBox(mouseX, mouseY) {
    const { dragInfo, canvas, imageInfo } = this;
    const { dragStart } = imageInfo;
    const { width, height } = dragInfo;
    const mx = mouseX - dragStart.x;
    const my = mouseY - dragStart.y;
    const xMin = (-1 * width) / 2;
    const yMin = (-1 * height) / 2;
    dragInfo.x = mx < xMin ? Math.max(mx, xMin) : Math.min(mx, canvas.originalWidth - width / 2);
    dragInfo.y = my < yMin ? Math.max(my, yMin) : Math.min(my, canvas.originalHeight - height / 2);
    this.draw();
  }
  // 旋转选择框
  rotateSelectionBox(mouseX, mouseY) {
    const { dragInfo } = this;
    const { x: dx, y: dy, width: dw, height: dh } = dragInfo;
    // 获取旋转角度
    const angle = Math.atan2(mouseY - (dy + dh / 2), mouseX - (dx + dw / 2));
    dragInfo.angle = adjustedAngleInCircle(angle); // 旋转角度
    this.draw();
  }
  // 获取旋转后的旋转框尺寸
  getSelectionBoxSize(dragInfo) {
    const { x = 0, y = 0, width, height, angle = 0 } = dragInfo;

    // 计算旋转后的宽度和高度
    const rotatedWidth = Math.abs(width * Math.cos(angle)) + Math.abs(height * Math.sin(angle));
    const rotatedHeight = Math.abs(width * Math.sin(angle)) + Math.abs(height * Math.cos(angle));

    // 计算旋转后的左上角坐标
    const rotatedX = x - (rotatedWidth - width) / 2;
    const rotatedY = y - (rotatedHeight - height) / 2;
    return {
      rotatedWidth,
      rotatedHeight,
      rotatedX,
      rotatedY,
    };
  }
  // 画布旋转
  rotateCanvas(angle, dragInfo, ctx = this.ctx) {
    const { x = 0, y = 0, width, height } = dragInfo;
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    ctx.translate(centerX, centerY);
    ctx.rotate(angle);
    ctx.translate(-1 * centerX, -1 * centerY);
  }
  // 提示信息
  createTips(tips) {
    const { maxSelectionBox } = this;
    return `最多只可添加${maxSelectionBox}个识别框，${tips}`;
  }
  // 检查是否超过限制
  checkMaximize() {
    const { maxSelectionBox, selectionBoxes = [] } = this;
    if (selectionBoxes.length >= maxSelectionBox) {
      Taro.kbToast({
        text: this.createTips('请长按删除后再试！'),
      });
      return true;
    }
    return false;
  }
  // 弹窗提示
  async modalTips() {
    if (this.modalOpened) {
      return;
    }
    const storageKey = 'cutting-modal';
    try {
      const res = await getStorage({ key: storageKey });
      if (res && res.data) {
        return;
      }
    } catch (error) {}

    Taro.kbModal({
      content: this.createTips('支持长按删除！'),
      confirmText: '知道了',
      cancelText: '',
    });
    setStorage({
      key: storageKey,
      data: 1,
    });
    this.modalOpened = true;
  }
  // 补充id
  patchSelectionBoxItem(item) {
    const box = {
      x: 0, // 选框默认x
      y: 0, // 选框默认y
      width: 200, // 选框默认width
      height: 100, // 选框默认height
      isDragging: false, // 拖动选框
      isResizing: false, // 缩放
      isRotating: false, // 旋转
      handle: null, // 操作点
      textAngle: 0, // 框选区域文案旋转角度
      angle: 0, // 旋转角度
    };
    return {
      id: randomCode(),
      ...box,
      ...(item || this.getBoxCenterPosition(box)),
    };
  }
  /**
   *
   * @param {*} boxes 添加选择框 box {x, y, width, height}[];
   * @param {*} draw 是否绘制
   */
  addSelectionBox(boxes, draw = true) {
    if (this.checkMaximize()) {
      // 超过最大限制，不允许操作
      return;
    }
    let addBoxes = [];
    const { selectionBoxes = [] } = this;
    if (isArray(boxes) && boxes.length > 0) {
      addBoxes = boxes;
    } else {
      // 过滤出ocr缓存中已经被删除的；
      const canUseOCRBox = this.OCRBoxes.filter(
        (item) => !selectionBoxes.some((iitem) => iitem.id === item.id),
      );
      const addItem = canUseOCRBox[0] || this.patchSelectionBoxItem();
      addBoxes = [addItem];
    }

    // 批量加入
    addBoxes.forEach((item) => {
      selectionBoxes.unshift(item);
    });
    // 取最后一个
    this.selectionBoxes = selectionBoxes;
    // 非内部添加识别框，更新lockNumber
    if (!this.internalAddition) {
      this.lockNumber = this.selectionBoxes.length;
    }
    this.dragInfo = selectionBoxes[0];
    if (draw) {
      // 触发绘制
      this.draw();
      if (!this.internalAddition) {
        this.modalTips();
      }
    }
  }
  /**
   *
   * @description 激活选择框
   */
  activeSelectionBox(mouseX, mouseY) {
    const { selectionBoxes } = this;
    const findItemResize = (item) => this.isResizeHandle(mouseX, mouseY, item);
    const findItemRotate = (item) => this.isRotateHandle(mouseX, mouseY, item);
    const findItemMove = (item) => this.isMoveHandle(mouseX, mouseY, item);
    const activeBox =
      selectionBoxes.find(findItemResize) ||
      selectionBoxes.find(findItemRotate) ||
      selectionBoxes.find(findItemMove);
    if (activeBox) {
      this.dragInfo = activeBox;
    }
  }
  // 删除当前选择框
  delSelectionBox(dragInfo = this.dragInfo) {
    const { selectionBoxes } = this;
    if (selectionBoxes.length === 1) {
      // 仅有一条
      return;
    }
    const index = selectionBoxes.findIndex((item) => item.id === dragInfo.id);
    if (index >= 0) {
      // 移除
      this.clean(index);
    }
  }
  /**
   *
   * @description 长按删除
   */
  longPressToDel(dragInfo) {
    clearTimeout(this.delTimeDelay);
    const { x, y } = dragInfo;
    const maxM = 5;
    this.delTimeDelay = setTimeout(() => {
      const { x: mx, y: my } = dragInfo;
      if (
        Math.abs(mx - x) <= maxM &&
        Math.abs(my - y) <= maxM &&
        dragInfo.isDragging &&
        !dragInfo.isDraggingImage
      ) {
        this.delSelectionBox(dragInfo);
      }
    }, 500);
  }
  // 缩放图片
  resizeImage(touches) {
    const { imageInfo } = this;
    const { initScale, lastAngle, startAngle, scaleWidth, scaleHeight, width, height } = imageInfo;
    const currentDistance = this.getDistance(touches);
    imageInfo.pinchCenter = this.getPinchCenter(touches);
    const angle = this.getAngle(touches) - startAngle;
    imageInfo.angle = adjustedAngleInCircle(lastAngle + angle);

    // 缩放
    const scaling = imageInfo.scale * (currentDistance / imageInfo.distance);
    // 小于初始缩放当0.5或大于初始缩放当2倍，停止缩放；
    if (!(scaling < initScale * 0.5 || scaling > initScale * 2)) {
      imageInfo.scaling = scaling;
      imageInfo.scaleWidth = width * scaling;
      imageInfo.scaleHeight = height * scaling;
      // 图片位置，实现在对应位置缩放
      imageInfo.x += (scaleWidth - imageInfo.scaleWidth) / 2;
      imageInfo.y += (scaleHeight - imageInfo.scaleHeight) / 2;
    }
    this.draw();
  }
  // 移动图片
  moveImage(mouseX, mouseY) {
    const { canvas, imageInfo } = this;
    const { dragStart, scaleWidth, scaleHeight } = imageInfo;
    const mx = mouseX - dragStart.x;
    const my = mouseY - dragStart.y;
    imageInfo.x =
      mx < 0
        ? Math.max(mx, (-2 * scaleWidth) / 3)
        : Math.min(mx, canvas.originalWidth - scaleWidth / 3);
    imageInfo.y =
      my < 0
        ? Math.max(my, (-2 * scaleHeight) / 3)
        : Math.min(my, canvas.originalHeight - scaleHeight / 3);
    this.draw();
  }
  /**
   *
   * @description 图片写入本地
   * @param {*} base64
   * @param {*} size
   */
  writeFileToLocal(base64, size) {
    return new Promise((resolve) => {
      const matches = base64.match(/image\/([a-zA-Z]+);base64/) || [];
      const imageSuffix = matches[1] || 'png';
      const imageType = `image/${imageSuffix}`;
      const file = Taro.getFileSystemManager();
      const filePath = Taro.env.USER_DATA_PATH + `/${randomCode()}.${imageSuffix}`;
      file.writeFile({
        filePath,
        data: base64.replace(`data:${imageType};base64,`, ''),
        encoding: 'base64',
        success: () => {
          resolve({
            filePath,
            ...size,
          });
        },
        fail: () => {
          resolve({
            filePath: base64,
            isBase64: true,
            ...size,
          });
        },
      });
    });
  }
  // 图片上传并识别
  async ocrImage(files) {
    return await triggerOcrImage(files);
  }
  // 切割图片
  async cutImage() {
    const { selectionBoxes, pixelRatio: dpr, imageInfo } = this;
    // 图片翻转
    const res = await Promise.all(
      selectionBoxes.map(async (item) => {
        const { textAngle = 0 } = item;
        const boxRestAngle = item.angle - textAngle; // 识别框剩余角度
        const {
          rotatedWidth: width,
          rotatedHeight: height,
          rotatedX: x,
          rotatedY: y,
        } = this.getSelectionBoxSize(item);
        const rotateItem = {
          ...item,
          x: item.x - x,
          y: item.y - y,
        };
        const rotateImage = {
          ...imageInfo,
          x: imageInfo.x - x,
          y: imageInfo.y - y,
        };

        const itemWidthScale = width * dpr;
        const itemHeightScale = height * dpr;
        const { ctx: offscreenCanvasCtx, canvas: offscreenCanvas } = this.createOffscreenCanvas({
          width: itemWidthScale,
          height: itemHeightScale,
        });
        offscreenCanvasCtx.scale(dpr, dpr);

        // 截取旋转图片
        this.drawSelectionImage(rotateItem, offscreenCanvasCtx, rotateImage);
        const tmpUrl = offscreenCanvas.toDataURL();
        const [image] = await this.loadImage([tmpUrl]);

        // 回正底图
        const { rotatedWidth: width2, rotatedHeight: height2 } = this.getSelectionBoxSize({
          ...rotateItem,
          angle: boxRestAngle,
        });
        const itemWidthScale2 = width2 * dpr;
        const itemHeightScale2 = height2 * dpr;
        const { ctx: offscreenCanvasCtx2, canvas: offscreenCanvas2 } = this.createOffscreenCanvas({
          width: itemWidthScale2,
          height: itemHeightScale2,
        });
        offscreenCanvasCtx2.scale(dpr, dpr);
        // 补充白色背景
        offscreenCanvasCtx2.fillStyle = 'white';
        offscreenCanvasCtx2.fillRect(0, 0, itemWidthScale2, itemHeightScale2);
        this.drawImage(
          offscreenCanvasCtx2,
          {
            x: (width2 - width) / 2,
            y: (height2 - height) / 2,
            scaleWidth: width,
            scaleHeight: height,
            angle: -1 * textAngle,
          },
          image,
        );

        // 导出图片
        const url = offscreenCanvas2.toDataURL();

        // 调试
        // Taro.setClipboardData({ data: url });
        // return null;

        // 写入本地文件
        const localUrls = await this.writeFileToLocal(url, {
          width: width2,
          height: height2,
        });
        return localUrls;
      }),
    );
    return res;
  }
  // 旋转图片
  async rotateImage() {
    const { image, imageInfo } = this;
    if (!image) return;
    this.initImageInfo(); // 恢复图片居中，缩放初始化
    imageInfo.isRotatingImage = true;
    imageInfo.angle += transDegToAngle(90); // 顺时针每次旋转90度
    imageInfo.angle = adjustedAngleInCircle(imageInfo.angle);
    this.draw();

    await this.runOCRDebounce();
    imageInfo.isRotatingImage = false;
  }
  // 防抖
  async runOCRDebounce() {
    clearTimeout(this.delayRunOCR);
    await new Promise((resolve) => {
      this.delayRunOCR = setTimeout(resolve, 300);
    });
    return await this.runOCR();
  }
  // 检查是否再次识别
  // this.imageInfoPrevious 根据上次缓存的图片信息
  checkCanOCR() {
    const { imageInfoPrevious, imageInfo } = this;
    const { isUpdateImage, isResizingImage, isDraggingImage, isRotatingImage } = imageInfo;
    if (!imageInfoPrevious) return true;
    if (isUpdateImage) return true;
    // 阈值检查
    const thresholdCheck = (key) => {
      const thresholdMap = {
        scale: 0.1, // 缩放超过0.1
        x: 10, // 移动>10
        y: 10, // 移动>10
        angle: transDegToAngle(5), // 旋转超过5度
      };
      const threshold = thresholdMap[key] || 0;
      return Math.abs(imageInfo[key] - imageInfoPrevious[key]) > threshold;
    };
    if (isResizingImage && thresholdCheck('scale')) return true;
    if ((isDraggingImage && thresholdCheck('x')) || thresholdCheck('y')) return true;
    if (isRotatingImage && thresholdCheck('angle')) return true;
    return false;
  }
  // 微信ocr识别
  async runOCR() {
    const { imageInfo, canvas, image, maxSelectionBox } = this;
    if (!image) return;
    const { isUpdateImage, isResizingImage, isDraggingImage, isRotatingImage } = imageInfo;
    if (isUpdateImage || isResizingImage || isDraggingImage || isRotatingImage) {
      // 检查是否可以触发识别
      // 并缓存上次触发识别时的图片信息
      const can = this.checkCanOCR();
      this.imageInfoPrevious = { ...imageInfo };
      if (!can) return;

      // 初始更新、图片缩放、图片移动、图片旋转触发ocr识别
      const { originalWidth, originalHeight } = canvas;
      const { ctx: offscreenCanvasCtx } = this.createOffscreenCanvas({
        width: originalWidth,
        height: originalHeight,
      });
      offscreenCanvasCtx.clearRect(0, 0, originalWidth, originalHeight);
      this.drawImage(offscreenCanvasCtx);
      // 获取画完后的数据
      const imgData = offscreenCanvasCtx.getImageData(0, 0, originalWidth, originalHeight);
      const { list } = await this.ocr.runOCR(imgData, imageInfo);
      const space = 5; // 留边
      const boxes = list.map((item) => {
        const x = item.origin.x - space;
        const y = item.origin.y - space;
        const { width, height } = item.size;

        return this.patchSelectionBoxItem({
          textAngle: item.angle,
          angle: item.angle,
          x,
          y,
          width: width + space * 2,
          height: height + space * 2,
        });
      });
      this.internalAddition = true; // 标识内部识别，自动添加识别框
      const isEmpty = this.selectionBoxes.length === 0;
      this.OCRBoxes = boxes; // 缓存当前boxes，点击添加时，从里面取数据
      const patchBox = this.lockNumber > 0 ? boxes.slice(0, this.lockNumber) : boxes;
      if (patchBox.length > 0) {
        this.selectionBoxes = [];
        this.addSelectionBox(maxSelectionBox === 1 ? patchBox.slice(0, 1) : patchBox); // 添加选择框
      } else if (isUpdateImage || isEmpty) {
        this.selectionBoxes = [];
        this.addSelectionBox(); // 添加选择框
      }
      this.runCallback(this.OCRCallback, this.selectionBoxes, patchBox);
      this.internalAddition = false;
    }
  }
  // 计算图片居中位置
  getImageCenterPosition() {
    const { scaleWidth, scaleHeight } = this.imageInfo;
    return {
      x: (this.canvas.originalWidth - scaleWidth) / 2,
      y: (this.canvas.originalHeight - scaleHeight) / 2,
    };
  }
  // 获取矩形框中心点
  getReactCenter({ x = 0, y = 0, width, height }) {
    return {
      x: x + width / 2,
      y: y + height / 2,
    };
  }
  // 使用逆时针方向旋转矩阵将触摸点的坐标转换回未旋转状态
  getRotatedTouchXY(mouseX, mouseY, dragInfo) {
    const { x, y, width, height, angle } = dragInfo;
    // 矩形框的中心坐标
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const rotatedTouchX =
      Math.cos(-angle) * (mouseX - centerX) - Math.sin(-angle) * (mouseY - centerY) + centerX;
    const rotatedTouchY =
      Math.sin(-angle) * (mouseX - centerX) + Math.cos(-angle) * (mouseY - centerY) + centerY;
    return {
      rotatedTouchX,
      rotatedTouchY,
    };
  }
  // 选择框-缩放操作
  isResizeHandle(mouseX, mouseY, dragInfo = this.dragInfo) {
    const { rotatedTouchX, rotatedTouchY } = this.getRotatedTouchXY(mouseX, mouseY, dragInfo);
    const handles = this.getResizeHandles(dragInfo);
    const boundarySize = 20;
    return handles.some((handle) => {
      const isHandle =
        Math.abs(rotatedTouchX - handle.x) < boundarySize &&
        Math.abs(rotatedTouchY - handle.y) < boundarySize;
      dragInfo.handle = isHandle ? handle : null;
      return isHandle;
    });
  }
  // 选择框-移动操作
  isMoveHandle(mouseX, mouseY, dragInfo = this.dragInfo) {
    const { x, y, width, height } = dragInfo;
    const { rotatedTouchX, rotatedTouchY } = this.getRotatedTouchXY(mouseX, mouseY, dragInfo);

    return (
      rotatedTouchX >= x &&
      rotatedTouchX <= x + width &&
      rotatedTouchY >= y &&
      rotatedTouchY <= y + height
    );
  }
  // 选择框-旋转操作，也可能是删除按钮
  isRotateHandle(mouseX, mouseY, dragInfo = this.dragInfo) {
    const { x, y, width, height, poi } = this.getRotateHandle(dragInfo);
    const { rotatedTouchX, rotatedTouchY } = this.getRotatedTouchXY(mouseX, mouseY, dragInfo);
    return rotatedTouchX >= x &&
      rotatedTouchX <= x + width &&
      rotatedTouchY >= y &&
      rotatedTouchY <= y + height
      ? poi
      : false;
  }
  // 清除识别框
  // index >=0 则清除对应数据，否则保留第一条，其余清除
  clean(index = -1) {
    const { selectionBoxes } = this;
    let text = '';
    if (index >= 0) {
      selectionBoxes.splice(index, 1);
      text = '已删除';
    } else {
      this.selectionBoxes = selectionBoxes.slice(-1); // 往前插入，最后一个是识别出的数据中的第一个
      text = '已清除多余识别框';
    }
    if (text) {
      Taro.kbToast({
        text,
      });
    }
    this.runCallback(this.cleanCallback, this.selectionBoxes.length);
    this.draw();
    this.lockNumber = this.selectionBoxes.length;
  }
  // 绑定事件
  handleEvents(e) {
    const type = e.type.toLocaleLowerCase();
    if (!this.image) return;
    switch (type) {
      case 'touchstart':
        this.onMouseDown(e);
        break;
      case 'touchmove':
        this.onMouseMove(e);
        break;
      case 'touchend':
        this.onMouseUp(e);
        break;
      default:
        break;
    }
  }
  // 格式化事件event，与h5保持一致
  formatEvent(e) {
    let touches = e.touches || [];
    if (touches.filter(({ x, clientX = x }) => clientX >= 0).length === 0) {
      touches = e.changedTouches || [];
    }
    const [{ x, clientX = x, y, clientY = y }] = touches;
    e.clientX = clientX;
    e.clientY = clientY;
    e.touches = touches.map(({ x, clientX = x, y, clientY = y }) => ({
      clientX,
      clientY,
    }));
    return e;
  }
  // 两指角度
  getAngle(touches) {
    const [touch1, touch2] = touches;
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    const angle = Math.atan2(dy, dx);
    return angle;
  }
  // 两指距离
  getDistance(touches) {
    const [touch1, touch2] = touches;
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }
  // 两指中心点
  getPinchCenter(touches) {
    const [touch1, touch2] = touches;
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
    };
  }
  getFormatEvent(ev) {
    const { canvas } = this;
    const e = this.formatEvent(ev);
    const { clientX, clientY } = e;
    const mouseX = clientX - canvas.offsetLeft;
    const mouseY = clientY - canvas.offsetTop;
    return { mouseX, mouseY, e };
  }
  // 事件
  onMouseDown(ev) {
    const { imageInfo } = this;
    const { mouseX, mouseY, e } = this.getFormatEvent(ev);
    const { touches } = e;
    this.activeSelectionBox(mouseX, mouseY); // 激活选择框
    const { dragInfo } = this; // 激活后更新dragInfo
    const rotateHandlePoi = this.isRotateHandle(mouseX, mouseY);
    if (touches.length === 2) {
      // 双指操作
      imageInfo.isResizingImage = true;
      imageInfo.distance = this.getDistance(touches);
      imageInfo.pinchCenter = this.getPinchCenter(touches);
      imageInfo.scale = imageInfo.scaling || imageInfo.scale;
      imageInfo.startAngle = this.getAngle(touches);
      imageInfo.lastAngle = imageInfo.angle;
    } else if (rotateHandlePoi) {
      // 旋转选择框
      dragInfo.isRotating = rotateHandlePoi === 'rotate';
    } else if (this.isResizeHandle(mouseX, mouseY)) {
      // 缩放选择框
      dragInfo.isResizing = true;
      imageInfo.dragStart = { x: mouseX, y: mouseY };
    } else if (this.isMoveHandle(mouseX, mouseY)) {
      // 拖拽选择框
      dragInfo.isDragging = true;
      imageInfo.dragStart = { x: mouseX - dragInfo.x, y: mouseY - dragInfo.y };
      this.longPressToDel(dragInfo); // 模拟长按删除
    } else {
      // 移动图片
      imageInfo.isDraggingImage = true;
      imageInfo.dragStart = { x: mouseX - imageInfo.x, y: mouseY - imageInfo.y };
    }
  }
  onMouseUp(ev) {
    const { mouseX, mouseY } = this.getFormatEvent(ev);
    const { dragInfo, imageInfo } = this;

    // 触发ocr识别
    this.runOCR();

    dragInfo.isDragging = false;
    dragInfo.isResizing = false;
    dragInfo.isRotating = false;
    imageInfo.isDraggingImage = false;
    imageInfo.isResizingImage = false;

    if (this.isRotateHandle(mouseX, mouseY) === 'del') {
      // 删除
      this.delSelectionBox(dragInfo);
    }

    // 支持缩放到翻转，修复位置与尺寸；
    if (dragInfo.width < 0) {
      dragInfo.x = dragInfo.width + dragInfo.x;
      dragInfo.width = Math.abs(dragInfo.width);
    }
    if (dragInfo.height < 0) {
      dragInfo.y = dragInfo.height + dragInfo.y;
      dragInfo.height = Math.abs(dragInfo.height);
    }
  }
  onMouseMove(ev) {
    const e = this.formatEvent(ev);
    const { canvas, dragInfo, imageInfo } = this;
    const mouseX = e.clientX - canvas.offsetLeft;
    const mouseY = e.clientY - canvas.offsetTop;
    if (imageInfo.isResizingImage && e.touches.length === 2) {
      this.resizeImage(e.touches, mouseX, mouseY);
    } else if (dragInfo.isRotating) {
      this.rotateSelectionBox(mouseX, mouseY);
    } else if (dragInfo.isResizing) {
      this.resizeSelectionBox(mouseX, mouseY);
    } else if (dragInfo.isDragging) {
      this.moveSelectionBox(mouseX, mouseY);
    } else if (imageInfo.isDraggingImage) {
      this.moveImage(mouseX, mouseY);
    }
  }
  // 创建离屏canvas
  createOffscreenCanvas(opts) {
    const { type = '2d' } = opts || {};
    const canvas = Taro.createOffscreenCanvas({ type, ...opts });
    const ctx = canvas.getContext(type);
    return {
      canvas,
      ctx,
    };
  }
}
