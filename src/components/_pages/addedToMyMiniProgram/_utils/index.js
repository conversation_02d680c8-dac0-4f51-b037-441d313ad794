import Taro, { useEffect, useRef } from '@tarojs/taro';

export function useCheckIsAddedToMyMiniProgram() {
  const actionRef = useRef({ stop: false });

  //  检查
  function check() {
    return new Promise((resolve) => {
      Taro.checkIsAddedToMyMiniProgram({
        success: ({ added }) => {
          resolve({ added });
        },
      });
    });
  }

  // 循环检查，是否添加
  function loopCheckIsAddedToMyMiniProgram() {
    return new Promise((resolve) => {
      if (actionRef.current.stop) {
        resolve({ added: false });
        return;
      }
      check().then((res) => {
        if (!res.added) {
          // 循环检查，是否添加
          actionRef.current.time = setTimeout(() => {
            loopCheckIsAddedToMyMiniProgram().then(resolve);
          }, 1000);
        } else {
          resolve(res);
        }
      });
    });
  }

  // 停止监听
  function stop() {
    actionRef.current.stop = true;
    clearTimeout(actionRef.current.time);
  }

  // 开始
  function start() {
    actionRef.current.stop = false;
    return loopCheckIsAddedToMyMiniProgram();
  }

  // 终止
  useEffect(() => {
    return () => {
      stop();
    };
  }, []);

  return {
    start,
    stop,
    check,
  };
}
