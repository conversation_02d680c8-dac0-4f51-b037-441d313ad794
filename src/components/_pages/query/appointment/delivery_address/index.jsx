/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Picker, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtButton, AtIcon, AtInput } from 'taro-ui';

import './index.scss';
import { useDeliveryAddress } from './useDeliveryAddress';

const DeliveryAddress = (props) => {
  const {
    firstColumns,
    secondColumns,
    pageData,
    pickerVal,
    inputChange,
    onSubmit,
    onColumnChange,
    onPickerChange,
  } = useDeliveryAddress(props);

  return (
    <View className='d_Address'>
      <View className='d_Address__form'>
        <AtInput
          placeholder='请输入姓名'
          value={pageData.receiver_name}
          onChange={inputChange.bind(null, 'receiver_name')}
        />
        <AtInput
          placeholder='请输入手机号'
          value={pageData.receiver_phone}
          onChange={inputChange.bind(null, 'receiver_phone')}
        />
        <Picker
          mode='multiSelector'
          range={[firstColumns, secondColumns]}
          value={pickerVal}
          onChange={onPickerChange}
          onColumnChange={onColumnChange}
        >
          <AtInput
            placeholder='请选择收货地址'
            editable={false}
            value={
              pageData.first_level_address
                ? `${pageData.first_level_address} ${pageData.second_level_address}`
                : ''
            }
          >
            <AtIcon prefixClass='kb-icon' value='arrow' className='kb-size__base2 kb-color__grey' />
          </AtInput>
        </Picker>

        <AtInput
          placeholder='详细地址（如X栋X室）'
          value={pageData.receiver_address}
          onChange={inputChange.bind(null, 'receiver_address')}
        />
      </View>
      <AtButton type='primary' circle onClick={onSubmit}>
        确定
      </AtButton>
    </View>
  );
};

DeliveryAddress.options = {
  addGlobalClass: true,
};

export default DeliveryAddress;
