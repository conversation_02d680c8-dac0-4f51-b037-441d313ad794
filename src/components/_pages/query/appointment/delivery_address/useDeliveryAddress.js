/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useMemo, useEffect } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';

export function useDeliveryAddress(props) {
  const { addressConfig = [], addressInfo = {} } = props;

  const { loginData } = useSelector((state) => state.global);

  const { userInfo: { mobile } = {} } = loginData || {};

  const [pageData, setPageData] = useState({
    receiver_name: '',
    receiver_phone: '',
    receiver_address: '',
    first_level_address: '',
    first_level_address_id: '',
    second_level_address: '',
    second_level_address_id: '',
  });

  const [pickerVal, setPickerVal] = useState([0, 0]);

  const firstCol = useMemo(
    () =>
      addressConfig.map((i) => ({
        label: i.addr,
        value: i.id,
      })),
    [addressConfig],
  );
  const secondCol = useMemo(() => {
    let arr = [];
    if (addressConfig[pickerVal[0]] && addressConfig[pickerVal[0]].child_arr) {
      arr = addressConfig[pickerVal[0]].child_arr.map((i) => ({
        label: i.addr,
        value: i.id,
      }));
    }
    return arr;
  }, [pickerVal, addressConfig]);

  const onColumnChange = (event) => {
    const { column, value } = event.detail || {};
    if (column === 0) {
      setPickerVal([value, 0]);
    }
  };

  const onPickerChange = (event) => {
    const { value } = event.detail || {};
    const [f, s] = value;
    setPickerVal(value);
    const f_id = firstCol[f].value;
    const f_name = firstCol[f].label;
    const s_id = secondCol[s] ? secondCol[s].value : '';
    const s_name = secondCol[s] ? secondCol[s].label : '';
    setPageData((val) => ({
      ...val,
      first_level_address: f_name,
      first_level_address_id: f_id,
      second_level_address: s_name,
      second_level_address_id: s_id,
    }));
  };

  const inputChange = (name, value) => {
    setPageData((val) => ({
      ...val,
      [name]: value,
    }));
  };

  const checkedAddress = () => {
    return new Promise((resolve, reject) => {
      const { receiver_name, receiver_phone, receiver_address, first_level_address } = pageData;
      let msg = '';
      if (!receiver_name) {
        msg = '请输入姓名';
      }
      if (!receiver_phone && !msg) {
        msg = '请输入手机号';
      }
      if (!receiver_address && !msg) {
        msg = '请输入详细地址';
      }
      if (!first_level_address && !msg) {
        msg = '请选择代取点';
      }
      if (msg) {
        Taro.kbToast({
          text: msg,
        });
        reject();
        return;
      }
      resolve();
    });
  };

  const onSubmit = async () => {
    await checkedAddress();
    Taro.navigator({
      post: {
        type: 'deliveryAddress',
        data: { ...pageData },
      },
    });
  };

  // 设置默认值
  useEffect(() => {
    if (addressInfo.receiver_name) {
      setPageData((val) => ({
        ...val,
        ...addressInfo,
      }));
    } else {
      setPageData((val) => ({
        ...val,
        receiver_phone: mobile,
      }));
    }
  }, [addressInfo.receiver_name, mobile]);

  return {
    firstColumns: firstCol.map((i) => i.label),
    secondColumns: secondCol.map((i) => i.label),
    pickerVal,
    pageData,
    inputChange,
    onSubmit,
    onColumnChange,
    onPickerChange,
  };
}
