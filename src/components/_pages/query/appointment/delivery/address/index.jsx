/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro, { useEffect } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import { getStorage } from '~base/utils/utils';
import { DELIVERY_ORDER_ADDRESS_KEY } from '../footer/useAppointmentDeliveryFooter';

import './index.scss';

const AppointmentDeliveryAddress = (props) => {
  const { addressInfo = {}, addressConfig = [], updatePageData } = props;

  const {
    receiver_name,
    receiver_phone,
    receiver_address,
    first_level_address,
    second_level_address,
  } = addressInfo || {};

  const navigatorAddress = () => {
    if (!addressConfig.length) {
      Taro.kbToast({
        text: '未配置站点信息',
      });
      return;
    }
    Taro.navigator({
      url: 'query/appointment/delivery-address',
      options: {
        postData: JSON.stringify({
          addressInfo,
          addressConfig,
        }),
      },
    });
  };

  useEffect(() => {
    getStorage({ key: DELIVERY_ORDER_ADDRESS_KEY }).then((result) => {
      const { data = {} } = result.data;
      if (data.receiver_name) {
        updatePageData({
          addressInfo: data,
        });
      }
    });
  }, []);

  return (
    <View className='app_del_Address' hoverClass='kb-hover' onClick={navigatorAddress}>
      <AtIcon prefixClass='kb-icon' value='location' className='kb-size__xl kb-color__brand' />
      {receiver_name ? (
        <View className='app_del_Address__item'>
          <View className='app_del_Address__item__address'>
            {first_level_address}-{second_level_address}-{receiver_address}
          </View>
          <View className='app_del_Address__item__name'>
            {receiver_name} {receiver_phone}
          </View>
        </View>
      ) : (
        <View className='kb-size__lg kb-color__grey kb-text__left' style={{ flex: '1' }}>
          请填写收货地址
        </View>
      )}
      <AtIcon prefixClass='kb-icon' value='arrow' className='kb-size__lg kb-color__grey' />
    </View>
  );
};

AppointmentDeliveryAddress.options = {
  addGlobalClass: true,
};

export default AppointmentDeliveryAddress;
