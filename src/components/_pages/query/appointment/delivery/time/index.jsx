/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import KbPicker from '@base/components/picker';
import { useAppointmentDeliveryTime } from './useAppointmentDeliveryTime';
import { dateCalendar } from '~base/utils/utils';

import './index.scss';

const AppointmentDeliveryTime = (props) => {
  const { reserve_time_list = [], reserve_time } = props;
  const { pickRef, openPicker, onConfirm } = useAppointmentDeliveryTime(props);
  return (
    <View>
      <View className='app_del_time' hoverClass='kb-hover' onClick={openPicker}>
        <View className='app_del_time__label'>送达时间</View>
        <View className='app_del_time__val'>
          {reserve_time ? `${dateCalendar(reserve_time, { timer: true })} 送达` : '暂无预约时间'}
        </View>
        <AtIcon prefixClass='kb-icon' value='arrow' className='kb-size__lg kb-color__grey' />
      </View>
      {reserve_time_list.length && (
        <KbPicker
          mode='selector'
          options={reserve_time_list}
          onRef={(ref) => (pickRef.current = ref)}
          onConfirm={onConfirm}
        >
          选择时间
        </KbPicker>
      )}
    </View>
  );
};

AppointmentDeliveryTime.options = {
  addGlobalClass: true,
};

export default AppointmentDeliveryTime;
