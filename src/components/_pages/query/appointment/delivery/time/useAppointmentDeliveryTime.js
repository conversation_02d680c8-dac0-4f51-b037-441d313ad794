/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useRef } from '@tarojs/taro';
import request from '@base/utils/request';
import dayjs from 'dayjs';

export function useAppointmentDeliveryTime(props) {
  const { dak_id, reserve_time_list, updatePageData } = props;
  const pickRef = useRef({});

  // 按照配置信息映射两天可选时间段。
  const formatTimeColumns = ({ timeList, deadline }) => {
    const arr = [];
    const days = [
      {
        label: '今天',
        date: dayjs().format('YYYY-MM-DD'),
      },
      {
        label: '明天',
        date: dayjs().add(1, 'day').format('YYYY-MM-DD'),
      },
    ];
    const deadlineTime = dayjs().add(deadline, 'hour');
    days.forEach((days) => {
      timeList.forEach((timers) => {
        const label = `${days.label} ${timers.time}`;
        const value = dayjs(`${days.date} ${timers.time}`).format('YYYY-MM-DD HH:mm:ss');
        if (dayjs(value).isAfter(dayjs(deadlineTime))) {
          arr.push({
            label,
            value,
          });
        }
      });
    });
    return arr;
  };

  // 获取驿站配置
  const getDakConfig = () => {
    request({
      url: '/api/weixin/mini/DakMini/HomeDeliverOrder/homeDeliverConfig',
      data: {
        dak_id: dak_id,
      },
      onThen: (res) => {
        const { code, data } = res;
        const {
          timeConf: { timeList = [], deadline = '0' } = {},
          addrConf: addressConfig,
          base_price,
        } = data || {};
        if (`${code}` === '0' && timeList.length) {
          const reserve_time_list = formatTimeColumns({ timeList, deadline });

          updatePageData({
            reserve_time_list: reserve_time_list,
            reserve_time: reserve_time_list[0].value,
            addressConfig,
            base_price,
          });
        }
      },
    });
  };

  const openPicker = () => {
    if (!reserve_time_list.length) {
      Taro.kbToast({
        text: '暂无可预约时间',
      });
      return;
    }
    pickRef.current.show();
  };

  const onConfirm = (val) => {
    const { value } = val.obj;
    updatePageData({
      reserve_time: value,
    });
  };

  useEffect(() => {
    if (dak_id) {
      getDakConfig();
    }
  }, [dak_id]);

  return {
    pickRef,
    openPicker,
    onConfirm,
  };
}
