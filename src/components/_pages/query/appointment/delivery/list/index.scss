/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.app_del_listItem {
  display: flex;
  gap: $spacing-v-md;
  align-items: center;
  justify-content: space-between;
  &__content {
    flex: 1;
    &__waybill {
      color: $color-grey-0;
      font-size: $font-size-base2;
    }
    &__desc {
      display: flex;
      gap: $spacing-v-md;
      align-items: center;
      justify-content: flex-start;
      color: $color-grey-2;
      font-size: $font-size-sm;
    }
  }
  &__pickCode {
    flex-shrink: 1;
    color: $color-brand;
    font-size: $font-size-lg;
  }
}

.app_del_listAdd {
  display: flex;
  gap: $spacing-v-md;
  align-items: center;
  justify-content: flex-start;
  color: $color-brand;
  font-size: $font-size-base2;
}
