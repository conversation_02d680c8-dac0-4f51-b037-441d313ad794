/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import KbCheckbox from '@base/components/checkbox';
import KbScrollView from '@base/components/scroll-view';
import KbBrand from '@/components/_pages/brand';
import { AtIcon } from 'taro-ui';

import './index.scss';
import { useAppointmentDeliveryList } from './useAppointmentDeliveryList';

const AppointmentDeliveryList = (props) => {
  const { packageList = [] } = props;

  const { onAddPackage, onHandleItem } = useAppointmentDeliveryList(props);

  return (
    <View className='kb-list  kb-margin-lg-lr'>
      <View className='kb-spacing-sm kb-spacing-md-l kb-background__white kb-color__black'>
        选择代取包裹
      </View>
      <View className='kb-list__content'>
        <KbScrollView scrollY className='kb-scrollview' fixIosFooter={false}>
          {!!packageList.length &&
            packageList.map((item) => (
              <View
                key={item.waybill_no}
                className='kb-list__sign app_del_listItem'
                onClick={onHandleItem.bind(null, item.waybill_no)}
              >
                <KbCheckbox
                  checked={item.isChecked}
                  onChange={onHandleItem.bind(null, item.waybill_no)}
                />
                <KbBrand size='sm' brand={item.brand} />
                <View className='app_del_listItem__content'>
                  <View className='app_del_listItem__content__waybill'>{item.waybill_no}</View>
                  <View className='app_del_listItem__content__desc'>
                    <View>{item.express_phone}</View>
                    <View>{item.name}</View>
                    {item.shelve_type_desc && item.shelve_type_desc != '小件' && (
                      <View>{item.shelve_type_desc || ''}</View>
                    )}
                  </View>
                </View>
                <View className='app_del_listItem__pickCode'>{item.pickup_code}</View>
              </View>
            ))}
          <View
            className='kb-list__sign app_del_listAdd'
            hoverClass='kb-hover'
            onClick={onAddPackage}
          >
            <AtIcon value='add-circle' className='kb-size__xl kb-color__brand' />
            <View>添加其他包裹</View>
          </View>
        </KbScrollView>
      </View>
    </View>
  );
};

AppointmentDeliveryList.options = {
  addGlobalClass: true,
};

export default AppointmentDeliveryList;
