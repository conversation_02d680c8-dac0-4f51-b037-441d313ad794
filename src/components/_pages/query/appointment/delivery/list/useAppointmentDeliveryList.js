/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect } from '@tarojs/taro';
import request from '@base/utils/request';

export function useAppointmentDeliveryList(props) {
  const { dak_id, updatePageData, packageList = [] } = props;

  // 获取列表
  const getPackageList = () => {
    request({
      url: '/api/weixin/mini/DakMini/Record/homeDeliveryWaitPickPacks',
      data: {
        cm_id: dak_id,
      },
      onThen: ({ code, data }) => {
        if (`${code}` === '0' && Array.isArray(data)) {
          updatePageData({
            packageList: data,
          });
        }
      },
    });
  };

  useEffect(() => {
    if (dak_id) {
      getPackageList();
    }
  }, [dak_id]);

  const onSearchPackage = (val) => {
    return new Promise((resolve) => {
      request({
        url: '/api/weixin/mini/DakMini/Record/matchingDakPickupRecord',
        data: {
          dakId: dak_id,
          search: val,
        },
        nonceKey: 'dakId',
        toastError: true,
        onThen: (res) => {
          const { code, data } = res;
          if (`${code}` === '0' && data.waybill_no) {
            const isIncludes = packageList.some((i) => i.waybill_no == data.waybill_no);
            if (isIncludes) {
              resolve(true);
              Taro.kbToast({
                text: '此包裹已存在，勿重复添加',
              });
              return;
            }
            resolve(false);
            Taro.kbToast({
              text: '添加成功',
            });
            updatePageData({
              packageList: [...packageList, data],
            });
          } else {
            Taro.kbToast({
              text: '未匹配到包裹',
            });
            resolve(true);
          }
        },
      });
    });
  };

  const onAddPackage = () => {
    Taro.kbModal({
      top: '添加包裹',
      template: [
        {
          tag: 'at-input',
          placeholder: '请输入取件码/运单号',
          value: '',
          circle: true,
          border: false,
          name: 'val',
          cursorSpacing: 200,
        },
      ],
      isPromiseResult: true,
      onConfirm: async (e) => {
        const {
          data: { val },
        } = e;
        if (val) {
          return onSearchPackage(val);
        } else {
          Taro.kbToast({
            text: '请输入取件码/运单号',
          });
          return true;
        }
        // return false;
      },
    });
  };

  const onHandleItem = (waybill_no) => {
    updatePageData({
      packageList: packageList.map((item) => ({
        ...item,
        isChecked: item.waybill_no == waybill_no ? !item.isChecked : item.isChecked,
      })),
    });
  };

  return {
    onAddPackage,
    onHandleItem,
  };
}
