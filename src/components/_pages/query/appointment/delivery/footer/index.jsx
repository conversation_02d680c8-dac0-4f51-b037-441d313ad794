/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import KbCheckbox from '@base/components/checkbox';
import KbButton from '~base/components/button';
import './index.scss';
import { useAppointmentDeliveryFooter } from './useAppointmentDeliveryFooter';

const AppointmentDeliveryFooter = (props) => {
  const { prePrice, base_price } = props;
  const { isCheckAll, checkedList, handleCheckAll, onSubmit } = useAppointmentDeliveryFooter(props);

  return (
    <View className='app_del_footer'>
      <KbCheckbox checked={isCheckAll} onChange={handleCheckAll}>
        <Text className='kb-spacing-sm-l'>全选</Text>
      </KbCheckbox>
      <View className='app_del_footer__content'>
        {!!checkedList.length ? (
          <View>
            {checkedList.length && <Text>已选{checkedList.length}件，</Text>}
            跑腿费
            <Text className='kb-color__red'> {prePrice || '--'} </Text>元
          </View>
        ) : (
          <View>
            跑腿费
            <Text className='kb-color__red'>{base_price || '--'} </Text>元/件起
          </View>
        )}
      </View>
      <KbButton
        size='small'
        type='primary'
        disabled={!checkedList.length}
        circle
        onClick={onSubmit}
      >
        立即支付
      </KbButton>
    </View>
  );
};

AppointmentDeliveryFooter.options = {
  addGlobalClass: true,
};

export default AppointmentDeliveryFooter;
