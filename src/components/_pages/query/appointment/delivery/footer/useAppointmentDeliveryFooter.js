/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo, useEffect } from '@tarojs/taro';
import isEmpty from 'lodash/isEmpty';
import request from '@base/utils/request';
import { deliveryOrderPay } from '~/components/_pages/order/detail-delivery/_utils/utils';
import { setStorage, sleep } from '~base/utils/utils';

export const DELIVERY_ORDER_ADDRESS_KEY = 'DELIVERY_ORDER_ADDRESS_KEY';

export function useAppointmentDeliveryFooter(props) {
  const { packageList = [], updatePageData, dak_id, addressInfo, reserve_time } = props;

  // 是否为全选状态
  const isCheckAll = useMemo(
    () => packageList.length && packageList.every((i) => i.isChecked),
    [packageList],
  );

  // 当前选择的列表
  const checkedList = useMemo(() => packageList.filter((i) => i.isChecked), [packageList]);

  const handleCheckAll = () => {
    updatePageData({
      packageList: packageList.map((i) => ({
        ...i,
        isChecked: !isCheckAll,
      })),
    });
  };

  const getPrePrice = () => {
    request({
      url: '/api/weixin/mini/DakMini/HomeDeliverOrder/homeDeliverOrderQuotation',
      data: {
        dak_id: dak_id,
        waybill_list: checkedList.map((item) => ({
          waybill_no: item.waybill_no,
          waybill_brand: item.brand,
          pickup_code: item.pickup_code,
          shelve_code: item.shelve_code || '',
        })),
      },
      nonceKey: 'dak_id',
      toastError: true,
      onThen: (res) => {
        const { code, data } = res;
        if (`${code}` === '0') {
          updatePageData({
            prePrice: (data.delivery_fee || 0) / 100,
          });
        }
      },
    });
  };

  useEffect(() => {
    if (checkedList.length) {
      getPrePrice();
    } else {
      updatePageData({
        prePrice: 0,
      });
    }
  }, [checkedList.length]);

  // 检查提交项目
  const onCheckInfo = () => {
    return new Promise((resolve, reject) => {
      let msg = '';
      if (isEmpty(addressInfo)) {
        msg = '请选择地址';
      }
      if (msg) {
        Taro.kbToast({
          text: msg,
        });
        reject();
        return;
      }
      resolve();
    });
  };

  // 提交方法
  const handleSubmit = () => {
    return new Promise((resolve) => {
      request({
        url: '/api/weixin/mini/DakMini/HomeDeliverOrder/createHomeDeliverOrder',
        data: {
          dak_id,
          ...addressInfo,
          reserve_arrival_time: reserve_time,
          waybill_list: JSON.stringify(
            checkedList.map((item) => ({
              waybill_no: item.waybill_no,
              waybill_brand: item.brand,
              pickup_code: item.pickup_code,
              shelve_code: item.shelve_code || '',
            })),
          ),
        },
        toastError: true,
        onThen: ({ code, data = {} }) => {
          if (`${code}` === '0' && data.order_id) {
            resolve(data.order_id);
          }
        },
      });
    });
  };

  const onSubmit = async () => {
    await onCheckInfo();
    const order_id = await handleSubmit();
    await deliveryOrderPay(order_id);
    setStorage({
      key: DELIVERY_ORDER_ADDRESS_KEY,
      data: addressInfo,
    });
    await sleep(1000);

    Taro.navigator({
      url: 'query/appointment/delivery-result',
    });

    // Taro.kbModal({
    //   top: false,
    //   title: '下单成功',
    //   centered: true,
    //   closable: false,
    //   cancelText: '继续取件',
    //   confirmText: '查看订单',
    //   onConfirm: () => {
    //     Taro.navigator({
    //       url: 'order/detail-delivery',
    //       target: 'self',
    //       options: {
    //         order_id,
    //       },
    //     });
    //   },
    //   onCancel: () => {
    //     Taro.navigator();
    //   },
    // });
  };

  return {
    checkedList,
    isCheckAll,
    handleCheckAll,
    onSubmit,
  };
}
