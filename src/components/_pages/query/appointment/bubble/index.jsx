/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { MovableArea, MovableView, View } from '@tarojs/components';
import Taro, { useEffect, useState } from '@tarojs/taro';
import './index.scss';

const Bubble = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [systemInfo, setSystemInfo] = useState(null);

  useEffect(() => {
    Taro.getSystemInfo({
      success: (res) => {
        setSystemInfo(res);
        setPosition({
          x: res.windowWidth,
          y: 30,
        });
      },
    });
  }, []);

  const navigator = () => {
    Taro.navigator({
      url: 'index',
      target: 'tab',
    });
  };

  const handleChange = (e) => {
    setPosition({
      x: e.detail.x,
      y: e.detail.y,
    });
  };

  if (!systemInfo) return null;

  return (
    <MovableArea
      className='kb-bubble-container'
      style={{
        height: `${systemInfo.windowHeight}px`,
        width: `${systemInfo.windowWidth}px`,
      }}
    >
      <MovableView
        className='kb-bubble'
        direction='vertical'
        x={position.x}
        y={position.y}
        onChange={handleChange}
        animation={false}
        outOfBounds={false}
        hoverClass='kb-hover-opacity'
        onClick={navigator}
      >
        <View>查看全部</View>
        <View>待取包裹</View>
      </MovableView>
    </MovableArea>
  );
};

Bubble.options = {
  addGlobalClass: true,
};

export default Bubble;
