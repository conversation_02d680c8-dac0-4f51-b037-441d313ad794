/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-bubble-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
}

.kb-bubble {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: unset;
  height: unset;
  padding: $spacing-h-sm $spacing-h-xl;
  color: $color-white;
  font-size: $font-size-sm;
  background-color: $color-brand;
  border-radius: $border-radius-arc;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
  view {
    text-align: center;
  }
}
