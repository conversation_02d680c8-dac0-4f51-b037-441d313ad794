/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbBrand from '@/components/_pages/brand';
import { setClipboardData } from '@/utils/qy';
import { refreshControl, REFRESH_KEY_CABINET } from '@/utils/refresh-control';
import { useDidShowCom } from '@base/hooks/page';
import { dateCalendar, noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Fragment, useMemo } from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import { padZero } from '../../kdg/_utils';
import { getPickUpList } from '../../pickup/_utils';
import './index-content-cabinet.scss';
import ExpressLightsBar from '../lights-bar';
import CabinetOpen from '../../kdg/cabinetOpen';

// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此组件已整个拿在components中 !!!!
// 此文件暂时做备份用，切勿修改此文件！！！
// src/components/_pages/query/list/components/cabinetList/index.jsx

const ListContentCabinet = (props) => {
  const { updateList = noop, search, handleRemove = noop, dakInfo } = props;
  const { list, else_list, shelves, other_list } = getPickUpList(props);

  const other = useMemo(() => {
    return [
      {
        label: `收件人柜中其他包裹（${other_list.length}）`,
        key: 'other_list',
        data: other_list,
        hide: !search,
      },
      {
        label: `其他柜中包裹（${else_list.length}）`,
        key: 'else_list',
        data: else_list,
        className: 'kb-size__bold',
        // hide: search,
      },
      { label: `货架包裹（${shelves.length}）`, key: 'shelves', data: shelves },
    ].filter((item) => !item.hide);
  }, [else_list, shelves, other_list, search]);

  const handleNavigator = (item, shelves) => {
    const { waybill_no: waybill, brand, dak_id: dakId, device_id } = item;
    refreshControl(REFRESH_KEY_CABINET);
    Taro.navigator({
      url: 'query/detail',
      options: {
        waybill,
        brand,
        dakId,
        isCabinet: '1',
        device_id,
        shelves: +shelves,
        row: item.row,
        col: item.col,
      },
    });
  };

  const handleCatch = (e) => e.stopPropagation();

  useDidShowCom(() => {
    if (refreshControl(REFRESH_KEY_CABINET, 'check')) {
      updateList();
    }
  });

  return (
    <View className='kb-cabinetList'>
      {list.length ? (
        list.map((item) => (
          <View
            className='kb-cabinetList-item'
            key={item.waybill_no}
            hoverClass='kb-hover-opacity'
            onClick={() => handleNavigator(item)}
          >
            <View className='kb-cabinetList-item__title at-row at-row__align--center at-row__justify--between'>
              {search ? (
                '查询结果'
              ) : (
                <Fragment>
                  <View className='at-row at-row__align--center at-row__justify--between kb-width-unset'>
                    <View className='kb-brandAvatar'>
                      <KbBrand brand={item.brand} />
                    </View>
                    {item.waybill_no && (
                      <View
                        className='at-row at-row__align--center at-row__justify--between kb-margin-sm-l'
                        onClick={handleCatch}
                      >
                        <View
                          className='kb-size__base at-row at-row__align--center'
                          hoverClass='kb-hover-opacity'
                          onClick={(e) => {
                            e.stopPropagation();
                            setClipboardData(item.waybill_no);
                          }}
                        >
                          <View className='kb-color__black'>
                            <Text>{item.brandName}</Text>
                            <Text className='kb-size__base'> {item.waybill_no} </Text>
                            <AtIcon
                              prefixClass='kb-icon'
                              value='copy-text'
                              className='kb-icon-size__sm kb-color__grey'
                            />
                          </View>
                        </View>
                      </View>
                    )}
                  </View>
                  <View className='kb-size__sm kb-color__grey'>
                    {dateCalendar(item.created_time, { timer: true })}
                  </View>
                </Fragment>
              )}
            </View>
            <View className='kb-cabinetList-item__content'>
              <View className='at-row at-row__align--center at-row__justify--between'>
                <View>
                  {item.suspected > 0 ? (
                    <View className='kb-package_suspected'>疑似包裹</View>
                  ) : (
                    <View className='kb-size__xl kb-color__brand'>
                      {search
                        ? `${padZero(item.row) + '号柜 ' + padZero(item.row) + padZero(item.col)}` +
                          '格口'
                        : `取件码 : ${item.pickupCode}`}
                    </View>
                  )}
                  {item.express_phone && item.mobileType == 0 && (
                    <View className='kb-spacing-sm-t kb-size__base kb-color__grey'>
                      亲友包裹
                      {item.mobileNote && `（${item.mobileNote}）`}：{item.express_phone}
                    </View>
                  )}
                  {search ? (
                    <Fragment>
                      <View className='kb-size__base kb-color__grey kb-spacing-sm-tb'>
                        {item.brandName} {item.waybill_no}
                      </View>
                      <View className='kb-size__base kb-color__grey'>{item.dakName}</View>
                    </Fragment>
                  ) : (
                    <View className='kb-size__base kb-color__grey kb-spacing-md-t'>
                      快递柜：{item.dakName}
                    </View>
                  )}
                </View>
                <View onClick={handleCatch}>
                  <CabinetOpen
                    dakInfo={dakInfo}
                    data={item}
                    updateList={updateList}
                    handleRemove={handleRemove}
                  />
                </View>
              </View>
            </View>
          </View>
        ))
      ) : (
        <View className='kb-cabinetList-item kb-empty'>当前柜机暂无待取包裹</View>
      )}
      {other.map((v) => {
        return (
          v.data.length && (
            <View className='kb-cabinetList-item' key={v.key}>
              <View className='kb-cabinetList-item__title'>
                <Text className={v.className}>{v.label}</Text>
              </View>
              {v.data.map((item, index) => (
                <View
                  className={classNames({
                    'kb-cabinetList-item__content': true,
                    'kb-border-bottom': index !== v.data.length - 1,
                  })}
                  key={item.waybill_no}
                  hoverClass='kb-hover-opacity'
                  onClick={() => handleNavigator(item, v.key == 'shelves')}
                >
                  <View className='at-row at-row__align--center at-row__justify--between'>
                    <View>
                      {item.suspected > 0 ? (
                        <View className='kb-package_suspected'>疑似包裹</View>
                      ) : (
                        <View className='kb-size__xl kb-color__brand'>
                          {search && v.key == 'other_list'
                            ? `${
                                padZero(item.row) + '号柜 ' + padZero(item.row) + padZero(item.col)
                              }` + '格口'
                            : `取件码 : ${item.pickupCode}`}
                        </View>
                      )}
                      <View className='kb-size__base kb-color__grey kb-spacing-sm-tb'>
                        {item.brandName} {item.waybill_no}
                      </View>
                      {item.express_phone && item.mobileType == 0 && (
                        <View className='kb-spacing-sm-t kb-size__base kb-color__grey'>
                          亲友包裹
                          {item.mobileNote && `（${item.mobileNote}）`}：{item.express_phone}
                        </View>
                      )}
                      <View className='kb-size__base kb-color__grey'>{item.dakName}</View>
                    </View>
                    <View onClick={handleCatch}>
                      {v.key != 'shelves' && (
                        <CabinetOpen
                          dakInfo={dakInfo}
                          data={item}
                          updateList={updateList}
                          handleRemove={handleRemove}
                        />
                      )}
                      {v.key == 'shelves' && (
                        <ExpressLightsBar
                          waybill={item.waybill_no}
                          dak_id={item.dak_id}
                          source='3'
                        />
                      )}
                    </View>
                  </View>
                </View>
              ))}
            </View>
          )
        );
      })}
    </View>
  );
};

ListContentCabinet.options = {
  addGlobalClass: true,
};
export default connect(
  ({ global: { brands = {} } }) => ({
    brands,
  }),
  {
    get,
  },
)(ListContentCabinet);
