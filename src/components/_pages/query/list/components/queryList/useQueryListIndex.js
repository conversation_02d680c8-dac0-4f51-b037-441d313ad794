/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';

export function useQueryListIndex(props) {
  const { list = [] } = props;

  const cardMode = useMemo(() => {
    let mode = 'single'; // compilation 单独卡片模式，或合集模式
    const isCompilation = list.some((i) => !!(i.inn_info && i.inn_info.inn_name));

    if (isCompilation) {
      mode = 'compilation';
    }
    return mode;
  }, [list]);

  const isShowAd = (index) => {
    if (cardMode == 'compilation') {
      return index == 0;
    } else {
      return list.length == 1 || index == 1;
    }
  };

  const formatItem = (item, index) => {
    const { inn_info = {}, pickup_list = [] } = item;
    const { cm_id } = inn_info || {};
    // 合集模式下，折叠交互。
    const itemKey = cardMode == 'compilation' ? cm_id : item.waybill;
    // 展示广告
    const isShowAdStatus = isShowAd(index);

    return {
      itemKey,
      isShowAdStatus,
      inn_info,
      pickup_list,
      dakId: cm_id,
    };
  };

  return {
    list,
    cardMode,
    formatItem,
  };
}
