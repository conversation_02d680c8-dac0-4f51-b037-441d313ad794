/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable import/first */
import { View } from '@tarojs/components';
import { useQueryListIndex } from './useQueryListIndex';
import Taro, { Fragment } from '@tarojs/taro';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import QueryListItemInnInfo from './listItem/innInfo';
import QueryListItemPickupInfo from './listItem/pickupInfo';
import KbCopyRight from '@/components/_pages/copy-right';
import QueryListStatus from './listStatus';

/**
 * 数据结构统一为此类型
 * 类型 1
 * 默认为使用驿站分组，头部展示驿站信息，下方为驿站包裹列表。此结构为一条完整数据卡片显示。
 *
 * 类型2
 * 原列表类型。每一条数据单独卡片，保留原有所有功能和样式。
 */

// const listTem = [
//   {
//     inn_info: {
//       cm_id: '3473674',
//       inn_name: '脉冲测试小屏',
//       inn_address: '天会广场二号楼',
//       type: 1,
//     },
//     pickup_list: [
//       {
//         brand: 'jt',
//         waybill: 'JT2927269273629',
//         pickup_code: '685836',
//         create_time: '2024-09-26 17:22:08',
//       },
//     ],
//   },
// ];

const QueryListIndex = (props) => {
  const { adUnitIdIndex, isAppointment, isFirstPage, service, showMoreDakEmptyStatus } = props;

  const { cardMode, list, formatItem } = useQueryListIndex(props);

  return (
    <View className='kb-list'>
      {list.map((item, index) => {
        const { itemKey, isShowAdStatus, inn_info, pickup_list, dakId } = formatItem(item, index);
        return (
          <Fragment key={itemKey}>
            <View className='kb-list__item--wrapper'>
              {cardMode == 'compilation' ? (
                <Fragment>
                  <QueryListItemInnInfo innInfo={inn_info} />
                  {pickup_list.map((i) => (
                    <QueryListItemPickupInfo
                      {...props}
                      item={{
                        ...i,
                        ...inn_info,
                        pickupCode: i.pickup_code || i.pickupCode,
                        dakId: i.inn_id,
                      }}
                      key={i.waybill}
                      mode='compilation'
                      dakId={inn_info.cm_id || dakId}
                    />
                  ))}
                </Fragment>
              ) : (
                <QueryListItemPickupInfo item={item} {...props} />
              )}
            </View>
            {isShowAdStatus && adUnitIdIndex && (
              <View id='float-pos-rb'>
                <KbExternalAd adUnitIdIndex={adUnitIdIndex} wrapper />
              </View>
            )}
          </Fragment>
        );
      })}
      {cardMode == 'compilation' && showMoreDakEmptyStatus && <QueryListStatus list={list || []} />}
      {!isAppointment && !isFirstPage && <KbCopyRight service={service} />}
    </View>
  );
};

QueryListIndex.options = {
  addGlobalClass: true,
};

export default QueryListIndex;
