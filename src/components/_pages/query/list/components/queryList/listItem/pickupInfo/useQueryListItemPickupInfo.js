/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { getExpressStatus, swipeActionOperator } from '@/components/_pages/query/_utils/query.list';
import classNames from 'classnames';

const deleteItem = {
  key: 'delete',
  text: '删除',
  style: {
    backgroundColor: '#FF5A7A',
  },
};
// 不需要置顶了
// 左滑选项，根据是否制定区分显示
// const swipeActionOptions = {
//   top: [
//     {
//       key: 'top',
//       text: '置顶',
//     },
//     deleteItem,
//   ],
//   toped: [
//     {
//       key: 'top-cancel',
//       text: '取消置顶',
//     },
//     deleteItem,
//   ],
// };

export function useQueryListItemPickupInfo(props) {
  const {
    mode,
    item = {},
    flowData = {},
    brands = {},
    isFirstPage,
    isProxyPickup,
    isCabinet,
    dakId,
    handleUpdateList = () => {},
    // updateList = () => {},
  } = props;

  const [openedId, updateOpenedId] = useState(null);

  // 左滑与长按
  const createItemKey = ({ brand = '', waybill = '' } = {}, index = -1) => {
    const key = `${brand}-${waybill}`;
    if (index >= 0) {
      return `${key}-${index}`;
    }
    return key;
  };

  const itemKey = createItemKey(item);
  // @微快递更新物流信息对应数据
  const flowDataItem = flowData ? flowData[createItemKey(item)] : {};

  const brandName = brands[item.brand] && brands[item.brand].name;
  // 疑似包裹 & 没有取件码 => 驿站未开启疑似包裹开关
  const disabled = item.isLike == '1' && !item.pickupCode;

  // 是否为驿站卡片合集显示
  const isCompilation = mode === 'compilation';

  const itemCls = classNames({
    'kb-list__item--disabled': disabled,
    kb__compilation: isCompilation,
  });

  const swipeOpened = openedId === itemKey;

  const handleSwitchSwipeAction = (item, index) => {
    updateOpenedId(item ? createItemKey(item, index) : null);
  };

  // 选项处理
  const handleSwipeActionClick = (item, index, e) =>
    swipeActionOperator(item, e)
      .then((res) => {
        const { topList } = res || {};
        const { key } = e;
        if (topList) {
          // 优化性能与体验，如果返回置顶数据，则优先设置置顶显示状态；且置顶设置不再重新拉取数据
          // mergeListAndTopData(list, type, topList)
          //   .then(updateList)
          //   .catch((err) => console.log(err));
          // if (key.startsWith('top')) {
          //   // 设置置顶信息
          //   return;
          // }
        } else if (key === 'delete' && index >= 0) {
          // 删除，体验优化，先更新视图再更新数据 237400
          //  list.splice(index, 1);
          //  updateList(list);
        }
        handleUpdateList();
      })
      .catch((err) => console.log(err));

  // 长按选项
  const swipeActionLongTapOptions = (item) => {
    return [
      { key: 'copy', text: '复制单号' },
      { key: 'note', text: `${!item.note ? '添加' : '修改'}备注` },
      {
        key: 'delete',
        text: '删除',
        style: {
          backgroundColor: '#FF5A7A',
        },
      },
    ];
  };

  // 跳转
  const handleNavigator = (item) => {
    const {
      brand,
      waybill,
      isLike,
      expressStatus,
      dakId: itemDakId = dakId,
      order_no,
      note,
      pickupCode,
      device_id,
    } = item;

    // 左滑开启，禁止触发点击
    if (openedId) return;
    if (isLike == '1' && !pickupCode) return;
    if (isProxyPickup) return;

    const isToAppointment = getExpressStatus(expressStatus) == '待取件' && isFirstPage && itemDakId;
    if (isCompilation || isToAppointment) {
      Taro.navigator({
        url: 'query/appointment',
        options: {
          dakId: itemDakId,
        },
      });
      return;
    }
    Taro.navigator({
      url: `query/${brand ? 'detail' : 'match'}`,
      options: {
        waybill,
        brand,
        dakId: itemDakId,
        order_no,
        note,
        isCabinet: isCabinet,
        device_id,
      },
    });
  };

  return {
    ...props,
    isCompilation,
    brandName: isCompilation ? '' : brandName,
    itemCls,
    swipeOpened,
    flowDataItem,
    disabled,
    swipeActionOptions: deleteItem,
    handleSwitchSwipeAction,
    handleSwipeActionClick,
    swipeActionLongTapOptions,
    handleNavigator,
  };
}
