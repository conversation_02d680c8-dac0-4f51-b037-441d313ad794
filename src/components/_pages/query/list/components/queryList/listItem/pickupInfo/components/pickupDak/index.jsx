/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import ExpressLightsBar from '@/components/_pages/query/lights-bar';
import KbStatusWrap from '@/components/_pages/status-wrap';
import KbButton from '@base/components/button';
import KbFeedbackBar from '@/components/_pages/query/feedback-bar';
import { getExpressStatus } from '@/components/_pages/query/_utils/query.list';
import { formatParamsOut } from '@base/components/_utils';
import classNames from 'classnames';
import CabinetOpen from '@/components/_pages/kdg/cabinetOpen';
import './index.scss';

// 驿站信息卡片
const PickupInfoDak = (props) => {
  const {
    item,
    isAppointment,
    flowDataItem = {},
    isCabinet,
    isProxyPickup,
    shareKey,
    isComplaint,
    handleUpdateList = () => {},
    viewPicture,
    isCompilation,
  } = props;

  const {
    isLike,
    pickupCode,
    dakId,
    waybill,
    isMark,
    specialMark,
    retreatReason,
    remark,
    mobile,
    mobileNote,
    applyStatus,
    lastLogistics,
    expressOrderMsg,
    inn_name,
    dakName,
    is_cabinet,
    expressStatus,
  } = item || {};

  const { status = getExpressStatus(expressStatus) } = item;
  const statusData = {
    status,
    ...flowDataItem,
  };

  const mobileType = 'notRelationMobile' in item ? item.notRelationMobile : item.mobileType;

  // 包裹为疑似包裹且有取件码返回，此驿站开启了展示疑似包裹功能
  const isDakOpenLikeStatus = isLike == '1' && pickupCode;

  // 是否展示取件码
  const isShowPickupCode = !!(pickupCode && expressStatus != '0' && statusData.status != '退回件');

  // 已出库时展示出库图片(驿站逻辑)
  const showPackagePic =
    process.env.MODE_ENV !== 'wkd' ? viewPicture == 1 && item.img_url : !!item.img_url;

  // 快宝驿站中隐藏待取件状态
  const hidePackageStatus = process.env.MODE_ENV === 'yz' && statusData.status == '待取件';

  const formatDakId = dakId || props.dakId;

  const handleCatch = (e) => e.stopPropagation();

  const handleSeeImage = (e) => {
    e.stopPropagation();

    // 前一项为驿站的兼容项，第二项为固定输出项
    const keys = [
      ['img_url', 'certificate_path'],
      ['waybill', 'waybillno'],
      ['order_id', 'order_number'],
    ];
    const {
      certificate_path,
      waybillno,
      order_number,
      brand: express_rand,
      in_time,
      out_time,
      inn_name,
    } = formatParamsOut({
      data: { ...item },
      keys,
    });
    Taro.navigator({
      url: 'order/voucher',
      options: {
        certificate_path,
        waybillno,
        order_number,
        brand: express_rand,
        inn_name,
        dak_id: formatDakId,
        outTime: out_time,
        inTime: in_time,
      },
    });
  };

  return (
    <View className={isCompilation ? 'kb-isCompilation' : ''}>
      {isLike == '1' ? (
        <View className='kb-spacing-md kb-query__extra--box'>
          <View>
            <View className='at-row at-row__align--center  at-row__justify--between'>
              <View className='at-row at-row__align--center  at-row__justify--start'>
                {isShowPickupCode && (
                  <Text className='kb-size__bold kb-color__black kb-size__xl kb-spacing-md-r'>
                    {isCompilation ? '' : '取件码 :'} {pickupCode}
                  </Text>
                )}
                <Text className='kb-color__red kb-spacing-md-t kb-spacing-md-b kb-size__lg'>
                  {isDakOpenLikeStatus ? '疑似' : '疑似您的包裹'}
                </Text>
              </View>

              {isShowPickupCode && (
                <ExpressLightsBar
                  dak_id={formatDakId}
                  waybill={waybill}
                  source={is_cabinet ? '3' : '1'}
                />
              )}
            </View>
            <View className='kb-color__greyer kb-size__sm kb-spacing-md-b'>
              请联系站点工作人员确认
            </View>
          </View>
        </View>
      ) : (
        <View className='at-row at-row__align--center kb-query__extra--box'>
          <View className='kb-query__extra kb-size__base kb-color__grey at-col'>
            {isMark == '1' && (
              <View className='kb-query__extra--appointment'>
                <AtIcon
                  prefixClass='kb-icon'
                  value='appointment'
                  className='kb-color__brand kb-icon-size__xxl'
                />
              </View>
            )}
            <View className='kb-query__extra--pickup'>
              <View className='at-row at-row__align--center at-row__justify--between'>
                <View className='at-row at-row__align--center at-row__justify--start'>
                  {!hidePackageStatus && (
                    <KbStatusWrap
                      className={classNames('kb-spacing-sm-r', {
                        'kb-size__base2': isCompilation,
                        'kb-color__black': !isCompilation,
                        'kb-color__greyer': isCompilation,
                      })}
                      status={statusData.status}
                    />
                  )}
                  {isShowPickupCode && (
                    <Text className='kb-size__bold kb-color__black'>
                      {isCompilation ? '' : '取件码 :'} {pickupCode}
                    </Text>
                  )}
                  {specialMark && (
                    <Text className='kb-query__extra--mark kb-margin-sm-l'>{specialMark}</Text>
                  )}
                </View>
                {isShowPickupCode && isMark != '1' && (
                  <ExpressLightsBar
                    dak_id={formatDakId}
                    waybill={waybill}
                    source={is_cabinet ? '3' : '1'}
                  />
                )}
              </View>
              {retreatReason && (
                <View
                  className={classNames('kb-spacing-sm-t', {
                    'kb-size__base': isCompilation,
                    'kb-color__black': !isCompilation,
                    'kb-color__greyer': isCompilation,
                  })}
                >
                  <Text>退回原因：</Text>
                  <Text>{retreatReason}</Text>
                </View>
              )}
              {remark && (
                <View className='kb-spacing-sm-t kb-color__grey kb-size__sm'>
                  <Text>备注：</Text>
                  <Text>{remark}</Text>
                </View>
              )}
            </View>

            <View>
              {mobile && mobileType === 0 && (
                <View className='kb-spacing-sm-t'>
                  亲友包裹
                  {mobileNote && `（${mobileNote}）`}：{mobile}
                </View>
              )}
              {applyStatus == '2' && (
                <View className='kb-spacing-sm-t kb-color__red'>
                  此包裹上次预约未能出库，详情请咨询站点工作人员
                </View>
              )}
              {!isAppointment && (
                <View className='at-row at-row__align--center at-row__justify--between kb-spacing-sm-t'>
                  <View
                    className={
                      process.env.MODE_ENV === 'wkd' ? 'kb-query__logistics' : 'kb-query__in-msg'
                    }
                  >
                    {flowDataItem.lastLogistics || lastLogistics || ''}
                  </View>
                </View>
              )}

              {(expressOrderMsg || isAppointment || isCabinet) && !isCompilation && (
                <View className='at-row at-row__align--center at-row__justify--between kb-spacing-sm-t'>
                  {isCabinet ? (
                    <View>{`快递柜：${inn_name || dakName}`}</View>
                  ) : (
                    expressOrderMsg &&
                    (!isAppointment || isProxyPickup) && (
                      <View className='kb-query__in-msg'>
                        {process.env.MODE_ENV === 'wkd' ? (
                          expressOrderMsg
                        ) : (
                          <Fragment>
                            {statusData.status === '退回件'
                              ? `操作${is_cabinet ? '快递柜' : '驿站'}：`
                              : `入库${is_cabinet ? '快递柜' : '驿站'}：`}
                            {item.expressOrderMsg}
                          </Fragment>
                        )}
                      </View>
                    )
                  )}
                  {isAppointment && !shareKey && !isCabinet && (
                    <View className='at-row  kb-opt-wrap'>
                      {showPackagePic && (
                        <View
                          className='kb-margin-lg-r'
                          hoverClass='kb-hover-opacity'
                          hoverStopPropagation
                          onClick={handleCatch}
                        >
                          <KbButton
                            hoverStopPropagation
                            onClick={handleSeeImage}
                            className='kb-button__link'
                          >
                            包裹图片
                          </KbButton>
                        </View>
                      )}
                      <View hoverStopPropagation onClick={handleCatch}>
                        <KbFeedbackBar
                          className='kb-button__link kb-clear__background-color'
                          dakId={formatDakId}
                          data={item}
                          updateList={handleUpdateList}
                        />
                      </View>
                    </View>
                  )}
                </View>
              )}
            </View>
          </View>
          {isCabinet && isAppointment && !isProxyPickup && (
            <View className='kb-spacing-md-r' hoverStopPropagation onClick={handleCatch}>
              <CabinetOpen data={item} updateList={handleUpdateList} />
            </View>
          )}
          {isComplaint && (
            <View className='kb-spacing-md-r' hoverStopPropagation onClick={handleCatch}>
              <KbFeedbackBar
                type='secondary'
                dakId={formatDakId}
                data={item}
                updateList={handleUpdateList}
              />
            </View>
          )}
        </View>
      )}
    </View>
  );
};

PickupInfoDak.options = {
  addGlobalClass: true,
};

export default PickupInfoDak;
