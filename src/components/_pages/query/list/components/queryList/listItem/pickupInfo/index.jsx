/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import KbSwipeAction from '@base/components/swipe-action';
import { useQueryListItemPickupInfo } from './useQueryListItemPickupInfo';
import PickupInfoBrands from './components/header';
import PickupInfoDak from './components/pickupDak';

const QueryListItemPickupInfo = (props) => {
  const { item = {}, index, isFirstPage, isProxyPickup } = props;

  const {
    isCompilation,
    brandName,
    itemCls,
    swipeOpened,
    flowDataItem,
    disabled,
    handleNavigator,
    swipeActionLongTapOptions,
    handleSwipeActionClick,
    handleSwitchSwipeAction,
    swipeActionOptions,
  } = useQueryListItemPickupInfo(props);

  return (
    <View className={itemCls}>
      <KbSwipeAction
        autoClose
        disabled={!isFirstPage}
        isOpened={swipeOpened}
        onOpened={handleSwitchSwipeAction.bind(null, item, index)}
        onClosed={handleSwitchSwipeAction}
        onClick={handleSwipeActionClick.bind(null, item, index)}
        options={item.isTop ? swipeActionOptions.toped : swipeActionOptions.top}
        longTapOptions={swipeActionLongTapOptions.bind(null, item)}
        title={item.note ? item.note : `${brandName} ${item.waybill}`}
      >
        <View
          hoverClass={disabled && isProxyPickup ? 'none' : 'kb-hover'}
          onClick={handleNavigator.bind(null, item)}
        >
          <PickupInfoBrands brandName={brandName} isCompilation={isCompilation} {...item} />
          <PickupInfoDak
            item={item}
            flowDataItem={flowDataItem}
            isCompilation={isCompilation}
            {...props}
          />
        </View>
      </KbSwipeAction>
    </View>
  );
};

QueryListItemPickupInfo.options = {
  addGlobalClass: true,
};

export default QueryListItemPickupInfo;
