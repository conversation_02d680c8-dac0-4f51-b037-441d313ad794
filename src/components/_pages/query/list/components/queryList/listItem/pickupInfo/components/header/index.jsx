/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text } from '@tarojs/components';
import KbBrand from '@/components/_pages/brand';
import { AtIcon } from 'taro-ui';
import { dateCalendar } from '@base/utils/utils';
import { setClipboardData } from '@/utils/qy';
import classNames from 'classnames';

import './index.scss';

// 取件卡片顶部，显示品牌，单号和时间信息

const PickupInfoBrands = (props) => {
  const handleCatch = (e) => e.stopPropagation();

  const {
    brand,
    brandName,
    waybill,
    createTime,
    create_time = createTime,
    isLike,
    pickupCode,
    isCompilation,
  } = props;

  // 是否展示复制单号按钮
  const isShowCopy = isLike == '1' ? pickupCode : true;

  const handleCopy = () => {
    setClipboardData(waybill);
  };

  return (
    <View
      className={classNames('kb-list__item', {
        'border-top': isCompilation,
      })}
    >
      <View className='kb-image--brand'>
        <KbBrand brand={brand} />
      </View>
      <View
        className={classNames('item-content kb-margin-sm-l', {
          'kb-color__greyer': isCompilation,
        })}
      >
        <View className='item-content__title'>
          <View className='at-row at-row__align--center at-row__justify--between'>
            <View
              className='kb-size__base at-row at-row__align--center kb-width-auto'
              onClick={handleCatch}
            >
              {brandName && <Text className='kb-spacing-sm-r'>{brandName}</Text>}
              <View
                onClick={handleCopy.bind(null, waybill, (e) => e.stopPropagation())}
                stopPropagation
              >
                <Text className='kb-header__text'>{waybill}</Text>
                {isShowCopy && (
                  <AtIcon
                    prefixClass='kb-icon'
                    value='copy-text'
                    className='kb-icon-size__sm kb-color__grey'
                  />
                )}
              </View>
            </View>
            {create_time && (
              <View className='kb-size__sm kb-color__grey'>
                {dateCalendar(create_time, { timer: true })}
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

PickupInfoBrands.options = {
  addGlobalClass: true,
};

export default PickupInfoBrands;
