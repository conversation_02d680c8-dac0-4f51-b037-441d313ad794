/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-query {
  &__extra {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    min-height: 180px;
    padding: $spacing-v-md $spacing-h-md;
    overflow: hidden;

    &--pickup {
      font-weight: bold;
      // color: $color-black-1;
      font-size: $font-size-xl;

      .kb-color__grey {
        font-weight: normal;
        font-size: $font-size-lg;
      }
    }
    &--mark {
      padding: 5px $spacing-v-sm;
      color: $color-orange-lighter;
      font-size: $font-size-base;
      background-color: $color-yellow-1;
      border-radius: $border-radius-md;
    }

    &--box {
      border-top: $border-lightest;
    }

    &--icon,
    &--text {
      display: inline-block;
      vertical-align: middle;
    }

    &--icon {
      width: 30px;
      height: 30px;
      margin-right: $spacing-h-md;
    }

    &--appointment {
      position: absolute;
      top: 20px;
      right: 20px;
    }
  }

  &__in-msg,
  &__logistics {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__in-msg {
    max-width: 80%;
  }
}

.kb-isCompilation {
  .kb-query__extra {
    min-height: unset;
    padding-top: 0;
    &--box {
      border: unset;
    }
    // &--pickup {
    //   font-size: $font-size-base;
    // }
  }
}
