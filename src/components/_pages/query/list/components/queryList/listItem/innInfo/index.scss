/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.ql_innInfo {
  display: flex;
  gap: $spacing-v-sm;
  align-items: center;
  justify-content: flex-start;
  padding: $spacing-v-md;
  color: $color-grey-1;
  font-size: $font-size-lg + 1;
  background-color: $color-white;
  border-radius: $border-radius-md;
  &__info {
    flex: 1;
  }
  &__button {
    width: 100px;
    padding: $spacing-v-xs;
    color: $color-brand;
    font-size: $font-size-sm;
    text-align: center;
    border: $border-lightest;
    border-color: $color-brand;
    border-radius: 10px;
  }
}
