/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbStationAvatar from '@/components/_pages/store-card/station-avatar';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

import './index.scss';

const QueryListItemInnInfo = (props) => {
  const { innInfo } = props;
  const { cm_id, type, inn_name, inn_address, home_deliver } = innInfo || {};

  const onNavigator = () => {
    Taro.navigator({
      url: 'query/appointment',
      options: {
        dakId: cm_id,
      },
    });
  };

  const onOpenDelivery = (event) => {
    event.stopPropagation();
    Taro.navigator({
      url: 'query/appointment/delivery',
      options: {
        postData: JSON.stringify({
          dak_id: cm_id,
        }),
      },
    });
  };

  return (
    <View className='ql_innInfo' hoverClass='kb-hover' onClick={onNavigator}>
      <KbStationAvatar
        size='80x80'
        data={{
          ...innInfo,
          dak_id: cm_id,
        }}
        isKdg={type == '2'}
      />
      <View className='ql_innInfo__info'>
        <View>{inn_name}</View>
        <View className='kb-size__sm kb-color__grey'>{inn_address}</View>
      </View>
      {!!(type == '1' && home_deliver) && (
        <View className='ql_innInfo__button' onClick={onOpenDelivery}>
          跑腿代取
        </View>
      )}
    </View>
  );
};

QueryListItemInnInfo.options = {
  addGlobalClass: true,
};

export default QueryListItemInnInfo;
