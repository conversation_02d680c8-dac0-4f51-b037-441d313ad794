/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import KbLoader from '@base/components/loader';
import Taro, { Fragment } from '@tarojs/taro';

import './index.scss';
import { useQueryListStatus } from './useQueryListStatus';
import QueryListItemInnInfo from '../listItem/innInfo';

// 需要改造

// 合集模式下，页脚展示
const QueryListStatus = (props) => {
  const {
    showRelation = [],
    showMoreDak,
    setShowMoreDak,
    getRelationList,
  } = useQueryListStatus(props);

  const showLoader = showRelation.length ? showMoreDak : true;

  const handleClick = () => {
    getRelationList().then(() => {
      setShowMoreDak(true);
    });
  };

  return (
    <Fragment>
      {showMoreDak ? (
        <View>
          {showRelation.map((item) => (
            <View className='kb-spacing-md-b' key={item.cm_id}>
              <QueryListItemInnInfo innInfo={item} />
            </View>
          ))}
        </View>
      ) : (
        <View className='q-l-loadMore'>
          <View hoverClass='kb-hover' className='q-l-loadMore__text' onClick={handleClick}>
            查看更多驿站或快递柜
          </View>
        </View>
      )}
      {showLoader && <KbLoader noMoreText='没有更多了' status='noMore' size='small' />}
    </Fragment>
  );
};

QueryListStatus.options = {
  addGlobalClass: true,
};

export default QueryListStatus;
