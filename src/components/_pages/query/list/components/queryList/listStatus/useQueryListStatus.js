/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import Taro, { useState, useMemo } from '@tarojs/taro';
import { checkIsKdg } from '~/components/_pages/store-card/_utils';

export function useQueryListStatus(props) {
  const { list: propsList = [] } = props;

  const [relationList, setRelationList] = useState([]);

  const [showMoreDak, setShowMoreDak] = useState(false);

  // 格式化下单关系列表显示
  const formatRelationList = (list) => {
    const list_ = (Array.isArray(list) ? list : [])
      .filter((i) => i.dak_id)
      .map((i) => {
        const { dak_id, label, home_deliver } = i;
        return {
          cm_id: `${dak_id}`,
          dakId: `${dak_id}`,
          inn_name: label,
          type: checkIsKdg(i) ? '2' : '1',
          inn_address: '',
          home_deliver: !!(home_deliver == '1'),
        };
      });
    return list_;
  };

  // 获取下单关系列表
  const getRelationList = () => {
    return new Promise((resolve) => {
      request({
        url: '/api/weixin/mini/minpost/relation/newUserRelationList',
        data: {
          find_cabinet: 1,
          page: 1,
        },
        onThen: ({ data = [] }) => {
          const list_ = formatRelationList(data);
          setRelationList(list_);
          resolve(list_.length > 0);
        },
      });
    });
  };

  const showRelation = useMemo(() => {
    const list_ = relationList.filter((i) => {
      const isPickup = propsList.some((o) => {
        const inn_info = o.inn_info || {};
        return inn_info.cm_id == i.cm_id;
      });
      return !isPickup;
    });
    return list_;
  }, [propsList, relationList]);

  // useEffect(() => {
  //   getRelationList();
  // }, []);

  return {
    showRelation,
    showMoreDak,
    setShowMoreDak,
    getRelationList,
  };
}
