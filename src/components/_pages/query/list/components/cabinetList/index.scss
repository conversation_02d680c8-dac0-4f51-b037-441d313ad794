/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-cabinetList {
  padding: $spacing-h-md;
  padding-top: 0;
  &-item {
    margin-bottom: $spacing-v-md;
    background-color: $color-white;
    border-radius: $border-radius-md;
    &__title {
      box-sizing: border-box;
      padding: $spacing-h-md;
      border-bottom: $border-lightest;
    }
    &__content {
      box-sizing: border-box;
      padding: 40px $spacing-h-md;
    }
  }
  .flex-1 {
    flex: 1;
  }
  .kb-empty {
    padding: $spacing-v-xl $spacing-h-md;
    color: $color-brand;
    font-size: $font-size-xl;
    text-align: center;
  }
  .kb-width-unset {
    width: unset;
  }
  .kb-border-bottom {
    border-bottom: $border-lightest;
  }
  .kb-brandAvatar {
    height: 50px;
  }
  .kb-package_suspected {
    display: inline-block;
    padding: 0 $spacing-h-md;
    color: $color-red;
    border: $width-base solid $color-red;
    border-radius: $border-radius-md;
  }
}
