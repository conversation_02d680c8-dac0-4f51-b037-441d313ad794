/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbBrand from '@/components/_pages/brand';
import { setClipboardData } from '@/utils/qy';
import { refreshControl, REFRESH_KEY_CABINET } from '@/utils/refresh-control';
import { useDidShowCom } from '@base/hooks/page';
import { dateCalendar, noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Fragment, useMemo } from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import { getPickUpList } from '@/components/_pages/pickup/_utils';
import { padZero } from '@/components/_pages/kdg/_utils';
import CabinetOpen from '@/components/_pages/kdg/cabinetOpen';
import KbExternalAd from '~/components/_pages/ad-extension/ad';
import ExpressLightsBar from '../../../lights-bar';
import './index.scss';

const CabinetListIndex = (props) => {
  const { updateList = noop, search, handleRemove = noop, dakInfo, mode } = props;
  const { list, else_list, shelves, other_list } = getPickUpList(props);

  const other = useMemo(() => {
    return [
      {
        label: `收件人柜中其他包裹（${other_list.length}）`,
        key: 'other_list',
        data: other_list,
        hide: !search,
        className: 'kb-size__bold',
      },
      {
        label: `其他柜中包裹（${else_list.length}）`,
        key: 'else_list',
        data: else_list,
        className: 'kb-size__bold',
        // hide: search,
      },
      {
        label: `货架包裹（${shelves.length}）`,
        key: 'shelves',
        data: shelves,
        className: 'kb-size__bold',
      },
    ].filter((item) => !item.hide);
  }, [else_list, shelves, other_list, search]);

  const needPayList = useMemo(() => {
    const _list = [...list, ...else_list, ...shelves];
    if (search) {
      _list.push(...other_list);
    }
    return _list.filter((item) => item.outTimeInfo && item.outTimeInfo.fee && item.rejection != 1);
  }, [list, else_list, shelves, other_list, search]);

  const handleNavigator = (item, shelves) => {
    const { waybill_no: waybill, brand, dak_id: dakId, device_id } = item;
    refreshControl(REFRESH_KEY_CABINET);
    Taro.navigator({
      url: 'query/detail',
      options: {
        waybill,
        brand,
        dakId,
        isCabinet: '1',
        device_id,
        shelves: +shelves,
        row: item.row,
        col: item.col,
      },
    });
  };

  const handleNavigatorPay = (item) => {
    Taro.navigator({
      url: 'pickup/pay',
      key: 'routerParamsChange',
      options: {
        list: needPayList.filter((v) => v.waybill_no !== item.waybill_no),
        current: item,
        dakInfo,
      },
    });
  };

  const handleCatch = (e) => e.stopPropagation();

  useDidShowCom(() => {
    if (refreshControl(REFRESH_KEY_CABINET, 'check')) {
      updateList();
    }
  });

  const listLen = list.length;
  const adIndex = Math.min(listLen, 2) - 1;

  return (
    <View className='kb-cabinetList'>
      {listLen ? (
        list.map((item, index) => (
          <Fragment key={item.waybill_no}>
            <View
              className='kb-cabinetList-item'
              key={item.waybill_no}
              hoverClass='kb-hover-opacity'
              onClick={() => handleNavigator(item)}
            >
              <View className='kb-cabinetList-item__title at-row at-row__align--center at-row__justify--between'>
                {search ? (
                  '查询结果'
                ) : (
                  <Fragment>
                    <View className='at-row at-row__align--center at-row__justify--between kb-width-unset'>
                      <View className='kb-brandAvatar'>
                        <KbBrand brand={item.brand} />
                      </View>
                      {item.waybill_no && (
                        <View
                          className='at-row at-row__align--center at-row__justify--between kb-margin-sm-l'
                          onClick={handleCatch}
                        >
                          <View
                            className='kb-size__base at-row at-row__align--center'
                            hoverClass='kb-hover-opacity'
                            onClick={(e) => {
                              e.stopPropagation();
                              setClipboardData(item.waybill_no);
                            }}
                          >
                            <View className='kb-color__black'>
                              <Text>{item.brandName}</Text>
                              <Text className='kb-size__base'> {item.waybill_no} </Text>
                              <AtIcon
                                prefixClass='kb-icon'
                                value='copy-text'
                                className='kb-icon-size__sm kb-color__grey'
                              />
                            </View>
                          </View>
                        </View>
                      )}
                    </View>
                    <View className='kb-size__sm kb-color__grey'>
                      {dateCalendar(item.created_time, { timer: true })}
                    </View>
                  </Fragment>
                )}
              </View>
              <View className='kb-cabinetList-item__content'>
                <View className='at-row at-row__align--center at-row__justify--between'>
                  {item.suspected > 0 ? (
                    <View className='kb-package_suspected'>疑似包裹</View>
                  ) : (
                    <View className='kb-size__xl kb-color__black'>
                      {`${
                        item.grid_number == 1 ? `${item.number}格口` : `${padZero(item.row)}号柜`
                      } | ${item.pickupCode}`}
                    </View>
                  )}
                  {search && (
                    <View className='kb-size__sm kb-color__grey'>
                      {dateCalendar(item.created_time, { timer: true })}
                    </View>
                  )}
                </View>
                <View className='at-row at-row__align--center at-row__justify--between'>
                  <View>
                    <View>
                      {item.express_phone && item.mobileType == 0 && (
                        <View className='kb-spacing-sm-t kb-size__base kb-color__grey'>
                          亲友包裹
                          {item.mobileNote && `（${item.mobileNote}）`}：{item.express_phone}
                        </View>
                      )}
                      {search ? (
                        <Fragment>
                          <View className='kb-size__base kb-color__grey kb-spacing-sm-tb'>
                            {item.brandName} {item.waybill_no}
                          </View>
                          <View className='kb-size__base kb-color__grey'>{item.dakName}</View>
                        </Fragment>
                      ) : (
                        <View className='kb-size__base kb-color__grey kb-spacing-md-t'>
                          快递柜：{item.dakName}
                        </View>
                      )}
                    </View>
                    {item.outTimeInfo && (
                      <View className='kb-size__sm kb-color__red kb-margin-sm-t'>
                        存放{item.outTimeInfo.saveTime}，超时费：￥{item.outTimeInfo.fee}
                      </View>
                    )}
                  </View>
                  <View onClick={handleCatch}>
                    <CabinetOpen
                      mode={mode}
                      dakInfo={dakInfo}
                      data={item}
                      updateList={updateList}
                      handleRemove={handleRemove}
                      handleNavigatorPay={
                        needPayList.length > 1 && item.outTimeInfo && item.outTimeInfo.fee
                          ? () => handleNavigatorPay(item)
                          : null
                      }
                    />
                  </View>
                </View>
              </View>
            </View>
            {index == adIndex && !search && (
              <View className='kb-spacing-md-b'>
                <KbExternalAd wrapper adUnitIdIndex='cabinet_pickup' />
              </View>
            )}
          </Fragment>
        ))
      ) : (
        <Fragment>
          {!search && <KbExternalAd wrapper adUnitIdIndex='cabinet_pickup' />}
          <View className='kb-cabinetList-item kb-empty'>当前柜机暂无待取包裹</View>
        </Fragment>
      )}
      {other.map((v) => {
        return (
          v.data.length && (
            <View className='kb-cabinetList-item' key={v.key}>
              <View className='kb-cabinetList-item__title'>
                <Text className={v.className}>{v.label}</Text>
              </View>
              {v.data.map((item, index) => (
                <View
                  className={classNames({
                    'kb-cabinetList-item__content': true,
                    'kb-border-bottom': index !== v.data.length - 1,
                  })}
                  key={item.waybill_no}
                  hoverClass='kb-hover-opacity'
                  onClick={() => handleNavigator(item, v.key == 'shelves')}
                >
                  <View className='at-row at-row__align--center at-row__justify--between'>
                    {item.suspected > 0 ? (
                      <View className='kb-package_suspected'>疑似包裹</View>
                    ) : (
                      <View className='kb-size__xl kb-color__black'>
                        {v.key == 'else_list'
                          ? `${
                              item.grid_number == 1
                                ? `${item.number}格口`
                                : `${padZero(item.row)}号柜`
                            } | ${item.pickupCode}`
                          : `取件码 : ${item.pickupCode}`}
                      </View>
                    )}
                    <View className='kb-size__sm kb-color__grey'>
                      {dateCalendar(item.created_time, { timer: true })}
                    </View>
                  </View>
                  <View className='at-row at-row__align--center at-row__justify--between'>
                    <View>
                      <View className='kb-size__base kb-color__grey kb-spacing-sm-tb'>
                        {item.brandName} {item.waybill_no}
                      </View>
                      {item.express_phone && item.mobileType == 0 && (
                        <View className='kb-spacing-sm-t kb-size__base kb-color__grey'>
                          亲友包裹
                          {item.mobileNote && `（${item.mobileNote}）`}：{item.express_phone}
                        </View>
                      )}
                      <View className='kb-size__base kb-color__grey'>{item.dakName}</View>
                    </View>
                    <View onClick={handleCatch}>
                      {(v.key === 'other_list' || v.key === 'else_list') && (
                        <CabinetOpen
                          mode={mode}
                          dakInfo={dakInfo}
                          data={item}
                          updateList={updateList}
                          handleRemove={handleRemove}
                          handleNavigatorPay={
                            needPayList.length > 1 && item.outTimeInfo && item.outTimeInfo.fee
                              ? () => handleNavigatorPay(item)
                              : null
                          }
                        />
                      )}
                      {v.key == 'shelves' && (
                        <ExpressLightsBar
                          waybill={item.waybill_no}
                          dak_id={item.dak_id}
                          source='3'
                          shelves
                        />
                      )}
                    </View>
                  </View>
                  {item.outTimeInfo && (
                    <View className='kb-size__sm kb-color__red'>
                      存放{item.outTimeInfo.saveTime}，超时费：￥{item.outTimeInfo.fee}
                    </View>
                  )}
                </View>
              ))}
            </View>
          )
        );
      })}
    </View>
  );
};

CabinetListIndex.options = {
  addGlobalClass: true,
};
export default connect(
  ({ global: { brands = {} } }) => ({
    brands,
  }),
  {
    get,
  },
)(CabinetListIndex);
