/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import Taro, { useEffect, useState } from '@tarojs/taro';
import { useDidShowCom } from '~base/hooks/page';

export const AUTO_OPEN_LIGHT_BAR = 'AUTO_OPEN_LIGHT_BAR';
export function useExpressLightsBar(props) {
  const { waybill, dak_id, source = '1', shelves } = props;

  const [pageData, setPageData] = useState({
    isShowLight: false,
    colorText: '',
    color: '',
  });

  const getLightBarByWaybills = () => {
    request({
      url: '/api/weixin/mini/waybill/record/getLightBarByWaybills',
      data: {
        waybill,
        dak_id,
        source,
      },
      toastLoading: false,
    }).then((res) => {
      const { code, data } = res;
      const lightsArr = Array.isArray(data) ? data : [];
      setPageData((val) => ({
        ...val,
        isShowLight: `${code}` === '0' && lightsArr.includes(waybill),
      }));
    });
  };

  useEffect(() => {
    if (waybill && dak_id) {
      getLightBarByWaybills();
    }
  }, [waybill, dak_id]);

  useDidShowCom(() => {
    const _refresh_waybill = Taro.kbGetGlobalData(AUTO_OPEN_LIGHT_BAR);
    if (_refresh_waybill == waybill) {
      setTimeout(() => {
        Taro.kbSetGlobalData(AUTO_OPEN_LIGHT_BAR, null);
        openLightBar();
      }, 300);
    }
  });

  const getShelfPackageOutTime = () => {
    return new Promise((resolve, reject) => {
      if (!shelves) {
        resolve();
        return;
      }
      request({
        url: '/api/weixin/mini/DakMini/Record/shelfPackageTimeoutQuery',
        data: {
          waybill_no: waybill,
          dak_id,
        },
        toastError: true,
        toastLoading: true,
        onThen: (res) => {
          const { code, data, msg } = res || {};
          if (code == 0) {
            resolve(data);
          } else {
            Taro.kbToast({
              text: msg,
            });
            reject();
          }
        },
      });
    });
  };

  const openLightBar = () => {
    request({
      url: '/api/weixin/mini/waybill/record/openLightBar',
      data: {
        waybill,
        dak_id,
        source,
      },
      toastError: true,
      toastLoading: true,
    }).then((res) => {
      const { code, data = {}, msg } = res;
      if (`${code}` === '0') {
        setPageData((val) => ({
          ...val,
          colorText: data.color || '',
          color: data.bg_color || '',
        }));
      } else if (msg) {
        Taro.kbToast({
          text: msg,
        });
      }
    });
  };

  const handleClick = () => {
    getShelfPackageOutTime().then((res) => {
      const { outTimeFlag } = res || {};
      if (outTimeFlag == '1') {
        Taro.navigator({
          url: 'pickup/pay',
          key: 'routerParamsChange',
          options: {
            shelfPackageInfo: {
              ...res,
              waybill,
            },
          },
        });
      } else {
        openLightBar();
      }
    });
  };

  return {
    ...pageData,
    handleClick,
  };
}
