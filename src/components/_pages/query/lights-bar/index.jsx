/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { useExpressLightsBar } from './useExpressLightsBar';
import './index.scss';

const ExpressLightsBar = (props) => {
  const { isShowLight, colorText, color, handleClick } = useExpressLightsBar(props);

  return isShowLight ? (
    <View
      className={`kb-expressLightsBar ${color ? 'kb-color__black' : ''}`}
      onClick={handleClick.bind(null, {}, (e) => e.stopPropagation())}
      stopPropagation
    >
      <AtIcon prefixClass='kb-icon' value='dengpao' />
      {color ? <View style={{ color }}>{colorText}</View> : <View>亮灯定位</View>}
    </View>
  ) : (
    <View />
  );
};

ExpressLightsBar.options = {
  addGlobalClass: true,
};

export default ExpressLightsBar;
