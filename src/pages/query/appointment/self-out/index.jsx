/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';
import KbPage from '~base/components/page';
import KbCheckbox from '~base/components/checkbox';
import Kb<PERSON>ongList from '~base/components/long-list';
import KbBrand from '~/components/_pages/brand';
import classNames from 'classnames';
import request from '~base/utils/request';
import { REFRESH_KEY_APPOINTMENT_LIST, refreshControl } from '~/utils/refresh-control';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '自助出库',
  };
  state = {
    list: [],
    selected: [],
  };

  listData = {
    pageKey: 'pageNum',
    openLocalPageMode: true,
    api: {
      url: '/api/weixin/mini/DakMini/Record/uncollectedPackageByDak',
      formatRequest: (req) => {
        return {
          ...req,
          cm_id: this.$router.params.cm_id,
          noEncrypt: 1,
        };
      },
      formatResponse: (res) => {
        const { code, data } = res || {};
        if (code == 0 && Array.isArray(data)) {
          const list = data.filter((item) => !item.cabinet_id);
          return {
            data: { list },
          };
        }
        return {
          data: void 0,
        };
      },
      onThen: (list) => {
        this.setState({
          list,
          selected: [],
        });
      },
    },
  };

  handleSelectAll = () => {
    const { list, selected } = this.state;
    this.setState({
      selected: selected.length === list.length ? [] : list,
    });
  };

  handleSelect = (item) => {
    const { selected } = this.state;
    this.setState({
      selected: selected.find((i) => i.waybill == item.waybill)
        ? selected.filter((i) => i.waybill != item.waybill)
        : [...selected, item],
    });
  };

  handleReviewImg = (certificate_path) => {
    Taro.navigator({
      url: 'order/voucher',
      options: {
        certificate_path,
        type: 'lite',
      },
    });
  };

  handleConfirm = () => {
    const { selected } = this.state;
    if (selected.length == 0) {
      Taro.kbToast({
        text: '请选择需要出库的包裹',
      });
      return;
    }
    request({
      url: '/api/weixin/mini/waybill/record/userConfirmPickup',
      data: {
        dak_id: this.$router.params.cm_id,
        waybill_info: selected.map((item) => ({
          brand: item.brand,
          waybill: item.waybill,
        })),
      },
      onThen: (res) => {
        const { code, data, msg } = res || {};
        if (code == 0) {
          Taro.kbToast({
            text: '出库成功',
          });
          refreshControl(REFRESH_KEY_APPOINTMENT_LIST);
          setTimeout(() => {
            Taro.navigator({
              url: 'query/appointment/self-out/result',
              target: 'self',
              key: 'routerParamsChange',
              options: {
                list: selected,
                result: data,
              },
            });
          }, 700);
        } else {
          Taro.kbToast({
            text: msg,
          });
        }
      },
    });
  };

  render() {
    const { list, selected, ...rest } = this.state;
    const isSelectAll = selected.length > 0 && list.length === selected.length;
    return (
      <KbPage
        {...rest}
        renderFooter={() => (
          <View className='kb-spacing-md kb-background__white'>
            <View className='at-row at-row__align--center at-row__justify--between'>
              <KbCheckbox checked={isSelectAll} onChange={this.handleSelectAll} label='全选' />
              <AtButton type='primary' size='small' circle onClick={this.handleConfirm}>
                确认出库
              </AtButton>
            </View>
          </View>
        )}
      >
        <KbLongList data={this.listData} enableMore enableRefresh>
          <View className='kb-spacing-md'>
            <View className='kb-border-radius__md'>
              {list.map((item, index) => (
                <View className='kb-spacing-md-lr kb-background__white' key={item.waybill}>
                  <View
                    className={classNames('at-row at-row__align--center at-row__justify--between', {
                      'kb-border-t': index != 0,
                    })}
                  >
                    <KbCheckbox
                      className='at-row kb-spacing-md-tb'
                      childCls='at-col'
                      checked={selected.find((i) => i.waybill == item.waybill)}
                      onChange={() => this.handleSelect(item)}
                    >
                      <View className='at-row at-row__align--center'>
                        <View className='kb-spacing-md-lr'>
                          <KbBrand brand={item.brand} size='md' />
                        </View>
                        <View className='at-col'>
                          <View className='kb-size__lg'>{item.pickupCode}</View>
                          <View className='kb-size__base'>{item.waybill}</View>
                        </View>
                      </View>
                    </KbCheckbox>
                    {item.img_url && (
                      <AtIcon
                        prefixClass='kb-icon'
                        value='pic'
                        className='kb-icon__pic'
                        onClick={() => this.handleReviewImg(item.img_url)}
                        hoverClass='kb-hover-opacity'
                      />
                    )}
                  </View>
                </View>
              ))}
            </View>
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
