/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-border-radius__md {
  overflow: hidden;
  border-radius: $border-radius-md;
}
.kb-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: $spacing-v-md;
  padding: 40px $spacing-v-md;
  padding-bottom: 60px;
  background-color: $color-white;
  .kb-tips {
    padding: $spacing-v-sm $spacing-h-md;
    color: #ff6645;
    font-size: $font-size-lg;
    background-color: #ffede9;
    border-radius: $border-radius-arc;
  }
}
.kb-icon__success {
  margin: $spacing-v-lg 0;
  color: $color-brand;
  font-size: 80px !important;
}
.kb-size__48 {
  font-size: 48px;
}
