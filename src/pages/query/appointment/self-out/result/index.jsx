/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import KbPage from '~base/components/page';
import KbBrand from '~/components/_pages/brand';
import { AtIcon } from 'taro-ui';
import classNames from 'classnames';
import dayjs from 'dayjs';
import './index.scss';

class Index extends Component {
  config = {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  };
  state = {
    list: [],
    date: '',
    result: {},
  };

  onPostMessage = (key, data) => {
    if (key == 'routerParamsChange') {
      const { params: { list = [], result } = {} } = data || {};
      this.setState({ list, result });
    }
  };

  componentDidShow() {
    this.setDate();
    this.timer = setInterval(this.setDate, 1000);
  }

  componentDidHide() {
    clearInterval(this.timer);
    this.timer = null;
  }

  setDate = () => {
    this.setState({
      date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    });
  };

  navigatorBackModal() {
    Taro.kbModal({
      title: '提示',
      content: ['请展示该页面给驿站工作人员，然后再关闭页面'],
      cancelText: '取消',
      confirmText: '继续退出',
      top: false,
      closeOnClickOverlay: false,
      closable: false,
      onConfirm: () => {
        Taro.navigator();
      },
    });
  }

  render() {
    const { list, date, result, ...rest } = this.state;
    const { fail_data = [], fail_count, sum } = result || {};
    const success_data = list.filter(
      (item) => !fail_data.find((i) => i.waybill_no == item.waybill),
    );

    return (
      <KbPage
        {...rest}
        navProps={{
          title: '自助出库',
          hideIcon: true,
        }}
        renderNavLeft={
          <View hoverClass='kb-hover' onClick={this.navigatorBackModal}>
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-color__white kb-icon-size__md kb-icon__direction-left'
            />
          </View>
        }
      >
        <View className='kb-result kb-border-radius__md'>
          <View className='kb-size__base'>{date}</View>
          <AtIcon prefixClass='kb-icon' value='success' className='kb-icon__success' />
          <View>
            共<Text className='kb-size__48 kb-color__brand'>{sum}</Text>件
          </View>
          <View className='kb-size__xl kb-size__bold kb-spacing-md-tb'>
            出库成功{success_data.length}件{fail_count > 0 && <Text>(失败{fail_count}件)</Text>}
          </View>
          <View className='kb-tips'>请展示该页面给驿站工作人员</View>
        </View>
        <View className='kb-spacing-md-lr'>
          <View className='kb-border-radius__md kb-spacing-md-lr kb-background__white'>
            {success_data.map((item, index) => (
              <View
                key={item.waybill}
                className={classNames({
                  'kb-border-t': index != 0,
                })}
              >
                <View className='at-row at-row__align--center kb-spacing-md-tb'>
                  <View className='kb-spacing-md-r'>
                    <KbBrand brand={item.brand} size='md' />
                  </View>
                  <View className='at-col '>
                    <View className='kb-size__lg'>{item.pickupCode}</View>
                    <View className='kb-size__base'>{item.waybill}</View>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
