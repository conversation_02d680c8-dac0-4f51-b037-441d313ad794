/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { extendMemo } from '@base/components/_utils';
import Taro, { Component } from '@tarojs/taro';
import DeliveryAddress from '~/components/_pages/query/appointment/delivery_address';
import { View } from '@tarojs/components';

import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '收货地址',
  };
  constructor() {
    const { postData = '{}' } = this.$router.params;

    const { addressConfig, addressInfo } = JSON.parse(postData);

    this.state = {
      addressConfig,
      addressInfo,
    };
  }

  render() {
    const { ...rest } = this.state;

    return (
      <KbPage {...rest}>
        <View className='delivery_address'>
          <DeliveryAddress {...rest} />
        </View>
      </KbPage>
    );
  }
}

export default extendMemo(Index);
