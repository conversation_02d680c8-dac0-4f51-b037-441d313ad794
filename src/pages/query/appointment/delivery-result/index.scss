/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.delivery_result {
  padding: $spacing-v-md;
  &__item {
    display: flex;
    flex-direction: column;
    gap: $spacing-v-md * 2;
    align-items: center;
    justify-content: center;
    height: 700px;
    background-color: $color-white;
    border-radius: $border-radius-md;
  }
  .at-button {
    width: 300px;
  }
}
