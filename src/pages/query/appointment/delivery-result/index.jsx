/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { extendMemo } from '@base/components/_utils';
import { View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtIcon } from 'taro-ui';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '跑腿代取',
  };
  constructor() {
    this.state = {};
  }

  navigator = () => {
    Taro.navigator({
      url: 'query',
      target: 'tabs',
    });
  };

  render() {
    const { ...rest } = this.state;

    return (
      <KbPage {...rest}>
        <View className='delivery_result'>
          <View className='delivery_result__item'>
            <AtIcon value='success' prefixClass='kb-icon' className='kb-color__brand' size={60} />
            <View className='kb-size__xl kb-color__black kb-size__bold'>支付成功</View>
            <View className='kb-size__base kb-color__grey'>已为您联系跑腿代取，请保持电话畅通</View>
            <AtButton type='secondary' onClick={this.navigator}>
              返回首页
            </AtButton>
          </View>
        </View>
      </KbPage>
    );
  }
}

export default extendMemo(Index);
