/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCheckbox from '@base/components/checkbox';
import KbCheckboxAll from '@base/components/checkbox/all';
import KbModal from '@base/components/modal';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import KbTimePicker from '@base/components/time-picker';
import {
  addressKeys,
  defaultSendStorageKey,
  getDefaultSendAddress,
} from '@/components/_pages/address/_utils';
import KbBrand from '@/components/_pages/brand';
import { formatAddress, getFormItem } from '@/components/_pages/order/_utils';
import {
  getPickUpApi,
  signExpressPaySign,
} from '@/components/_pages/query/_utils/query.appointment';
import { extendMemo } from '@base/components/_utils';
import Form from '@base/utils/form';
import { requestPaymentByPlugin } from '@/utils/qy';
import { refreshControl, REFRESH_KEY_QUERY } from '@/utils/refresh-control';
import { createListener, getPage } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import classNames from 'classnames';
import dayjs from 'dayjs';
import qs from 'qs';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import './index.scss';

const ownerTypeMaps = {
  1: '本人',
  2: '亲友包裹',
  3: '疑似你的包裹',
  4: '疑似亲友的包裹',
};
@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '送货上门',
  };
  constructor() {
    const {
      endTime = '20:00',
      startTime = '8:00',
      legMoney: money,
      dakPhone,
    } = this.$router.params || {};
    const format = 'YYYY-MM-DD';
    this.readyData = {};
    this.days = [
      {
        label: '今天',
        value: dayjs().format(format),
      },
      {
        label: '明天',
        value: dayjs().add(1, 'days').format(format),
      },
    ];
    this.state = {
      section: [startTime, endTime],
      isOpenedRes: false,
      list: [],
      form: {},
      isOpenedTimePicker: false,
      day: dayjs().format(format),
      money: parseFloat(money),
      dakPhone,
    };
    this.loadData = {};
  }

  handleCheckboxAllChange = () => {
    const {
      list,
      form: { data: { receive_selected } = [] },
    } = this.state;
    this.formIns.update({
      receive_selected: receive_selected.length === list.length ? [] : list.map((i) => i.waybill),
    });
  };
  handleSelectAddress = () => {
    // 监听地址选择：source= list|edit  列表页|编辑页
    createListener('addressSelect', ({ data, source }) => {
      let formData = data || {};
      if (source === 'edit') {
        formData = formatAddress(formData, 'send').data || {};
      }
      formData = this.updateAddress(formData);
      this.formIns.update(formData);
    });
    let params = {
      org: 'delivery',
      action: 'select',
    };
    Taro.navigator({
      url: `address?${qs.stringify({ ...params })}`,
    });
  };
  handleCheck = (waybill) => {
    const {
      form: { data: { receive_selected } = [] },
    } = this.state;
    let index = receive_selected.findIndex((i) => i === waybill);
    this.formIns.update({
      receive_selected:
        index == -1
          ? [...receive_selected, waybill]
          : receive_selected.filter((_, i) => i !== index),
    });
  };
  handleSubmit = () => {
    this.onSubmit_form();
  };
  handleTimerPicker = ({ value, type }) => {
    switch (type) {
      case 'activity':
        this.setState({ activity: value });
        break;
      case 'time':
        let data = { receive_time: value };

        if (!this.formIns) {
          this.readyData = data;
          return;
        }
        this.formIns.update(data);
        break;
      case 'day':
        this.setState({ day: value });
        break;
      case 'open':
        this.setState({
          isOpenedTimePicker: value,
        });
        break;
    }
  };
  getAllowDelivery(list) {
    return list.filter((item) => !item.isMark);
  }

  componentDidMount() {
    const { state: { list = [] } = {} } = getPage(-2);
    const { dakId } = this.$router.params || {};
    this.setState({ list: this.getAllowDelivery(list) });
    this.createForm({ dakId }).then(() => {
      getDefaultSendAddress(defaultSendStorageKey).then((data) => {
        this.formIns.update(this.updateAddress(data));
      });
    });
  }
  updateAddress = (data) => formatAddress(data, 'receive', { reverse: true }).data;
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (state) => {
    const { dakId } = state;
    return new Promise((resolve) => {
      const { remark } = this.$router.params;
      this.formIns = new Form({
        enableEmpty: false,
        api: {
          ...getPickUpApi({ dak_id: dakId, mode: 'replacePickup', need_pay: '1' }),
          formatRequest: (req) => {
            const {
              money,
              form: { data: { receive_selected } = {} },
            } = this.state;
            const {
              receive_address,
              receive_city,
              receive_district,
              receive_province,
              receive_time,
              receive_remark,
              ...formatReq
            } = req;
            return {
              ...formatReq,
              to_door_time: receive_time,
              run_errands: receive_selected.length * money,
              receive_address: receive_province + receive_city + receive_district + receive_address,
              remark: receive_remark,
            };
          },
          onThen: (res) => {
            const { code, msg, data: batch_no } = res;
            if (code === 0) {
              let params = { batch_no };
              const { money = 0, form: { data: formData = {} } = {} } = this.state;
              const { receive_selected = [] } = formData;
              params.price = money * receive_selected.length;
              params.dak_id = dakId;
              if (process.env.MODE_ENV !== 'third.pro' && process.env.MODE_ENV !== 'third.post') {
                signExpressPaySign(params)
                  .then(() => {
                    this.setState({
                      isOpenedRes: true,
                    });
                  })
                  .catch((error) => {
                    Taro.kbToast({
                      text: error,
                    });
                  });
              } else {
                let extraData = {
                  url: 'https://mp.kuaidihelp.com/api/weixin/mini/minpost/Pay/legWorkPaySign',
                  fee: params.price,
                  relationName: '驿站',
                  paymentArgs: params,
                };
                requestPaymentByPlugin(extraData).then((e) => {
                  this.timer && clearTimeout(this.timer);
                  this.timer = setTimeout(() => {
                    if (e.type === 'success') {
                      this.setState({
                        isOpenedRes: true,
                      });
                    } else {
                      const { code, msg } = (e && e.detail) || {};
                      code && Taro.kbToast({ text: msg });
                    }
                  }, 500);
                });
              }
            } else {
              Taro.kbToast({
                text: msg,
              });
            }
          },
        },
        form: getFormItem({
          keys: [...addressKeys, 'time', 'selected', 'remark'],
          data: { ...this.readyData, receive_selected: [], receive_remark: remark },
          // clean: false,
          merge: {
            receive_time: { required: true, clean: false },
            receive_selected: { required: true, clean: false },
            receive_mobile: { required: true, clean: false },
            receive_address: { required: true, clean: false },
            receive_remark: { required: false, clean: false },
          },
          prefix: 'receive',
        }),
        onReady: resolve,
      });
    });
  };
  handleNavBack = () => {
    this.setState({
      isOpenedRes: false,
    });
    refreshControl(REFRESH_KEY_QUERY);
    Taro.navigator();
  };
  render() {
    const {
      list,
      form: { data, disabled },
      activity,
      isOpenedTimePicker,
      day,
      section,
      dakPhone,
      isOpenedRes,
      money,
      ...rest
    } = this.state;
    console.log('disabled', disabled);
    const { receive_time: receiveTime, receive_selected = [], ...receive } = data || {};
    const timeCls = classNames(
      {
        'kb-color__grey kb-size__lg': !receiveTime,
      },
      'kb-spacing-lg-l',
    );
    const total = parseFloat(money * receive_selected.length).toFixed(2);
    return (
      <KbPage
        {...rest}
        renderHeader={
          <View className='kb-form kb-margin-lg'>
            <View
              className='kb-form-item kb-navigator'
              hoverClass='kb-hover'
              onClick={this.handleSelectAddress}
            >
              <View className='kb-form__receive-icon kb-margin-lg-r'>收</View>
              <View className='kb-form__receive'>
                {receive && receive.receive_mobile ? (
                  <View>
                    <View className='kb-form__receive-address kb-spacing-sm-b'>
                      {(receive.receive_province === receive.receive_city
                        ? receive.receive_city
                        : receive.receive_province + receive.receive_city) +
                        receive.receive_district +
                        receive.receive_address}
                    </View>
                    <View className='kb-form__receive-user kb-color__grey kb-size__sm'>
                      {receive.receive_name + receive.receive_mobile}
                    </View>
                  </View>
                ) : (
                  <View className='kb-color__grey kb-size__xl'>请填写收货地址</View>
                )}
              </View>
            </View>
            <View
              className='kb-form-item kb-navigator'
              hoverClass='kb-hover'
              onClick={this.handleTimerPicker.bind(this, {
                type: 'open',
                value: true,
              })}
            >
              <View className='kb-form__receive-time'>
                <View>送达时间</View>
                <View className={timeCls}>{activity ? activity : '请选择送达时间'}</View>
              </View>
            </View>
            {data.receive_remark && (
              <View className='kb-form__item kb-spacing-lg-tb kb-spacing-lg-r'>
                <View className='at-row at-row__justify--between'>
                  <View>备注：</View>
                  <View className='kb-form__receive-remark kb-text__right'>
                    {data.receive_remark}
                  </View>
                </View>
              </View>
            )}
          </View>
        }
        renderFooter={
          <View>
            <KbCheckboxAll
              confirmDisabled={disabled}
              confirmText={process.env.MODE_ENV == 'wkd' ? '提交订单' : '立即支付'}
              total={list.length}
              count={receive_selected.length}
              onConfirm={this.handleSubmit}
              onChange={this.handleCheckboxAllChange}
              renderLabel={
                <View className='kb-delivery-fee kb-size__lg kb-spacing-sm-l'>
                  <View className='kb-text__right'>
                    跑腿费
                    {receive_selected.length && money ? (
                      <Text className='kb-color__brand'>¥{total}</Text>
                    ) : money ? (
                      <Text className='kb-color__brand'>¥{money}/件</Text>
                    ) : (
                      ''
                    )}
                  </View>
                </View>
              }
            />
          </View>
        }
      >
        <View className='kb-list  kb-margin-lg-lr'>
          <View className='kb-spacing-sm kb-spacing-md-l kb-background__white'>选择代取包裹</View>
          <View className='kb-list__content '>
            <KbScrollView scrollY className='kb-scrollview' fixIosFooter={false}>
              {list.map((item) => {
                const { brand, waybill, mobile, pickupCode, owner_type } = item || {};
                const markText = owner_type != 1 ? ownerTypeMaps[owner_type] : null;
                const checked = receive_selected && receive_selected.find((i) => waybill === i);
                return (
                  <View
                    className='kb-list__sign kb-background__white'
                    key={waybill + pickupCode}
                    hoverClass='kb-hover'
                    onClick={this.handleCheck.bind(this, waybill)}
                  >
                    <View className='at-row at-row__justify--between at-row__align--center kb-size__sm kb-background-white  kb-color__greyer'>
                      <View className='at-row at-row__align--center  kb-width__auto'>
                        <KbCheckbox
                          onChange={this.handleCheck.bind(this, waybill)}
                          checked={checked}
                          className='kb-spacing-lg-r'
                        />
                        <KbBrand size='lg' brand={brand} />
                      </View>
                      <View className='kb-flex__full kb-spacing-lg-l'>
                        <View className='kb-spacing-sm-b kb-size__lg'>{waybill}</View>
                        <View className='kb-color__grey kb-size__sm'>
                          {mobile}
                          {[3, 4].includes(owner_type) && (
                            <Text className='kb-spacing-sm-l kb-color__red kb-size__sm'>
                              {markText}
                            </Text>
                          )}
                          {owner_type === 2 && (
                            <Text className='kb-margin-sm-l kb-color__brand kb-border__not-self-mobile'>
                              {markText}
                            </Text>
                          )}
                        </View>
                      </View>
                      <View className='kb-color__brand kb-size__lg'>{pickupCode}</View>
                    </View>
                  </View>
                );
              })}
            </KbScrollView>
          </View>
        </View>

        <AtFloatLayout
          isOpened={isOpenedTimePicker}
          onClose={this.handleTimerPicker.bind(this, {
            type: 'open',
            value: false,
          })}
        >
          <View className='kb-float-layout__bars'>
            <View className='layout-bars__title'>选择送件时间</View>
            <View
              className='layout-bars__confirm'
              hoverClass='kb-hover'
              onClick={this.handleTimerPicker.bind(this, {
                type: 'open',
                value: false,
              })}
            >
              确定
            </View>
          </View>
          <KbTimePicker
            section={section}
            days={this.days}
            day={day}
            time={receiveTime}
            onChange={this.handleTimerPicker}
          />
        </AtFloatLayout>
        <KbModal
          isOpened={isOpenedRes}
          onConfirm={this.handleNavBack}
          onCancel={this.handleNavBack}
          onClose={this.handleNavBack}
          confirmText='回到首页'
        >
          <View className='at-row at-row__justify--center kb-receive-resbox'>
            <View className='kb-receive-resbox--result'>
              <AtIcon
                prefixClass='kb-icon'
                value='success'
                className='kb-icon-size__xl kb-color__brand'
              />
            </View>
            <View className='kb-size__bold kb-size__xl kb-spacing-lg kb-spacing-xl-t kb-receive-resbox--title'>
              {process.env.MODE_ENV === 'wkd' ? '提交成功' : '支付成功'}
            </View>
            {process.env.MODE_ENV !== 'wkd' ? (
              <View className='kb-size__bold kb-size__lg kb-spacing-lg-tb kb-text__center'>
                共{receive_selected.length}件包裹，跑腿费{total}元
              </View>
            ) : (
              ''
            )}
          </View>

          <View className='kb-receive-resbox-content'>
            <View className=' kb-receive-resbox-content--item'>
              <Text>已为您联系跑腿代取，请保持电话通畅。</Text>
            </View>
            <View className='kb-receive-resbox-content--item'>
              <Text>如需取消,请联系驿站{dakPhone}</Text>
            </View>
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default extendMemo(Index);
