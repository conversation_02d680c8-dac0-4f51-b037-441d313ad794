/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$color-brand: #1abba1;
$color-excel-file: #3a714b;

.kb-color__brand--dark {
  color: $color-excel-file !important;
}

.kb-choose-file {
  align-items: center;
  justify-content: center;

  &,
  &__list {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: $color-white;
  }
  &__item {
    padding-top: $spacing-v-xxl * 2;
    &:first-child {
      padding-top: 0;
    }
  }
  &__tips {
    .file__tips--item {
      padding-top: $spacing-v-xs;
      &:first-child {
        padding-top: 0;
      }
    }
  }
  .at-button {
    background-color: $color-brand;
  }
  .kb-icon-file {
    font-size: 120px !important;
  }
  &__list {
    justify-content: space-between;
    .file-list {
      &__box {
        padding: 50px;
        &--title {
          margin-bottom: $spacing-v-md;
          font-weight: bold;
        }
        &--item {
          display: flex;
          align-items: center;
          margin-bottom: $spacing-v-md;
          padding: $spacing-v-md $spacing-h-md;
          background-color: $color-grey-8;
          &:last-child {
            margin-bottom: 0;
          }
        }
        &--icon {
          padding-right: $spacing-h-md;
          .kb-icon-file-excel-fill {
            font-size: 100px !important;
          }
        }
        &--desc {
          color: $color-grey-2;
        }
      }
      &__btn {
        padding: $spacing-v-md $spacing-h-md;
        .at-button--secondary {
          color: $color-brand;
          border-color: $color-brand;
        }
        .at-button--primary {
          background-color: $color-brand;
          border-color: $color-brand;
        }
      }
    }
  }
}
