/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import request from '@base/utils/request';
import { getUploadFileApis } from '@/components/_pages/address/_utils';
import { View } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';
import isArray from 'lodash/isArray';
import './index.scss';

class PageChooseFile extends Component {
  config = {
    navigationBarTitleText: process.env.MODE_ENV === 'wkd' ? '微快递' : '驿站',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black',
  };
  constructor() {
    const { count } = this.$router.params;
    this.state = {
      files: [],
      count: 1 * (count || 1),
    };
  }

  uploadFiles = (tempFiles) => {
    return Promise.all(
      tempFiles.map((item) => {
        return new Promise((resolve) => {
          const { path, name, size } = item;
          request({
            ...getUploadFileApis({ filePath: path, name }),
            requestDataType: 'file',
            toastLoading: false,
            onThen: (res) => {
              const {
                data: { path: filePath },
                code,
                msg,
              } = res;
              resolve({
                path: filePath ? `https://upload.kuaidihelp.com${filePath}` : '',
                errMsg: code > 0 ? msg : '',
                name,
                size: `${Math.floor(size / 1024)}KB`,
              });
            },
          });
        });
      }),
    );
  };

  joinStr = (list, key, str) => {
    return list
      .map((item) => item[key])
      .filter((item) => item)
      .join(str);
  };

  handleChooseMessageFile = () => {
    this.setState({ files: [] });
    const { type = 'file', extension = JSON.stringify(['xls', 'xlsx']) } = this.$router.params;
    Taro.chooseMessageFile({
      count: this.state.count,
      type,
      extension: JSON.parse(decodeURIComponent(extension)),
    }).then(({ tempFiles }) => {
      if (isArray(tempFiles)) {
        const toastIns = Taro.kbToast({
          status: 'loading',
        });
        this.uploadFiles(tempFiles).then((res) => {
          const errMsg = this.joinStr(res, 'errMsg', '；');
          const files = res.filter((item) => item.path);
          this.setState({ files });
          if (errMsg) {
            toastIns.update({
              status: 'error',
              text: errMsg,
            });
          } else {
            toastIns.close();
          }
        });
      }
    });
  };

  getAppParameter = (file) => {
    const { params } = this.$router;
    return JSON.stringify({
      ...params,
      path: file,
    });
  };

  render() {
    const { count, files, ...rest } = this.state;
    const hasFiles = files.length > 0;
    const file = this.joinStr(files, 'path', ',');
    const appParameter = this.getAppParameter(file);

    return (
      <KbPage {...rest}>
        {!hasFiles ? (
          <View className='kb-choose-file'>
            <View className='kb-choose-file__item'>
              <AtIcon prefixClass='kb-icon' value='file' className='kb-color__brand' />
            </View>
            <View className='kb-choose-file__item'>
              <AtButton type='primary' onClick={this.handleChooseMessageFile}>
                选择微信聊天中的文件
              </AtButton>
            </View>
            <View className='kb-choose-file__item kb-choose-file__tips'>
              <View className='file__tips--item'>1、支持Excel文件</View>
              <View className='file__tips--item'>2、每次最多选择{count}个文件</View>
              <View className='file__tips--item'>3、已过期的聊天文件，无法导入</View>
            </View>
          </View>
        ) : (
          <View className='kb-choose-file__list'>
            <View className='file-list__box'>
              <View className='file-list__box--title'>请确认：</View>
              <View className='file-list__box--list'>
                {files.map((item) => (
                  <View key={item.path} className='file-list__box--item'>
                    <View className='file-list__box--icon'>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='file-excel-fill'
                        className='kb-color__brand--dark'
                      />
                    </View>
                    <View className='file-list__box--info'>
                      <View>{item.name}</View>
                      <View className='file-list__box--desc'>xlsx {item.size}</View>
                    </View>
                  </View>
                ))}
              </View>
            </View>
            <View className='file-list__btn'>
              <View className='at-row'>
                <View className='at-col'>
                  <AtButton type='secondary' onClick={this.handleChooseMessageFile}>
                    重新选择文件
                  </AtButton>
                </View>
                <View className='at-col kb-spacing-md-l'>
                  <AtButton openType='launchApp' appParameter={appParameter} type='primary'>
                    打开驿站APP
                  </AtButton>
                </View>
              </View>
            </View>
          </View>
        )}
      </KbPage>
    );
  }
}

export default PageChooseFile;
