/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { Image, View } from '@tarojs/components';
import KbStoreCardUsed from '@/components/_pages/store-card/used';
import './index.scss';
import { getAdConfig } from '~/components/_pages/ad-extension/_utils';
import request from '~base/utils/request';

class PageChooseFile extends Component {
  config = {
    navigationBarTitleText: '',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black',
  };
  constructor() {
    this.state = {
      img: '',
      qrcode: '',
      dakInfo: {},
    };
  }

  handleRelationInfoUpdate = (v) => {
    this.setState(
      {
        dakInfo: v,
      },
      () => {
        this.getQrCode();
      },
    );
  };

  handleUpdate = (loginData) => {
    const { logined } = loginData || {};
    if (logined) {
      this.getAd();
    }
  };

  getQrCode = () => {
    const { dakInfo } = this.state;
    const { source } = this.$router.params || {};
    const dakId = dakInfo.cm_id || dakInfo.dak_id || dakInfo.dakId;
    const state = `${dakId}_${source}`;
    request({
      url: '/api/weixin/work/ContactMe/generateContactWay',
      data: {
        state,
      },
      onThen: (res) => {
        const { code, data, msg } = res || {};
        const { contact_qrcode } = data || {};
        if (code == 0 && contact_qrcode) {
          this.setState({
            qrcode: contact_qrcode,
          });
        } else {
          Taro.kbToast({ text: msg });
        }
      },
    });
  };

  getAd = () => {
    getAdConfig({ position: '4' }).then((res) => {
      const ad = res[0] || {};
      const url = ad.imgUrl;
      if (url) {
        this.setState({
          img: url,
        });
      }
    });
  };

  render() {
    const { qrcode, img, ...rest } = this.state;

    return (
      <KbPage {...rest} onUpdate={this.handleUpdate}>
        <View className='kb-ad_wrap'>
          <View className='kb-hidden'>
            <KbStoreCardUsed onRelationUpdate={this.handleRelationInfoUpdate} pageSource='query' />
          </View>
          <Image className='kb-ad_img' src={img} mode="widthFix" />
          <Image className='kb-ad_qrcode' src={qrcode} showMenuByLongpress />
        </View>
      </KbPage>
    );
  }
}

export default PageChooseFile;
