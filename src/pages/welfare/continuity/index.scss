/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$sprite-src: '//cdn-img.kuaidihelp.com/miniapp/miniapp_yz/welfare/Sprite/01.png';

.kb-continuity-page {
  &__signTitle {
    min-height: 350px;
    margin: 0 30px 20px;
    overflow: hidden;
    background: #fff;
    border-radius: 10px;
  }
  &__signNumber {
    padding: 20px;
    color: #000;
    font-size: 28px;
  }
  &__signBox {
    position: relative;
    margin-top: -280px;
  }
  &__box {
    display: flex;
    justify-content: space-around;
  }
  &__card {
    flex: 1;
    margin: 0 10px;
    font-size: 20px;
    text-align: center;
  }
  &__signDay {
    font-size: 20px;
    text-align: center;
  }
  &__signDayTitle {
    width: 75px;
    height: 35px;
    margin-bottom: 6px;
  }
  &__signDayHeadImg {
    color: #fff;
    font-size: 14px;
    line-height: 30px;

    background: url('//cdn-img.kuaidihelp.com/miniapp/miniapp_ttzq/continuity/tag_lq.png');
    background-repeat: no-repeat;
    background-size: 72px 30px;
  }
  &__signImg {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 65px;
    margin-bottom: 20px;
    line-height: 0.95rem;
  }
  &__signImgRed {
    width: 100%;
    height: 100%;
  }
  &__signDayImg {
    width: 75px;
    height: 65px;
    margin-bottom: 20px;

    background: url('//cdn-img.kuaidihelp.com/miniapp/miniapp_ttzq/continuity/notSign.png')
      no-repeat 50%;
    background-size: 20px;
  }
  &__signDayImgs {
    background: url('//cdn-img.kuaidihelp.com/miniapp/miniapp_ttzq/continuity/sign_lq.png')
      no-repeat 50%;
    background-size: 44px;
  }
  &__signDayImgSuccess {
    width: 55px;
    height: 55px;
    background: url('//cdn-img.kuaidihelp.com/miniapp/miniapp_ttzq/continuity/signSuccess.png');
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: 26px;
  }
  &__notes {
    padding: 20px 20px 30px;
    color: #000;
    font-size: 24px;
    text-align: center;
  }
  &__signBtn {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
  &__signBtnBag {
    width: 580px;
    height: 80px;
    color: #fff;
    font-size: 32px;
    line-height: 80px;
    background-image: linear-gradient(to right, #ff6b2a, #ff4215);
    border-radius: 50px;
  }
  &__btnBagGrey {
    color: #666666;
    background: #c9c9c9 !important;
  }

  .at-curtain {
    width: 100vw;
    height: 100vh;
    //弹窗组件
    background: rgba(0, 0, 0, 0.6);

    .txt {
      position: absolute;
      left: 50%;
      width: 100%;
      color: red;
      text-align: center;
      transform: translateX(-50%);
    }

    .success {
      top: 35px;
      font-size: 40px;
    }

    .redPackage {
      top: 110px;
      font-size: 35px;
    }

    .money {
      top: 200px;
      font-weight: 600;
      font-size: 80px;
    }

    .wallet {
      top: 460px;
      color: #fff !important;
      font-size: 33px;
    }

    .send {
      top: 550px;
      width: 60%;
      height: 100px;
      font-size: 40px;
      line-height: 100px;
      background: yellow;
      border-radius: 50px;
    }

    .head {
      top: 260px !important;
      margin-bottom: 20px;
      color: #cf7b49;
      font-weight: 500;
      font-weight: 500;
      font-size: 33px;
      letter-spacing: 3px;
    }

    .addMoney {
      top: 320px !important;
      color: red;
      font-weight: 500;
      font-size: 50px;
    }

    .know {
      top: 710px;
      height: 100px;
      color: #c75f15;
      font-weight: 500;
      font-size: 30px;
      line-height: 1rem;
      letter-spacing: 5px;
    }

    .tip {
      top: 820px;
      color: #fff;
      font-size: 20px;
    }

    .lifeFollow {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);

      &.lifeFollow {
        bottom: 100px;
        transform: scale(1.3) translateX(-37%);
      }
    }
  }

  // .adStyle{  //灯火广告
  //   width: 92% !important;
  //   margin: 2% auto 2% !important;
  // }

  .fiexdShow {
    position: fixed;
    top: 10px;
    right: 20px;
    //tips提示订阅消息弹窗
    width: 160px;
    height: 60px;
  }

  .yz_continuity {
    position: absolute;
    // 关注组件
    border: none;

    .at-button__text {
      position: fixed;
      top: 150px;
      right: 20px;
      z-index: 1;
      width: 160px;
      height: 60px;

      .subsImag {
        width: 100%;
        height: 100%;
      }
    }
  }

  .at-button {
    border: none !important;
  }

  .adBox {
    position: relative;
    width: 88% !important;
    margin: 10px auto 30px !important;
    padding: 20px;
    background: #fff;
    border-radius: 10px;
    .headBox {
      display: flex;
      justify-content: center;
      height: 80px;
      color: #000;
      font-size: 32px;
      line-height: 80px;
      text-align: center;
      border-radius: 10px 10px 0 0;
      .headTitle {
        display: flex;
        align-content: center;
        justify-content: center;
        width: 680px;
        background: #fff;
        .img {
          width: 54px;
          height: 18px;
        }
        .headLeftImg {
          transform: rotate(180deg);
        }
        .headRightImg,
        .headLeftImg {
          display: flex;
          align-items: center;
        }
        .headTxt {
          margin: 0 20px;
        }
      }
    }
    .adList {
      width: 100%;
      margin: 0 auto;
      overflow: hidden;
      border-radius: 10px;
    }
  }
  .line {
    position: absolute;
    top: 46%;
    left: -47px;
    width: 70px;
    height: 5px;
    background: #ffbfb5;
  }

  .adContainer {
    width: 100%;
  }
}
