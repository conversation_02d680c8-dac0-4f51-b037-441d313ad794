/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-continuity {
  position: relative;
  z-index: 0;
  min-height: 100vh !important;
  background: #fff;
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &__flex {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  &__background {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
  }

  &__activity {
    position: relative;
    flex: 1;
    margin: 0 30px;
    background: #fff;
    border-radius: 10px;
    .kb-scrollview {
      position: absolute;
      height: calc(100% - 92px);
    }
    &__lottery {
      position: relative;
      width: 100%;
      margin-top: 20px;
      padding: 20px 0;
      background-color: #fff;
      border-bottom: 4px solid #fcfcfc;
    }
    &__information {
      flex: 1;
      align-items: flex-end;
      padding-bottom: 20px 0;
    }
    &__allName {
      margin: 20px 0;
      font-weight: 600;
      font-size: 30px;
    }
    &__myAllName {
      margin: 20px 0;
      color: #995850;
      font-weight: 600;
      font-size: 30px;
    }
    &__ml-30 {
      margin-left: 30px;
    }
    &__description {
      display: flex;
      align-items: center;
      color: #f4d096;
      font-size: 26px;
    }
    &__lotteryBtn {
      position: absolute;
      bottom: 20px;
      width: 300px;
      height: 80px;
      color: #fff;
      line-height: 80px;
      text-align: center;
      -webkit-border-radius: 50px;
      -moz-border-radius: 50px;
      -ms-border-radius: 50px;
      -o-border-radius: 50px;
      border-radius: 50px;
    }
    &__grey {
      background: #c5c5c5;
      background: linear-gradient(to bottom, #f8f6f6, #b6b6b5);
    }
    &__pink {
      background: linear-gradient(to bottom, #ee81a7, #ea3a4d);
    }
    &__winningStatus {
      position: absolute;
      top: 200px;
      right: 10px;
      padding: 10px;
      font-size: 25px;
      -webkit-transform: rotate(-40deg);
      -moz-transform: rotate(-40deg);
      -ms-transform: rotate(-40deg);
      -o-transform: rotate(-40deg);
      transform: rotate(-40deg);
    }
    &__scrollBox {
      position: absolute;
      top: 400px;
      width: 100%;
      background: #f6f6f6;
      border-radius: 40px;
    }
    &__Card {
      background: red;
    }
    &__scroll {
      width: 96%;
      margin: 0 auto;
      background: #fff;
      border-radius: 20px;
    }
    &__cardTitle {
      margin: 0 10px;
      color: #e2723c;
      font-weight: 600;
      font-size: 22px;
    }
    &__cardTxt {
      color: #7f7f7f;
      font-size: 20px;
    }
    &__emptyBox {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 300px;
      padding: 200px 100px;
      color: #f3acaa;
      font-size: 30px;
      line-height: 50px;
      text-align: center;
    }
    &__ptpBtn {
      width: 60%;
      margin: 50px auto 0;
      background: linear-gradient(to bottom, #f9f1e4, #ee943b);
      -webkit-border-radius: 50px;
      -moz-border-radius: 50px;
      -ms-border-radius: 50px;
      -o-border-radius: 50px;
      border-radius: 50px;
    }
    &__ListParent {
      display: flex;
      align-items: center;
    }
    &__ListTop {
      width: 21%;
      padding: 30px 2%;
      font-weight: 300;
      font-size: 35px;
      text-align: center;
    }
    &__switchTab {
      color: red;
    }
    &__tops {
      padding-right: 20px;
      border-right: 2px solid black;
    }
    &__head {
      display: block;
      width: 100%;
      height: 420px;
    }
  }

  &__sign {
    margin-top: 90px;
    padding-top: 55px;
  }

  &__signTitle {
    margin: 0 30px 20px;
    padding-bottom: 40px;
    overflow: hidden;
    background: #fff;
    border-radius: 10px;
  }

  &__signNumber {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    font-size: 28px;
    line-height: 28px;
    &__title {
      color: #333;
      font-weight: 600;
    }
    .kb-switch {
      padding: unset;
    }
    .at-switch__container {
      zoom: 0.6;
    }
  }

  &__box {
    display: flex;
    justify-content: space-around;
    padding-top: 30px;
  }

  &__card {
    flex: 1;
    margin: 0 10px;
    font-size: 20px;
    text-align: center;
  }

  &__signDay {
    font-size: 20px;
    text-align: center;
  }

  &__signDayTitle {
    width: 75px;
    height: 35px;
  }

  &__signDayTitles {
    color: #fff;
    font-size: 17px;
    line-height: 35px;
    text-align: center;
    background: #ff5924;
    border-radius: 10px 10px 10px 0;
  }

  &__signImg {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    height: 95px;
    margin-bottom: 20px;
    line-height: 0.95rem;
  }

  &__sameBox {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    height: 95px;
    margin-bottom: 20px;
    line-height: 0.95rem;

    &__imgs {
      width: 60px;
      height: 60px;
    }

    &__img {
      width: 60px;
      height: 60px;
      animation: rotateLeftRigth 1s infinite;
    }
  }

  &__signDayImg {
    width: 60px;
    height: 60px;
    opacity: 0.6;
  }

  &__bigSignDayImg {
    width: 70px;
    height: 70px;
    opacity: 1;
  }

  &__signDayImgSuccess {
    width: 60px;
    height: 60px;
  }

  .at-curtain {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    // position: relative;

    .popupMoney {
      position: absolute;
      top: 120px;
      left: 0;
      width: 100%;
      color: #da5300;
      font-weight: bold;
      font-size: 40px;
      text-align: center;

      .count {
        font-size: 56px;
      }
    }
    .vipCount {
      top: 138px;
      left: 29px;
      color: #d3492e;
      .count {
        font-size: 55px;
      }
    }

    .describe {
      position: absolute;
      top: 210px;
      left: 0;
      width: 100%;
      color: #da5300;
      font-weight: bold;
      font-size: 40px;
      text-align: center;
    }

    .button {
      position: absolute;
      bottom: 170px;
      left: 50%;
      transform: translateX(-50%);
    }

    .reject {
      position: absolute;
      bottom: 100px;
      left: 0;
      width: 100%;
      color: #ffa844;
      font-size: 30px;
      text-align: center;
    }

    .agreement {
      position: absolute;
      bottom: 280px;
      left: 60%;
      display: flex;
      align-items: center;
      width: 100%;
      transform: translateX(-48%);
      zoom: 0.8;

      .at-button__wxbutton {
        color: #f6d008;
      }
    }

    .save_button {
      position: absolute;
      bottom: 95px;
      left: 50%;
      width: 90%;
      height: 100px;
      color: #cf3e2d;
      font-weight: bold;
      font-size: $font-size-xxl;
      letter-spacing: 1px;
      border: none;
      transform: translateX(-49%);
    }

    .header {
      position: absolute;
      top: 500px !important;
      left: 50%;
      width: 100%;
      color: #fbe6c3 !important;
      font-weight: 500;
      font-size: 50px;
      text-align: center;
      transform: translateX(-50%);
    }

    .center {
      position: absolute;
      top: 570px !important;
      left: 50%;
      width: 90%;
      color: #000;
      color: #fbe6c3 !important;
      font-weight: 500;
      font-size: 60px;
      text-align: center;
      transform: translateX(-50%);
    }

    .fotter {
      position: absolute;
      top: 700px !important;
      left: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 160px;
      color: #000;
      font-size: 30px;
      text-align: center;
      transform: translateX(-50%);

      &__btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60%;
        height: 80px;
        margin: 20px 0;
        color: #eb3f33;
        font-weight: 500;
        font-size: 30px;
        line-height: 82px;
        letter-spacing: 5px;
        background: #f4b54e;
        border-radius: 50px;
        box-shadow: 0 8px 0 #f32525;
      }

      &__txt {
        color: #f7cd46;
        font-weight: 600;
        font-size: 22px;
      }
    }
  }

  .kb-nav-bar__wrapper {
    display: none;
  }

  .wkd_continuity {
    align-items: unset;
    margin-bottom: 20px;
    padding: 0;
    overflow: unset;
    border: none;
  }

  .adStyle {
    width: 92% !important;
    margin: 3% auto 0 !important;
  }
}

@keyframes rotateLeftRigth {
  0% {
    transform: rotate(20deg);
  }

  50% {
    transform: rotate(-20deg);
  }

  100% {
    transform: rotate(20deg);
  }
}
