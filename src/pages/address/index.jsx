/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import { apiURLs, fixType } from '@/components/_pages/address/_utils';
import { connect } from '@tarojs/redux';
import { View, Block } from '@tarojs/components';
import { AtTabs, AtTabsPane, AtButton } from 'taro-ui';
import { getTabCurrentByType, debounce, extractData } from '@base/utils/utils';
import KbPage from '@base/components/page';
import KbSearch from '@base/components/search';
import KbCitys from '@base/components/citys';
import KbCheckboxAll from '@base/components/checkbox/all';
import KbAddressList from '@/components/_pages/address/list';
import request from '@base/utils/request';
import classNames from 'classnames';
import qs from 'qs';
import './index.scss';
import { createAddrCodeService } from '~/components/_pages/address/_utils/addrCode';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '地址薄',
  };
  constructor() {
    const { org } = this.$router.params;
    this.tabList =
      org === 'company'
        ? [
            { title: '发件人', key: 'send' },
            { title: '收件人', key: 'receive' },
          ]
        : [
            // { title: "企业", key: "company" },
            { title: '个人', key: 'person' },
          ];
    this.state = {
      isManager: true,
      active: true,
      areaOpen: false,
      total: 0,
      selectted: [],
      citys: ['全国', '全国'],
      removes: [],
      titles: {
        send: '寄件',
        receive: '收件',
        delivery: '收货',
      },
      current: getTabCurrentByType(this),
      searchData: {},
    };
    this.triggerRefresh = debounce(this.triggerRefresh);
  }

  componentDidMount() {
    this.updateTitle();
    this.triggerRefresh();
  }

  componentDidShow() {
    this.triggerRefresh();
  }

  // 数据回传
  onPostMessage = (type) => {
    switch (type) {
      case 'addressSelect':
      case 'refreshAddress':
        this.triggerRefresh();
        break;
    }
  };

  // 更新标题
  updateTitle = () => {
    const { org = 'send', pageType = '' } = this.$router.params;
    const { titles } = this.state;
    Taro.setNavigationBarTitle({
      title:
        pageType === 'addrCode'
          ? '选择收货地址'
          : org === 'company'
          ? '企业收发薄'
          : `常用${titles[org] || ''}地址`,
    });
  };

  // 地址列表组件
  // 切换标签页
  onSwitchTab = (current) => {
    this.setState({
      selectted: [],
      removes: [],
      current,
    });
  };

  // 地址拉取回调
  onGetted = (list) => {
    this.list = list;
    this.setState({
      total: list.length,
      selectted: [],
    });
  };

  onSearch = (search) => {
    this.setState({
      searchData: {
        search,
      },
    });
  };

  // 其他查询条件
  extraQuery = () => {
    const { current, citys } = this.state;
    return {
      province: citys[current],
    };
  };

  // 城市选择
  onSelectCity = (value) => {
    const { current } = this.state;
    const citys = [...this.state.citys];
    citys.splice(current, 1, value);
    this.setState(
      {
        citys,
      },
      this.triggerRefresh,
    );
  };

  onTriggerRemove = (selectted) => {
    this.setState(
      {
        selectted,
      },
      () => {
        this.onRemove('confirm');
      },
    );
  };

  // 点击删除
  onRemove = (action) => {
    const { current, selectted } = this.state;
    if (action === 'confirm') {
      // 确认删除
      const { org, type } = this.fixOrgAndType();
      request({
        url: apiURLs[type].remove,
        data: {
          type: fixType(org),
          ids: selectted.join(','),
        },
        toastError: true,
        onThen: ({ code }) => {
          if (code == 0) {
            this.setState(
              {
                removes: [],
                selectted: [],
              },
              this.triggerRefresh,
            );
          }
        },
      });
      return;
    }
    const removes = [...this.state.removes];
    removes.splice(current, 1, !removes[current]);
    this.setState({
      removes,
      selectted: [],
    });
  };

  // 点击地址项
  onClickItem = async ({ data }) => {
    const { current, removes, selectted } = this.state;
    const { action, multiple, isBatch = 1, source, pageType = '' } = this.$router.params;
    if (removes[current] || multiple === '1') {
      // 删除选择
      const { id } = data;
      let selectted_ = [...selectted];
      const index = selectted_.findIndex((item) => item === id);
      if (index >= 0 && isBatch == 1) {
        selectted_.splice(index, 1);
      } else {
        isBatch == 1 ? selectted_.push(id) : (selectted_ = [id]);
      }

      this.setState({
        selectted: selectted_,
      });
      return;
    }
    if (action === 'select' && multiple !== '1') {
      const info = {};
      source === 'ai' ? (info.current = data) : (info.data = data);
      if (pageType === 'addrCode') {
        const res = await createAddrCodeService(
          extractData(data, [
            'name',
            ['tel', 'origin_tel'],
            ['mobile', 'origin_mobile'],
            'province',
            'city',
            ['county', 'district'],
            ['detail', 'address'],
          ]),
        );
        if (!res) return;
      }
      Taro.navigator({
        post: {
          type: 'addressSelect',
          data: {
            ...info,
            source: 'list',
          },
        },
      });
    }
  };

  // org=company时修正org与type
  fixOrgAndType = () => {
    const { org = 'send' } = this.$router.params;
    const { current } = this.state;
    const { key } = this.tabList[current];
    if (org === 'company') {
      return {
        org: key,
        type: org,
      };
    }
    if (org === 'delivery') {
      return { org: 'send', type: key };
    }
    return { org, type: key };
  };

  // 新增
  onAdd = () => {
    const { action, pageType = '' } = this.$router.params;
    Taro.navigator({
      url: `address/edit?${qs.stringify({
        ...this.fixOrgAndType(),
        action,
        source: 'list',
        pageType,
      })}`,
    });
  };

  // 选择地址
  onCheckboxChange = (checked) => {
    this.setState({
      selectted: checked ? this.list.map((item) => item.id) : [],
    });
  };

  // 返回上一页或确认选择
  handleSelect = (type) => {
    const { isBatch = 1 } = this.$router.params;
    if (type === 'cancel') {
      Taro.navigator();
    } else {
      const { selectted } = this.state;
      let res = this.list.filter((item) => selectted.includes(item.id));
      isBatch != 1 && (res = res[0]);
      // 格式化地址选择
      Taro.navigator({
        post: {
          type: 'addressSelect',
          data: {
            [isBatch == 1 ? 'list' : 'current']: res,
          },
        },
      });
    }
  };

  handleListReady = (ins) => {
    this.listIns = this.listIns || {};
    this.listIns[this.state.current] = ins;
  };

  triggerRefresh = () => {
    const listIns = (this.listIns && this.listIns[this.state.current]) || {};
    if (listIns && listIns.loader) {
      this.listIns[this.state.current].loader();
    }
  };

  render() {
    const {
      isManager,
      areaOpen,
      active,
      total,
      searchData,
      current,
      titles,
      citys,
      removes,
      selectted,
      ...rest
    } = this.state;
    let { org: org_, action, multiple, isBatch = 1, pageType = '' } = this.$router.params;
    const tabCls = classNames('kb-tabs kb-tabs__hidetab');
    const count = selectted.length;
    const { org, type } = this.fixOrgAndType();
    const searchCls = classNames(
      'kb-tabs__inner--search kb-tabs__inner--search-0 at-row at-row__align--center',
    );
    const searchPlaceholder = `请输入姓名/电话/地址`;
    // 多选
    const isMultiple = multiple === '1';
    return (
      <Block>
        <KbPage
          {...rest}
          renderFooter={
            <Fragment>
              {isManager || type === 'person' ? (
                <Fragment>
                  {removes[current] ? (
                    <KbCheckboxAll
                      confirmText='删除'
                      cancelText='取消'
                      onCancel={this.onRemove.bind(this, 'cancel')}
                      onConfirm={this.onRemove.bind(this, 'confirm')}
                      total={total}
                      count={count}
                      onChange={this.onCheckboxChange}
                    />
                  ) : isMultiple ? (
                    <KbCheckboxAll
                      confirmText='确定'
                      cancelText='返回'
                      isBatch={isBatch}
                      onCancel={this.handleSelect.bind('cancel')}
                      onConfirm={this.handleSelect.bind(this, 'confirm')}
                      total={total}
                      count={count}
                      onChange={this.onCheckboxChange}
                    />
                  ) : (
                    <View className='kb-spacing-md at-row at-row__align--center kb-address__button'>
                      {total > 0 && (
                        <View className='at-col'>
                          <AtButton
                            type='secondary'
                            circle
                            onClick={this.onRemove.bind(this, 'trigger')}
                          >
                            删除
                          </AtButton>
                        </View>
                      )}
                      <View className='at-col'>
                        <AtButton type='primary' circle onClick={this.onAdd}>
                          新增
                        </AtButton>
                      </View>
                    </View>
                  )}
                </Fragment>
              ) : null}
            </Fragment>
          }
        >
          <View className={searchCls}>
            <KbCitys onChange={this.onSelectCity} value={citys[current]} />
            <View className='at-col'>
              <KbSearch placeholder={searchPlaceholder} onSearch={this.onSearch} />
            </View>
          </View>
          <AtTabs
            className={tabCls}
            current={current}
            tabList={this.tabList}
            onClick={this.onSwitchTab.bind(this)}
            swipeable={false}
          >
            <View className='kb-tabs__inner'>
              {this.tabList.map((item, index) => {
                const { key } = item;
                let fixType = key;
                let fixOrg = org;
                if (org_ === 'company') {
                  fixType = org_;
                  fixOrg = key;
                }
                return (
                  <AtTabsPane key={key} current={current} index={index}>
                    <KbAddressList
                      isManager={isManager}
                      onGetted={this.onGetted}
                      type={fixType}
                      org={fixOrg}
                      action={action}
                      multiple={multiple}
                      active={active && current === index && searchData}
                      query={this.extraQuery}
                      onClickItem={this.onClickItem}
                      onDelete={this.onTriggerRemove}
                      remove={removes[current]}
                      selectted={selectted}
                      pageType={pageType}
                      onReady={this.handleListReady}
                    />
                  </AtTabsPane>
                );
              })}
            </View>
          </AtTabs>
        </KbPage>
      </Block>
    );
  }
}

export default Index;
