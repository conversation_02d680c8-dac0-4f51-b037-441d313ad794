/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-addrCode {
  &-notice {
    display: flex;
    align-items: center;
    height: 74px;
    padding: 0 $spacing-h-md;
    color: #666666;
    font-weight: 500;
    font-size: 24px;
    background: #fff2bb;
  }
  &-list {
    padding-bottom: 40px;
    &__item {
      margin: $spacing-v-md $spacing-h-md 0;
      padding: $spacing-h-md;
      background: #fff;
      border-radius: 8px;
      .title {
        color: #333333;
        font-size: 34px;
      }
      .desc {
        margin-top: $spacing-v-sm;
        color: #9e9e9e;
        font-size: 28px;
      }
      &--action {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: $spacing-v-md;
        padding-top: $spacing-v-md;
        border-top: $width-base solid #e6e6e6;
      }
      &--btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        margin-left: $spacing-h-md;
        padding: 0 40px;
        color: #333333;
        font-size: 30px;
        background: #ffffff;
        border: $width-base solid #cccccc;
        border-radius: 30px;
      }
    }
  }
  &-footer {
    padding: $spacing-h-md;
    background: #fff;
  }
  &-add {
    height: 80px;
    font-size: 32px;
    line-height: 80px;
  }
  &-copyModal {
    .at-modal__header {
      color: $color-brand;
      font-weight: normal;
    }
  }
}

.kb-page__footer-view {
  background: #fff;
}
