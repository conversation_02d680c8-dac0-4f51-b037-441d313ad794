/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { View, Text } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtIcon } from 'taro-ui';
import KbLongList from '@base/components/long-list';
import { setClipboardData } from '~/utils/qy';
import './index.scss';
import { deleteAddrCodeService } from '~/components/_pages/address/_utils/addrCode';

@connect(({ global }) => ({
  relationInfo: global.relationInfo,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '地址码',
  };

  constructor(props) {
    super(props);

    this.state = {
      isOpened: false,
      list: undefined,
    };
    this.listData = {
      api: {
        url: '/api/weixin/mini/address/record/getAddressCodeList',
        data: {
          page_size: 20,
        },
        formatResponse: (res) => {
          const { code, data: { list = [] } = {} } = res;
          if (code == 0 && list.length > 0) {
            return {
              data: {
                list: list,
              },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (_, res) => {
          const { data: { list } = {} } = res || {};
          this.setState({
            list,
          });
        },
      },
    };
  }

  componentDidMount() {}

  // 监听
  onPostMessage = (key, data) => {
    console.log('onPostMessage', key, data);
    switch (key) {
      case 'routerParamsChange':
        break;
      case 'addressSelect':
        Taro.kbToast({
          text: '创建成功',
        });
        this.listIns.loader();
        break;
    }
  };

  handleAdd = () => {
    Taro.navigator({
      url: 'address',
      options: {
        org: 'send',
        action: 'select',
        pageType: 'addrCode',
      },
    });
  };

  onListReady = (ins) => {
    this.listIns = ins;
  };

  handleHelp = () => {
    Taro.navigateToDocument('addrCode_help');
  };

  handleDelete = (item) => {
    Taro.kbModal({
      top: false,
      title: '提示',
      content: '确定删除该地址码？',
      cancelText: '取消',
      confirmText: '删除',
      onConfirm: () => {
        return deleteAddrCodeService(item.id).then(() => {
          this.listIns.loader();
        });
      },
    });
  };

  handleCopy = (item) => {
    const mobile = [item.mobile, item.tel].filter((item) => !!item).join(' ');
    setClipboardData(
      `${item.name} ${mobile} ${item.province}${item.city}${item.county}${item.detail}${item.address_code}`,
    );
    Taro.kbModal({
      top: false,
      closable: false,
      rootClass: 'kb-addrCode-copyModal',
      title: '已复制',
      content: '将该地址填入电商平台收货地址中可避免 快递到驿站后收不到取件通知',
      confirmText: '我知道了',
    });
  };

  render() {
    const { list = [], ...rest } = this.state;

    return (
      <KbPage
        {...rest}
        renderHeader={
          <View className='kb-addrCode-notice' onClick={this.handleHelp}>
            <View className='kb-margin-sm-r'>
              收货地址中添加地址码，可避免快递放驿站后收不到取件通知
            </View>
            <AtIcon prefixClass='kb-icon' value='help' className='kb-icon-size__base' />
          </View>
        }
        renderFooter={
          <View className='kb-addrCode-footer'>
            <AtButton className='kb-addrCode-add' type='primary' circle onClick={this.handleAdd}>
              新增地址码
            </AtButton>
          </View>
        }
      >
        <KbLongList
          data={this.listData}
          onReady={this.onListReady}
          enableMore
          noDataText='暂无地址码'
        >
          <View className='kb-addrCode-list'>
            {list.length &&
              list.map((item) => {
                return (
                  <View className='kb-addrCode-list__item' key={item.id}>
                    <View className='kb-addrCode-list__item--content'>
                      <View className='title'>
                        {item.name} {item.mobile} {item.tel}
                      </View>
                      <View className='desc'>
                        {item.province}
                        {item.city}
                        {item.county}
                        {item.detail}
                        <Text className='kb-color__brand'>{item.address_code}</Text>
                      </View>
                    </View>
                    <View className='kb-addrCode-list__item--action'>
                      <View
                        className='kb-addrCode-list__item--btn'
                        onClick={() => this.handleDelete(item)}
                        hoverClass='kb-hover'
                      >
                        删除
                      </View>
                      <View
                        className='kb-addrCode-list__item--btn'
                        onClick={() => this.handleCopy(item)}
                        hoverClass='kb-hover'
                      >
                        复制
                      </View>
                    </View>
                  </View>
                );
              })}
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
