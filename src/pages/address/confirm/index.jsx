/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  addressKeysAll,
  batchCheckRealName,
  checkDataComplete,
  checkRepeatKeysBatch,
  getErrorAddressIndex,
  labelRedFn,
  receiveStorageKey,
  repeatSortAddressList,
} from '@/components/_pages/address/_utils';
import KbNoticeBar from '@base/components/notice-bar';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import KbModal from '@base/components/modal';
import { createListener, debounce, noop, triggerScrollIntoView } from '@base/utils/utils';
import { addressList } from '@/components/_pages/order/_utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import { AtButton, AtIcon, AtPagination } from 'taro-ui';
import './index.scss';

/**
 * todo
 * 1、去除重复数据功能有问题
 */
@connect(({ global }) => ({
  relationInfo: global.relationInfo,
  serviceConfig: global.serviceConfig,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '提交信息',
  };

  constructor(props) {
    super(props);

    this.state = {
      content: '',
      list: [],
      errKeys: {},
      page: 1,
      total: 0,
      repeat: 0,
      scrollTop: 0,
      openUnRealNameModal: false,
    };
    this.pageSize = 50;
    this.list = [];
    this.triggerScrollIntoView = debounce(triggerScrollIntoView, 300, {
      trailing: true,
    });

    // refs
    this.scrollViewRef = createRef();
    this.list2 = [
      {
        receive_name: '李华东',
        receive_company: '温数交区',
        receive_mobile: '14657656599',
        receive_province: '辽宁省',
        receive_city: '临汾市',
        receive_district: '东陵区',
        receive_address: '头写布老车别拉一局调县约',
        send_name: '梁明',
        send_company: '',
        send_mobile: '17563746549',
        // send_province: null,
        // send_city: null,
        // send_district: null,
        send_province: '上海',
        send_city: '上海',
        send_district: '长宁区',
        send_address: '回价收即人示老影',
        extraInfo: {
          service: {},
          goods_weight: 8,
          goods_name: '日用品',
          goods_remark: '知联用规参气林',
        },
      },
      {
        receive_name: '梁涛',
        receive_company: '习称外农当',
        receive_mobile: '18485947944',
        receive_province: '香港特别行政区',
        receive_city: '九龙',
        receive_district: '柯城区',
        receive_address: '者接展人布便需书型快水着本放织术力过',
        send_name: '姜芳',
        send_company: '外称张',
        send_mobile: '17563746549',
        send_province: '香港特别行政区',
        send_city: '九龙',
        send_district: '柯城区',
        send_address: '值战点边物面道织能信深又',
        extraInfo: {
          service: {},
          goods_weight: 2,
          goods_name: '日用品',
          goods_remark: '果关济适',
        },
      },
      {
        receive_name: '任敏',
        receive_company: '二状共时受',
        receive_mobile: '14796897674',
        receive_province: '青海省',
        receive_city: '黄冈市',
        receive_district: '萨尔图区',
        receive_address: '听战属候山大或干',
        send_name: '于霞',
        send_company: '事器应心族',
        send_mobile: '18879347684',
        send_province: '青海省',
        send_city: '黄冈市',
        send_district: '萨尔图区',
        send_address: '分对参办文与然员而方装亲业果',
        extraInfo: {
          service: {},
          goods_weight: 6,
          goods_name: '日用品',
          goods_remark: '理质住外个',
        },
      },
      {
        receive_name: '李刚',
        receive_company: '委地提何果',
        receive_mobile: '16664449489',
        receive_province: '新疆维吾尔自治区',
        receive_city: '湖州市',
        receive_district: '石狮市',
        receive_address: '形结海书量达时斗石意物图面',
        send_name: '蒋强',
        send_company: '带干查变',
        send_mobile: '14374836586',
        send_province: '新疆维吾尔自治区',
        send_city: '湖州市',
        send_district: '石狮市',
        send_address: '原参者约信程论记飞水称向日做容年',
        extraInfo: {
          service: {},
          goods_weight: 1,
          goods_name: '日用品',
          goods_remark: '准育子分状相流间',
        },
      },
      {
        receive_name: '陆丽',
        receive_company: '采斗示所复',
        receive_mobile: '14848674694',
        receive_province: '广东省',
        receive_city: '泰州市',
        receive_district: '林西县',
        receive_address: '风最清快备理调属前',
        send_name: '马明',
        send_company: '口花看开一身',
        send_mobile: '16438974666',
        send_province: '广东省',
        send_city: '泰州市',
        send_district: '林西县',
        send_address: '一果许九便高件题难',
        extraInfo: {
          service: {},
          goods_weight: 4,
          goods_name: '日用品',
          goods_remark: '标查农器基价',
        },
      },
      {
        receive_name: '金刚',
        receive_company: '满少干',
        receive_mobile: '13874375594',
        receive_province: '重庆',
        receive_city: '贵阳市',
        receive_district: '其它区',
        receive_address: '可业较条素标定为点',
        send_name: '史艳',
        send_company: '老题研接',
        send_mobile: '17987434777',
        send_province: '重庆',
        send_city: '贵阳市',
        send_district: '其它区',
        send_address: '研商得持队表',
        extraInfo: {
          service: {},
          goods_weight: 8,
          goods_name: '日用品',
          goods_remark: '如色性带边很体文',
        },
      },
      {
        receive_name: '金刚2',
        receive_company: '满少干',
        receive_mobile: '13874375594',
        receive_province: '重庆',
        receive_city: '贵阳市',
        receive_district: '其它区',
        receive_address: '可业较条素标定为点',
        send_name: '史艳',
        send_company: '老题研接',
        send_mobile: '17987434777',
        send_province: '重庆',
        send_city: '贵阳市',
        send_district: '其它区',
        send_address: '研商得持队表',
        extraInfo: {
          service: {},
          goods_weight: 8,
          goods_name: '日用品',
          goods_remark: '如色性带边很体文',
        },
      },
      {
        receive_name: '金刚3',
        receive_company: '满少干',
        receive_mobile: '13874375594',
        receive_province: '重庆',
        receive_city: '贵阳市',
        receive_district: '其它区',
        receive_address: '可业较条素标定为点',
        send_name: '史艳',
        send_company: '老题研接',
        send_mobile: '17987434777',
        send_province: '重庆',
        send_city: '贵阳市',
        send_district: '其它区',
        send_address: '研商得持队表',
        extraInfo: {
          service: {},
          goods_weight: 8,
          goods_name: '日用品',
          goods_remark: '如色性带边很体文',
        },
      },
    ];
  }

  componentDidMount() {
    let list = Taro.kbGetGlobalDataOnce(receiveStorageKey);
    console.log('list', list);
    this.updateSelectList(list);
    // this.updateSelectList(this.list2);
  }

  // 监听
  onPostMessage = (key, data) => {
    console.log('onPostMessage', key, data);
    switch (key) {
      case 'routerParamsChange':
        const { params = {} } = data;
        const { list } = params || {};
        this.updateSelectList(list);
        break;
    }
  };

  // 更新列表
  updateSelectList = async (current, force = false, then = noop) => {
    // 如果 this.activeIndex>=0 则编辑对应的数据
    const isRemoveRepeat = force === 'removeRepeat';
    if (force) {
      this.list = isRemoveRepeat ? current : await this.checkDataCompleteByList(current);
    } else if (isArray(current)) {
      const resultList = this.list;
      const checkList = await this.checkDataCompleteByList(current);
      resultList.push(...checkList);
    } else {
      // 编辑
      if (this.activeIndex >= 0) {
        const checkList = await this.checkDataCompleteByList([current]);
        this.list.splice(this.activeIndex, 1, {
          ...checkList[0],
        });
      }
    }
    // 过滤重复
    const repeat = repeatSortAddressList(this.list, isRemoveRepeat, checkRepeatKeysBatch);
    // 设置总条数
    const total = this.list.length;
    this.setState(
      {
        total,
        repeat,
      },
      () => {
        this.handlePageChange(then());
      },
    );
  };

  // 列表检测
  checkDataCompleteByList = async (list) => {
    console.log('列表检查', list);
    const { addressKeys, excludeAddressKeys } = addressKeysAll();
    const phone = list.map((i) => i.send_mobile);
    const realNameRes = await batchCheckRealName({ phone });
    if (realNameRes && realNameRes.length > 0) {
      realNameRes.map((p) => {
        const listIndex = list.findIndex((i) => i.send_mobile == p);
        if (p && listIndex > -1) {
          list[listIndex].realName = 1;
        }
      });
    }
    return list.map(({ error, ...ret }) => ({
      ...ret,
      error:
        error || checkDataComplete(ret, addressKeys, excludeAddressKeys, ['send', 'receive']).error,
    }));
  };

  // 翻页
  handlePageChange = (opts) => {
    console.log('handlePageChange.opts====>', opts);
    let { current, index = -1 } = opts || {};
    const page = current || 1;
    const start = (page - 1) * this.pageSize;
    this.setState(
      {
        page,
        list: this.list.slice(start, start + this.pageSize),
      },
      () => {
        if (index >= 0) {
          // 传入索引，则滚动到指定位置
          this.triggerScrollIntoView(
            {
              itemView: { id: `item-${index}` },
              scrollView: {
                id: this.scrollViewId,
                component: this.scrollViewRef.current,
              },
            },
            this,
          );
        }
      },
    );
  };

  // 获取对应条数的总页数
  getLastPage = (total = this.state.total) => Math.ceil(total / this.pageSize);

  handleScrollChange = ({ id }) => {
    this.scrollViewId = id;
  };

  // 列表操作
  handleClickBar = (key, e) => {
    console.log('key, e', key, e);
    const { total } = this.state;
    let navigatorOpts = null;
    let listenerKey = '';
    // 点击列表变价按钮，激活当前的索引，以便编辑后修正对应的数据
    this.activeIndex = -1;
    switch (key) {
      case 'delete':
        // 移除
        const list = [...this.list];
        list.splice(e, 1);
        Taro.kbToast({
          text: '删除成功',
        });
        this.updateSelectList(list, true);
        break;
      case 'edit':
        this.activeIndex = e;
        const { extraInfo, ...rest } = this.list[e];
        navigatorOpts = {
          url: 'address/orderEdit',
          options: {
            ...rest,
            extraInfo: encodeURIComponent(JSON.stringify(extraInfo)),
            listIndex: this.activeIndex || 0,
            total,
          },
        };
        listenerKey = 'addressSelectEdit';
        break;
      case 'realName':
        listenerKey = 'realnameBack';
        this.realnameItemData = e || {};
        navigatorOpts = {
          url: 'realname',
          options: {
            action: 'realname',
            phone: e && e.send_mobile,
          },
        };
        Taro.navigator(navigatorOpts);
        break;
      case 'unRealNameList':
        this.setState({
          openUnRealNameModal: true,
        });
        break;
    }
    if (navigatorOpts) {
      createListener(listenerKey, (data = {}) => {
        console.log(`数据监听===${listenerKey}`, data);
        let aList = data.current || data.list;
        if (listenerKey === 'realnameBack') {
          this.list.map((item) => {
            if (item.send_mobile == this.realnameItemData.send_mobile) {
              item.realName = 1;
            }
            return item;
          });
          aList = this.list;
          this.updateSelectList(aList, 'removeRepeat');
          return;
        }
        let singleFlag = !isArray(aList); //单个对象形式数据还是数组数据;
        aList = singleFlag ? [aList] : aList;
        this.updateSelectList(singleFlag ? aList[0] : aList);
      });
      Taro.navigator(navigatorOpts);
    }
  };

  // 清除重复数据
  handleCleanRepeat = async () => await this.updateSelectList(this.list, 'removeRepeat');

  // 确定选择
  handleSelect = () => {
    const list = [];
    // 防止污染
    this.list.map((item) => {
      list.push({ ...item });
    });
    // 将实名问题变成错误
    list.map((item) => {
      if (item.realName != 1) {
        item.error = 'realName';
      }
    });
    // 检查包含错误的条目
    const { index } = getErrorAddressIndex(list);
    if (index >= 0) {
      this.handlePageChange({
        index,
      });
    } else {
      console.log('提交信息', this.list);
      // if (this.list && this.list.length > 1) {
      //   setStorage({
      //     key: receiveStorageKey,
      //     data: this.list,
      //   });
      // }
      Taro.navigator({
        post: {
          index: -10,
          type: 'addressBatch',
          data: {
            list: this.list,
          },
        },
      });
    }
  };

  // 获取错误数据
  getErrorData = () => {
    if (this.list && this.list.length > 0) {
      return this.list.filter((i) => !!i.error);
    }
    return [];
  };

  // 获取未实名数据
  getUnRealNameData = () => {
    if (this.list && this.list.length > 0) {
      return this.list.filter((i, index) => {
        i.index = index;
        return i.realName != 1;
      });
    }
    return [];
  };

  render() {
    const {
      scrollTop,
      scrollIntoBottom,
      repeat,
      total,
      page,
      content,
      list,
      openUnRealNameModal,
      ...rest
    } = this.state;

    // const { index: errorIndex } = this.$router.params;
    const pageStart = (page - 1) * this.pageSize + 1;
    const errorList = this.getErrorData();
    const errorLen = errorList.length || 0;
    const unRealNameList = this.getUnRealNameData();
    const unRealNameLen = unRealNameList.length || 0;
    // console.log('list', list)
    const confirmBtnCls = classNames({
      'at-button--disabled': !!(errorLen > 0),
    });
    return (
      <KbPage {...rest} cover={false}>
        <KbScrollView
          full
          ref={this.scrollViewRef}
          relative
          scrollIntoBottom={scrollIntoBottom}
          scrollTop={scrollTop}
          onChange={this.handleScrollChange}
          renderFooter={
            <Fragment>
              <View className='kb-confirm__submit'>
                {repeat > 0 && (
                  <View className='kb-spacing-md-b'>
                    <KbNoticeBar
                      renderMore={
                        <AtButton
                          type='secondary'
                          size='small'
                          circle
                          className='kb-button__mini'
                          onClick={this.handleCleanRepeat}
                        >
                          清除
                        </AtButton>
                      }
                    >
                      {repeat}条信息重复，是否清除？
                    </KbNoticeBar>
                  </View>
                )}
                <View className='kb-spacing-md-lr'>
                  <AtButton
                    className={confirmBtnCls}
                    type='primary'
                    onClick={this.handleSelect}
                    circle
                  >
                    确定
                  </AtButton>
                </View>
              </View>
            </Fragment>
          }
        >
          {total > 0 ? (
            <View className='kb-confirm'>
              <View className='kb-margin-md kb-size__base'>
                共<Text className='kb-color__brand'>{total}</Text>条寄件信息
                {errorLen > 0 ? (
                  <Fragment>
                    ，<Text className='kb-color__brand'>{errorLen}</Text>条有误
                  </Fragment>
                ) : unRealNameLen > 0 ? (
                  <Fragment>
                    ，其中<Text className='kb-color__brand'>{unRealNameLen}</Text>条寄件人未实名
                    <Text
                      className='kb-color__brand kb-spacing-md-l'
                      onClick={() => this.handleClickBar('unRealNameList')}
                    >
                      查看详情
                    </Text>
                  </Fragment>
                ) : null}
              </View>
              <View className='kb-confirm__list'>
                <View>
                  {list.map((item, index) => {
                    const itemCls = classNames('kb-confirm__list--item', {
                      'kb-confirm__list--error': !!item.error,
                    });
                    const itemKey = `item-${index}`;
                    const listIndex = pageStart * 1 + index * 1 - 1;
                    return (
                      <View className={itemCls} id={itemKey} key={itemKey}>
                        <View className='kb-confirm__list--item--index'>{listIndex + 1}</View>
                        <View className='at-row at-row__align--center'>
                          <View className='at-col'>
                            {addressList.map((oItem, oIndex) => {
                              const name = item[`${oItem.key}_name`] || '';
                              const mobile = item[`${oItem.key}_mobile`] || '';
                              const company = item[`${oItem.key}_company`] || '';
                              const province_confidence = item[`${oItem.key}_province_confidence`];
                              const province = item[`${oItem.key}_province`] || '';
                              const city_confidence = item[`${oItem.key}_city_confidence`];
                              const city = item[`${oItem.key}_city`] || '';
                              const district_confidence = item[`${oItem.key}_district_confidence`];
                              const district = item[`${oItem.key}_district`] || '';
                              const address = item[`${oItem.key}_address`] || '';
                              return (
                                <View
                                  className={`at-row at-row__align--center ${
                                    oIndex === 1 ? 'kb-margin-md-t' : ''
                                  }`}
                                  key={oItem.key}
                                >
                                  <View className={`tag tag-${oItem.key}`}>{oItem.tag}</View>
                                  <View className='kb-size__base'>
                                    <View>
                                      <Text className='kb-spacing-sm-r'>{name}</Text>
                                      <Text>{mobile}</Text>
                                    </View>
                                    {company ? (
                                      <View className='kb-color__grey'>{company}</View>
                                    ) : null}
                                    <View className='kb-color__grey kb-confirm__list--text'>
                                      <Text className={labelRedFn(province_confidence)}>
                                        {province}
                                      </Text>
                                      <Text className={labelRedFn(city_confidence)}>{city}</Text>
                                      <Text className={labelRedFn(district_confidence)}>
                                        {district}
                                      </Text>
                                      {address}
                                    </View>
                                  </View>
                                </View>
                              );
                            })}
                          </View>
                          <View>
                            <View className='at-row at-row__align--center'>
                              <View
                                hoverClass='kb-hover-opacity'
                                className='kb-confirm__list--item--bar'
                                onClick={this.handleClickBar.bind(this, 'edit', listIndex)}
                              >
                                <AtIcon
                                  prefixClass='kb-icon'
                                  value='edit'
                                  className='kb-color__grey kb-icon-size__base'
                                />
                              </View>
                              <View
                                hoverClass='kb-hover-opacity'
                                className='kb-confirm__list--item--bar'
                                onClick={this.handleClickBar.bind(this, 'delete', listIndex)}
                              >
                                <AtIcon
                                  prefixClass='kb-icon'
                                  value='delete'
                                  className='kb-color__grey kb-icon-size__base'
                                />
                              </View>
                            </View>
                          </View>
                        </View>
                        {item.realName != 1 && (
                          <View className='kb-confirm__list--unRealName'>
                            <AtButton
                              className='kb-confirm__list--unRealName-btn'
                              type='primary'
                              size='small'
                              onClick={() => this.handleClickBar('realName', item)}
                            >
                              去实名
                            </AtButton>
                          </View>
                        )}
                      </View>
                    );
                  })}
                </View>
                {total > this.pageSize && (
                  <View className='kb-spacing-lg-b at-row at-row__justify--center'>
                    <AtPagination
                      icon
                      total={total}
                      pageSize={this.pageSize}
                      current={page}
                      onPageChange={this.handlePageChange}
                    />
                  </View>
                )}
              </View>
            </View>
          ) : null}
        </KbScrollView>
        <KbModal
          isOpened={openUnRealNameModal}
          title='温馨提示'
          top={false}
          confirmText='知道了'
          onClose={() => this.setState({ openUnRealNameModal: false })}
          onCancel={() => this.setState({ openUnRealNameModal: false })}
          onConfirm={() => this.setState({ openUnRealNameModal: false })}
        >
          <View>
            {unRealNameList.map((item) => {
              return (
                <Text
                  className='kb-unRealNameList-item'
                  key={item}
                  onClick={() => this.handleClickBar('realName', item)}
                >
                  第{item.index + 1}条;
                </Text>
              );
            })}
            <View className='kb-color__grey kb-size__base kb-margin-md-t'>
              注：点击具体条，可前往实名
            </View>
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
