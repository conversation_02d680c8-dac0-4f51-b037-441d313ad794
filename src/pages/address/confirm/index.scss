/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-confirm {
  &__submit {
    padding: $spacing-v-md 0;
    background-color: $color-grey-8;
    border-bottom: $border-lightest;
  }

  &__list {
    &--item {
      position: relative;
      display: flex;
      align-items: center;
      margin: $spacing-v-md;
      padding: $spacing-h-md 0;
      background: $color-white;
      border-radius: $border-radius-lg;
      &--index {
        padding: 0 $spacing-h-md;
      }
      &--bar {
        padding: $spacing-h-md;
      }
      .tag {
        display: inline-block;
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        margin-right: $spacing-h-md;
        overflow: hidden;
        color: $color-white;
        font-size: $font-size-sm;
        line-height: 40px;
        text-align: center;
        border-radius: $border-radius-lg;
        &-send {
          background-color: $color-brand;
        }
        &-receive {
          background-color: $color-orange;
        }
      }
    }

    &--error {
      background: #fffbe9;
    }

    &--unRealName {
      position: absolute;
      top: 10px;
      right: 10px;
      &-btn {
        height: 50px !important;
        line-height: 50px !important;
        background: $color-orange;
        border-color: $color-orange;
      }
    }
  }
}

.kb-unRealNameList {
  &-item {
    padding: 0 10px;
    color: $color-brand;
    text-decoration: underline;
  }
}
