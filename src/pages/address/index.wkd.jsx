/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import KbSearch from '@base/components/search';
import KbCheckboxAll from '@base/components/checkbox/all';
import KbAddressList from '@/components/_pages/address/list';
import KbEcodeList from '@/components/_pages/ecode/list/';
import { View, Block } from '@tarojs/components';
import { AtTabs, AtTabsPane, AtButton } from 'taro-ui';
import { triggerRefresh, getTabCurrentByType, debounce, noop } from '@base/utils/utils';
import { scanCode } from '@/utils/scan';
import { getAndAddEcode } from '@/components/_pages/ecode/_utils';
import { envNames } from '@/utils/config';
import { chooseAddress, createTabList, fixType, apiURLs } from '@/components/_pages/address/_utils';
import { refreshControl, REFRESH_KEY_ADDRESS } from '@/utils/refresh-control';
import request from '@base/utils/request';
import classNames from 'classnames';
import qs from 'qs';
import './index.scss';

@connect(({ global }) => ({
  relationInfo: global.relationInfo,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '地址薄',
  };
  constructor() {
    // customer_id需要从路由中获取，方便区分是否是从常用地址页面进入
    const { org, customer_id } = this.$router.params;
    const { relationInfo = {} } = this.props;
    const { customer = {} } = relationInfo;
    const { isCanAdmin } = customer;
    this.tabList = createTabList(this.$router.params);
    console.log('地址列表页面参数', this.$router.params);

    this.state = {
      isManager: isCanAdmin,
      active: true,
      areaOpen: false,
      total: 0,
      selectted: [],
      citys: ['全国', '全国'],
      removes: [],
      titles: {
        send: '发件',
        receive: '收件',
      },
      current: getTabCurrentByType(this),
      searchData: {},
      isShowAll: !org, // 展示所有选项
      addressType: customer_id ? 'company' : 'person', // 大客户 | 个人
    };
    this.triggerRefresh = debounce(this.triggerRefresh);
  }

  triggerRefresh = () => triggerRefresh(this);

  componentDidMount() {
    this.updateTitle();
  }

  componentDidShow() {
    if (refreshControl(REFRESH_KEY_ADDRESS, 'check')) {
      this.triggerRefresh();
    }
  }

  // 数据回传
  onPostMessage = (type) => {
    switch (type) {
      case 'refreshAddress':
        this.triggerRefresh();
        break;
    }
  };

  // 更新标题
  updateTitle = () => {
    const { org, customer_id } = this.$router.params;
    const { titles } = this.state;
    let title = `常用${titles[org] || ''}地址`;
    if (customer_id && org !== 'send' && org !== 'receive') {
      title = '企业地址列表';
    }
    Taro.setNavigationBarTitle({
      title,
    });
  };

  // 地址列表组件
  // 切换标签页
  onSwitchTab = (current) => {
    this.setState({
      selectted: [],
      removes: [],
      current,
    });
  };

  // 地址拉取回调
  onGetted = (list) => {
    this.list = list;
    this.setState({
      total: list.length,
      selectted: [],
    });
  };

  onSearch = (search) => {
    this.setState({
      searchData: {
        search,
      },
    });
  };

  // 其他查询条件
  extraQuery = () => {
    const { current, citys } = this.state;
    return {
      province: citys[current],
    };
  };

  onTriggerRemove = (selectted) => {
    this.setState(
      {
        selectted,
      },
      () => {
        this.onRemove('confirm');
      },
    );
  };

  // 点击删除
  onRemove = (action) => {
    const { customer_id } = this.$router.params;
    const { org, type } = this.fixOrgAndType();
    const { current, selectted } = this.state;
    const { remove: deleteURL } = apiURLs[type] || {};
    const isCompany = type === 'company';

    if (action === 'confirm') {
      // 确认删除
      const params = {};
      if (customer_id && isCompany) {
        params.customer_id = customer_id;
        params.id = selectted;
      } else {
        params.address_type = fixType(org);
        params.id = selectted.join(',');
      }
      request({
        url: isCompany ? deleteURL : '/v1/user_address/delete',
        data: params,
        toastError: true,
        onThen: ({ code }) => {
          if (code == 0) {
            this.setState(
              {
                removes: [],
                selectted: [],
              },
              this.triggerRefresh,
            );
          }
        },
      });
      return;
    }
    const removes = [...this.state.removes];
    removes.splice(current, 1, !removes[current]);
    this.setState({
      removes,
      selectted: [],
    });
  };

  // 点击地址项
  onClickItem = ({ data }) => {
    const { current, removes, selectted } = this.state;
    const { action, multiple, source } = this.$router.params;
    if (removes[current] || multiple === '1') {
      // 删除选择
      const { id } = data;
      const selectted_ = [...selectted];
      const index = selectted_.findIndex((item) => item === id);
      if (index >= 0) {
        selectted_.splice(index, 1);
      } else {
        selectted_.push(id);
      }

      this.setState({
        selectted: selectted_,
      });
      return;
    }
    if (action === 'select' && multiple !== '1') {
      const info = {};
      source === 'ai' ? (info.current = data) : (info.data = data);
      Taro.navigator({
        post: {
          type: 'addressSelect',
          data: {
            ...info,
            source: 'list',
          },
        },
      });
    }
  };

  // org=company时修正org与type
  fixOrgAndType = () => {
    const { current } = this.state;
    const { key } = this.tabList[current];
    const { org = key, pageType } = this.$router.params;
    if (pageType === 'company') {
      return {
        org,
        type: key,
      };
    }
    if (org === 'company') {
      return {
        org: key,
        type: org,
      };
    }
    return { org, type: 'person' };
  };

  // 扫描快递码
  handleScan = () => {
    scanCode()
      .then((result) =>
        getAndAddEcode(result)
          .then(this.ecodeListIns.loader)
          .catch((err) => Taro.kbToast({ text: err.message })),
      )
      .catch((err) => console.log(err));
  };

  handleReadyEcodeList = (ins) => {
    this.ecodeListIns = ins;
  };

  // 新增
  onAdd = () => {
    const { action, customer_id } = this.$router.params;
    Taro.navigator({
      url: `address/edit?${qs.stringify({
        ...this.fixOrgAndType(),
        action,
        customer_id,
        source: 'list',
      })}`,
    });
  };

  // 使用客户端地址库
  useSystemAddress = () => {
    chooseAddress()
      .then((data) => {
        this.onClickItem({ data });
      })
      .catch(noop);
  };

  // 选择地址
  onCheckboxChange = (checked) => {
    this.setState({
      selectted: checked ? this.list.map((item) => item.id) : [],
    });
  };

  // 返回上一页或确认选择
  handleSelect = (type) => {
    if (type === 'cancel') {
      Taro.navigator();
    } else {
      const { selectted } = this.state;
      // 格式化地址选择
      Taro.navigator({
        post: {
          type: 'addressSelect',
          data: {
            list: this.list.filter((item) => selectted.includes(item.id)),
          },
        },
      });
    }
  };

  render() {
    const {
      isManager,
      active,
      total,
      searchData,
      current,
      removes,
      selectted,
      isShowAll,
      addressType,
      ...rest
    } = this.state;
    let { action, multiple, customer_id } = this.$router.params;
    const count = selectted.length;
    const { key: currentTabKey } = this.tabList[current];
    const { org, type } = this.fixOrgAndType();

    const isHideTabBar = customer_id && org === 'send' ? false : org === 'send' && !isShowAll;
    const tabCls = classNames('kb-tabs', {
      'kb-tabs__hidetab': isHideTabBar,
    });
    const searchCls = classNames('kb-tabs__inner--search at-row at-row__align--center', {
      'kb-tabs__inner--search-0': isHideTabBar,
    });
    const searchPlaceholder = '请输入姓名/电话/地址';
    // 多选
    const isMultiple = multiple === '1';
    return (
      <Block>
        <KbPage
          {...rest}
          renderHeader={
            <View className={searchCls}>
              <View className='at-col kb-spacing-md-l'>
                <KbSearch placeholder={searchPlaceholder} onSearch={this.onSearch} />
              </View>
            </View>
          }
          renderFooter={
            <Fragment>
              {isManager || type === 'person' ? (
                currentTabKey === 'ecode' ? (
                  <View className='kb-spacing-md at-row at-row__align--center kb-address__button'>
                    <View className='at-col'>
                      <AtButton type='primary' circle onClick={this.handleScan}>
                        扫描新的快递码
                      </AtButton>
                    </View>
                  </View>
                ) : (
                  <Fragment>
                    {removes[current] ? (
                      <KbCheckboxAll
                        confirmText='删除'
                        cancelText='取消'
                        onCancel={this.onRemove.bind(this, 'cancel')}
                        onConfirm={this.onRemove.bind(this, 'confirm')}
                        total={total}
                        count={count}
                        onChange={this.onCheckboxChange}
                      />
                    ) : isMultiple ? (
                      <KbCheckboxAll
                        confirmText='确定'
                        cancelText='返回'
                        onCancel={this.handleSelect.bind('cancel')}
                        onConfirm={this.handleSelect.bind(this, 'confirm')}
                        total={total}
                        count={count}
                        onChange={this.onCheckboxChange}
                      />
                    ) : (
                      <View className='kb-spacing-md at-row at-row__align--center kb-address__button'>
                        {total > 0 && (
                          <View className='at-col'>
                            <AtButton
                              type='secondary'
                              circle
                              onClick={this.onRemove.bind(this, 'trigger')}
                              className='kb-button__middle'
                            >
                              删除
                            </AtButton>
                          </View>
                        )}
                        {action === 'select' && (
                          <View className='at-col'>
                            <AtButton
                              type='primary'
                              circle
                              onClick={this.useSystemAddress}
                              className='kb-button__middle'
                            >
                              使用{envNames[process.env.PLATFORM_ENV]}地址
                            </AtButton>
                          </View>
                        )}
                        <View className='at-col'>
                          <AtButton
                            type='primary'
                            circle
                            onClick={this.onAdd}
                            className='kb-button__middle'
                          >
                            新增
                          </AtButton>
                        </View>
                      </View>
                    )}
                  </Fragment>
                )
              ) : null}
            </Fragment>
          }
        >
          <AtTabs
            className={tabCls}
            current={current}
            tabList={this.tabList}
            onClick={this.onSwitchTab.bind(this)}
            swipeable={false}
          >
            <View className='kb-tabs__inner'>
              {this.tabList.map((item, index) => {
                const { key: tabKey } = item;
                let fixOrg = tabKey;
                let fixType_ = addressType;

                if (action === 'select') {
                  if (customer_id) {
                    fixOrg = org;
                    fixType_ = tabKey;
                  }
                }

                return (
                  <AtTabsPane key={tabKey} current={current} index={index}>
                    {tabKey === 'ecode' ? (
                      current === index && (
                        <KbEcodeList
                          type='address'
                          org={!action ? 'common_address' : ''}
                          onReady={this.handleReadyEcodeList}
                        />
                      )
                    ) : (
                      <KbAddressList
                        isManager={isManager}
                        onGetted={this.onGetted}
                        type={fixType_} // person | company
                        org={fixOrg} // send | receive | ecode | person | company
                        action={action} // select | ''
                        multiple={multiple}
                        active={active && current === index && searchData}
                        query={this.extraQuery}
                        onClickItem={this.onClickItem}
                        onDelete={this.onTriggerRemove}
                        remove={removes[current]}
                        selectted={selectted}
                        customer_id={customer_id}
                      />
                    )}
                  </AtTabsPane>
                );
              })}
            </View>
          </AtTabs>
        </KbPage>
      </Block>
    );
  }
}

export default Index;
