/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb_address_edit_default {
  display: flex;
  align-items: center;
  justify-content: start;
  &__dice {
    margin-left: 20rpx;
    color: #999;
    font-size: 30rpx;
  }
}
.kb-background {
  &__white {
    background-color: $color-white;
  }
}

.kb-address {
  &-edit {
    &__bars {
      display: flex;
      align-items: center;
      padding: $spacing-v-md $spacing-h-md;
      background: $color-white;

      &--item {
        flex-grow: 1;
        padding-right: $spacing-h-md;

        &:last-child {
          padding-right: 0;
        }
      }
    }
  }
}

.kb-aiActionTips__button {
  width: 400px !important;
  height: 70px !important;
  line-height: 70px !important;
}

.kb-margin-xxs-b {
  margin-bottom: 3px;
}
