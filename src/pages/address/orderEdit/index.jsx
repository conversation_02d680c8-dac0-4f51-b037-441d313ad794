/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import KbAddressEdit from '@/components/_pages/order/address-edit';
import KbExtraEnter from '@/components/_pages/order/extra-info/entrance';
import KbRealname from '@/components/_pages/order/realname';
import { getForm, getFormItem, goodsKeys } from '@/components/_pages/order/_utils';
import { checkDataComplete } from '@/components/_pages/address/_utils';
import { defaultFormData } from '@/components/_pages/order/_utils/order.edit';
import { extractData, jsonParse } from '@base/utils/utils';
import Form from '@base/utils/form';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  relationInfo: global.relationInfo,
  serviceConfig: global.serviceConfig,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '',
  };

  constructor(props) {
    super(props);
    const { extraInfo } = this.$router.params;
    this.extraInfoData = {
      ...defaultFormData,
      ...(extraInfo ? jsonParse(decodeURIComponent(extraInfo)) : {}),
    };
    this.state = {
      form: {
        disabled: true,
        data: {},
      },
    };
  }

  componentDidMount() {
    this.createForm(() => {
      this.setState({
        addressData: this.$router.params,
      });
      this.formIns.update(this.extraInfoData);
    });
    this.updateTitle();
  }

  // 更新标题
  updateTitle = () => {
    const { listIndex, total } = this.$router.params;
    Taro.setNavigationBarTitle({
      title: `修改订单（${(listIndex * 1 || 0) + 1}/${total}）`,
    });
  };

  // 监听
  onPostMessage = (key, e) => {
    const { data, source } = e;
    switch (key) {
      case 'routerParamsChange':
        switch (source) {
          case 'goods':
            this.handleChange({ data });
            break;
          default:
            break;
        }
        break;
    }
  };

  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onReady) => {
    const extraInfoKeys = [...goodsKeys, 'service'];
    this.formIns = new Form(
      {
        form: getFormItem({
          form: getForm(),
          keys: extraInfoKeys,
          merge: { service: { required: false, value: {} } },
        }),
        onSubmit: (req) => {
          const extraInfo = extractData(req, extraInfoKeys);
          const { error } = checkDataComplete(extraInfo, goodsKeys);
          const data = {
            current: {
              ...req,
              extraInfo: error === 'all' ? null : extraInfo,
            },
          };
          Taro.navigator({
            post: {
              type: 'addressSelectEdit',
              data,
            },
          });
        },
        onReady,
      },
      this,
    );
  };
  handleSubmit = (key) => {
    this.onSubmit_form(key);
  };
  handleChange = (e) => {
    this.formIns && this.formIns.update(e.data);
  };

  render() {
    const {
      addressData,
      form: { disabled, data },
      ...rest
    } = this.state;
    // console.log('表单数据', data);
    const { serviceConfig } = this.props;
    return (
      <KbPage
        {...rest}
        renderHeader={
          <Fragment>
            <KbRealname phone={data.send_mobile} />
          </Fragment>
        }
      >
        <KbScrollView
          renderFooter={
            <View className='kb-address-edit__bars'>
              <View className='kb-address-edit__bars--item'>
                <AtButton
                  type='primary'
                  disabled={disabled}
                  onClick={this.handleSubmit.bind(this)}
                  circle
                >
                  确定
                </AtButton>
              </View>
            </View>
          }
        >
          <View className='kb-margin-md'>
            <KbAddressEdit
              storageKey=''
              onChange={this.handleChange}
              hideInfo
              locked
              autoGps={false}
              data={addressData}
            />
          </View>
          <View className='kb-margin-md kb-margin-xl-b'>
            <KbExtraEnter data={data} serviceConfig={serviceConfig} onChange={this.handleChange} />
          </View>
        </KbScrollView>
      </KbPage>
    );
  }
}

export default Index;
