/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '~base/components/page';

class Index extends Component {
  config = {
    navigationBarTitleText: '支付',
  };

  state = {
    data: {},
  };

  componentDidMount() {
    this.loaded = true;
    try {
      const { params } = this.$router;
      const { payData } = params || {};
      const data = JSON.parse(decodeURIComponent(decodeURIComponent(payData)));
      console.log(data);
      // Taro.navigator({
      //   url: data.redirect_path,
      //   appId: data.redirect_appid,
      //   extraData: data,
      //   force: true,
      // });
      // 众安保险
      Taro.kbSetGlobalData('za-result', data);
      Taro.navigator({
        delta: 1,
      });
    } catch (error) {}
  }

  componentDidShow() {
    // if (this.loaded) {
    //   console.log('check----api');
    //   Taro.navigator({
    //     url: 'pickup/pay-cabinet/result',
    //     target: 'self',
    //   });
    // }
  }

  render() {
    const { ...rest } = this.state;

    return <KbPage {...rest} />;
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
