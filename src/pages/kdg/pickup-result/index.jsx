/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbPage from '@base/components/page';
import { intersitialAd } from '~/components/_pages/ad-extension/ad/intersitialAd';
import KbPickupResult from '~/components/_pages/kdg/pickup-result';
import KbRejectResult from '~/components/_pages/kdg/pickup-result/reject';
import { REFRESH_KEY_APPOINTMENT_LIST, refreshControl } from '~/utils/refresh-control';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '开柜结果',
  };
  constructor() {
    this.state = {};
  }

  componentDidMount() {
    refreshControl(REFRESH_KEY_APPOINTMENT_LIST);
    this.showIntersitialAd();
  }

  onPostMessage = (key, data) => {
    console.log('onPostMessage===>', key, data);
    switch (key) {
      case 'routerParamsChange':
        const { params = {} } = data || {};
        this.setState({
          routerParams: params,
        });
        break;
      default:
        break;
    }
  };

  showIntersitialAd = () => {
    // 插屏广告 一天一次
    intersitialAd({
      adId: 'adunit-62759621f5f2b337',
      storageKey: 'kdg/pickup-result/intersitialAd',
      minutes: 'day1',
    });
  };

  render() {
    const { routerParams = {}, ...rest } = this.state;
    const { isReject } = routerParams;

    return (
      <KbPage {...rest}>
        <View className='kb-pickupResult'>
          {isReject ? <KbRejectResult {...routerParams} /> : <KbPickupResult {...routerParams} />}
        </View>
      </KbPage>
    );
  }
}

export default Index;
