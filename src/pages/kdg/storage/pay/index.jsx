/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KdgStoragePay from '@/components/_pages/kdg/storage-pay';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '确认支付',
  };
  constructor() {
    const params = this.$router.params;
    this.state = {
      ...params,
    };
  }

  render() {
    const { ...rest } = this.state;
    return <KdgStoragePay {...rest} />;
  }
}

export default Index;
