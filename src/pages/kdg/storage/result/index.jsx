/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KdgStorageResult from '@/components/_pages/kdg/storage-result';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '提交结果',
    navigationStyle: 'custom',
  };
  constructor() {
    const params = this.$router.params;

    const defaultParams = {
      dakInfo:
        '{"dak_id":"","inn_name":"德沃公司Test01柜","inn_phone":"15833332222","id":"178","slave_cm_id":"3473684"}',
      device_id: 'd9c19a82-196f-3476-8227-aab79b24f821',
      order_id: '1010286110704618',
    };
    this.state = {
      ...defaultParams,
      ...params,
    };
  }

  render() {
    const { ...rest } = this.state;
    return <KdgStorageResult {...rest} />;
  }
}

export default Index;
