/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';

import KdgStorage from '@/components/_pages/kdg/storage';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '存储物品',
  };
  constructor() {
    const { dakId = '', dak_id = dakId } = this.$router.params;
    this.state = {
      dak_id,
    };
  }

  render() {
    const { ...rest } = this.state;
    return <KdgStorage {...rest} />;
  }
}

export default Index;
