/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-cabinetPay {
  box-sizing: border-box;
  height: 100%;
  padding: $spacing-v-lg $spacing-v-md;
  background-color: $color-white;
  border-radius: $border-radius-md;

  &-wrapper {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: $spacing-v-md;
  }
  &-avatar {
    width: 120px;
    height: 120px;
    margin-right: $spacing-v-xl;
  }
  .kb-margin-90 {
    margin-top: 90px;
  }
  &-money {
    color: #f74e4e;
    font-size: 42px;
  }
  &-btn {
    height: 80px;
    margin-top: 90px;
    color: #ee8a4f;
    font-size: 32px;
    line-height: 80px;
    background-color: $color-white;
    border-color: #ee8a4f;
  }
  &-ad {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100px;
    margin-top: 90px;
    overflow: hidden;
    color: $color-white;
    background-color: $color-brand;
    border-radius: $border-radius-md;
    font-size: 32px;
    &::after {
      position: absolute;
      top: -1px;
      right: -1px;
      padding: 2px $spacing-v-sm;
      color: $color-white;
      font-size: 22px;
      background-color: #f64545;
      border-top-right-radius: $border-radius-md;
      border-bottom-left-radius: 12px;
      content: '推荐';
    }
    .kb-ad-desc {
      opacity: 0.7;
      font-size: $font-size-base;
    }
  }
}
