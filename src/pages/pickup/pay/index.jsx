/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import KbButton from '@base/components/button';
import { padZero } from '@/components/_pages/kdg/_utils';
import KbCheckbox from '@base/components/checkbox';
import { connect } from '@tarojs/redux';
import CabinetOpen from '@/components/_pages/kdg/cabinetOpen';
import { get } from '~/actions/brands';
import { REFRESH_KEY_CABINET, refreshControl } from '~/utils/refresh-control';
import dayjs from 'dayjs';
import request from '~base/utils/request';
import { requestPayment } from '~/utils/qy';
import { handleCabinetPay } from '~/components/_pages/kdg/cabinetOpen/_utils/utils';
import { AUTO_OPEN_LIGHT_BAR } from '~/components/_pages/query/lights-bar/useExpressLightsBar';
import './index.scss';

@connect(
  ({ global }) => ({
    brands: global.brands,
  }),
  {
    get,
  },
)
class Index extends Component {
  static config = {
    navigationBarTitleText: '支付超时费',
  };
  constructor() {
    this.state = {
      data: {},
      selected: [],
      totalMoney: 0,
      waitPayList: [],
      dakInfo: {},
      shelfPackageInfo: null,
    };
    this.cabinetRef = createRef();
    this.cabinetRef.current = {};
  }

  componentDidMount() {
    this.props.get();
    refreshControl(REFRESH_KEY_CABINET);
  }

  onPostMessage(key, data) {
    switch (key) {
      case 'routerParamsChange':
        const { params: { list = [], current = {}, dakInfo, shelfPackageInfo } = {} } = data;
        if (shelfPackageInfo) {
          // 货架包裹
          this.setState({
            shelfPackageInfo,
            totalMoney: shelfPackageInfo.payData.money,
          });
          return;
        }
        const waitPayList = [...list];

        const isSame = (item1, item2) => {
          if (item1.grid_number == 1) {
            return item1.number === item2.number;
          }
          return item1.row === item2.row && item1.col === item2.col;
        };

        const sortByCreateTime = (a, b) => {
          // 使用dayjs比较创建时间的先后
          const timeA = dayjs(a.create_time);
          const timeB = dayjs(b.create_time);

          if (timeA.isSame(timeB)) {
            return a.waybill_no.localeCompare(b.waybill_no);
          }
          return timeA.isBefore(timeB) ? -1 : 1;
        };

        // 检查是否有与current相同格口且创建时间更早的运单
        const sameAsCurrent = list.filter(
          (v) => v.waybill_no !== current.waybill_no && isSame(v, current),
        );

        // 如果存在相同格口的运单，选择创建时间最早的作为current
        let actualCurrent = current;
        if (sameAsCurrent.length > 0) {
          const allCurrentItems = [...sameAsCurrent, current].sort(sortByCreateTime);
          actualCurrent = allCurrentItems[0];

          // 更新waitPayList，将创建时间最早的移除，保留其他的
          const remainingItems = allCurrentItems.slice(1);
          const waitPayListWithoutCurrent = waitPayList.filter(
            (item) => !allCurrentItems.some((v) => v.waybill_no === item.waybill_no),
          );
          waitPayList.length = 0;
          waitPayList.push(...waitPayListWithoutCurrent, ...remainingItems);
        }

        // 按格口分组
        const groupByLocation = waitPayList.reduce((groups, item) => {
          const key =
            item.grid_number == 1 ? `single_${item.number}` : `multi_${item.row}_${item.col}`;

          if (!groups[key]) {
            groups[key] = [];
          }
          groups[key].push(item);
          return groups;
        }, {});

        // 对每个分组内的运单按创建时间升序排序
        Object.values(groupByLocation).forEach((group) => {
          group.sort(sortByCreateTime);
        });

        // 重新组织waitPayList，保持相同格口的运单在一起
        waitPayList.length = 0;
        Object.values(groupByLocation).forEach((group) => {
          waitPayList.push(...group);
        });

        // 设置disabled状态
        waitPayList.forEach((item) => {
          // 检查与current的冲突
          const isDisabledByCurrent = isSame(item, actualCurrent);

          // 找出所有相同格口的运单
          const sameLocationItems = waitPayList.filter((v) => v !== item && isSame(item, v));

          let isDisabled = isDisabledByCurrent;

          if (sameLocationItems.length > 0) {
            // 如果不是该格口组中的第一个（创建时间最早的），则禁用
            const locationGroup =
              groupByLocation[
                item.grid_number == 1 ? `single_${item.number}` : `multi_${item.row}_${item.col}`
              ];
            if (locationGroup[0] !== item) {
              isDisabled = true;
            }
          }

          item.disabled = isDisabled;
        });

        this.setState({
          waitPayList,
          data: actualCurrent,
          totalMoney: +actualCurrent.outTimeInfo.fee,
          dakInfo,
        });
        break;
    }
  }

  handleChoose = (item) => {
    if (item.disabled) return;
    const { selected, data } = this.state;
    const { waybill_no } = item;
    const index = selected.findIndex((v) => v.waybill_no == waybill_no);
    if (index > -1) {
      selected.splice(index, 1);
    } else {
      selected.push(item);
    }
    const totalMoney = (
      selected.reduce((total, item) => total + +item.outTimeInfo.fee, 0) + +data.outTimeInfo.fee
    ).toFixed(2);
    this.setState({
      selected,
      totalMoney,
    });
  };

  handlePay = () => {
    const { selected, data, shelfPackageInfo } = this.state;
    if (shelfPackageInfo) {
      handleCabinetPay(shelfPackageInfo.payData).then(() => {
        setTimeout(() => {
          Taro.kbSetGlobalData(AUTO_OPEN_LIGHT_BAR, shelfPackageInfo.waybill);
          Taro.navigator();
        }, 300);
      });
      return;
    }
    const { device_id, waybill_no } = data;
    const params = {
      source: 'batch_pay',
      device_id,
      waybill_no: [waybill_no, selected.map((v) => v.waybill_no)].join(','),
      current: waybill_no,
    };
    this.cabinetRef.current.handleBatchPay(params);
    return;
    request({
      url: '/api/weixin/mini/DakMini/Record/cabinetPackageTimeoutPay',
      data: params,
    }).then((res) => {
      if (res.code == 0) {
        requestPayment(res.data)
          .then(() => {
            Taro.kbToast({ text: '支付成功' });
            refreshControl(REFRESH_KEY_CABINET);
            this.cabinetRef.current.handleOpenCabinet();
          })
          .catch((err) => {
            console.log(err);
            Taro.kbToast({ text: '支付失败' });
          });
      } else {
        Taro.kbToast({ text: res.msg });
      }
    });
  };

  render() {
    const { data, selected, totalMoney, waitPayList, dakInfo, shelfPackageInfo, ...rest } =
      this.state;

    return (
      <KbPage
        {...rest}
        className='kb-pickupPay__page'
        renderFooter={
          <View className='kb-background__white kb-spacing-md'>
            <View className=' at-row at-row__align--center at-row__justify--between'>
              <View className='kb-color__grey kb-size__sm'>
                <View>总计：</View>
                <View className='kb-size__xxl kb-color__red'>￥{totalMoney}</View>
              </View>
              <KbButton
                className='kb-button__middle'
                circle
                type='primary'
                onClick={this.handlePay}
              >
                立即支付
              </KbButton>
            </View>
          </View>
        }
      >
        <ScrollView scrollY className='kb-scrollview'>
          <View className='kb-pickupPay'>
            {shelfPackageInfo && (
              <View className='kb-background__white kb-margin-md-b kb-spacing-xl-tb kb-border-radius kb-spacing-80-tb'>
                <View className='kb-color__brand kb-size__lg kb-text__center'>
                  该包裹超时，请支付超时费后点亮灯条
                </View>
                <View className='kb-text__center kb-margin-md-t'>
                  存放{shelfPackageInfo.saveTime}，
                  <Text className='kb-color__grey kb-size__sm'>
                    （其中{shelfPackageInfo.out_time_config.free_hours}h为免费）
                  </Text>
                </View>
                <View className='kb-text__center kb-size__60 kb-color__red'>￥{totalMoney}</View>
              </View>
            )}
            {data.outTimeInfo && (
              <Fragment>
                <View className='kb-tips'>确认您当前在快递柜前，避免包裹丢失</View>
                <View className='kb-background__white kb-margin-md-b kb-spacing-xl-tb kb-border-radius kb-spacing-80-tb'>
                  {data.grid_number == 1 && data.number ? (
                    <View className='kb-text__center kb-color__brand kb-size__48'>
                      <Text className='kb-number'>{data.number}</Text>格口
                    </View>
                  ) : (
                    <View className='kb-size__48 at-row at-row__align--baseline at-row__justify--center'>
                      <Text className='kb-number'>{padZero(data.row)}</Text>号柜
                      <Text className='kb-number'>{padZero(data.col)}格口</Text>
                    </View>
                  )}
                  <View className='kb-text__center kb-size__lg'>
                    存放{data.outTimeInfo.saveTime}，请支付超时费
                  </View>
                  <View className='kb-size__sm kb-color__grey kb-text__center'>
                    （其中{data.outTimeInfo.free_time}h为免费）
                  </View>
                  <View className='kb-text__center kb-size__60 kb-color__red'>
                    ￥{data.outTimeInfo.fee}
                  </View>
                </View>
              </Fragment>
            )}
            {waitPayList.length && (
              <View className='kb-background__white kb-border-radius kb-spacing-md-lr'>
                <View className='kb-spacing-md-tb kb-color__black'>
                  当前柜子其他超时包裹，一起支付
                </View>
                <View className='kb-pickupPay__list'>
                  {waitPayList.map((item) => {
                    const checked = selected.findIndex((v) => v.waybill_no == item.waybill_no) > -1;
                    return (
                      <View
                        key={item.waybill_no}
                        className='at-row at-row__align--center at-row__justify--between kb-spacing-md-tb kb-border-t'
                        hoverClass='kb-hover-opacity'
                        onClick={() => this.handleChoose(item)}
                      >
                        <View>
                          <View className='kb-size__lg'>
                            {item.brandName} {item.waybill_no}
                            {item.express_phone && item.mobileType == 0 && (
                              <Text className='kb-spacing-sm-l kb-size__sm kb-color__grey'>
                                亲友包裹
                              </Text>
                            )}
                          </View>
                          <View className='kb-margin-xs-tb'>
                            {`${
                              item.grid_number == 1
                                ? `${item.number}格口`
                                : `${padZero(item.row)}号柜`
                            }`}
                          </View>
                          <View className='kb-size__sm kb-color__red'>
                            存放{item.outTimeInfo.saveTime}，超时费：￥{item.outTimeInfo.fee}
                          </View>
                        </View>
                        <KbCheckbox
                          disabled={item.disabled}
                          checked={checked}
                          onChange={() => this.handleChoose(item)}
                        />
                      </View>
                    );
                  })}
                </View>
              </View>
            )}
          </View>
        </ScrollView>
        {data.outTimeInfo && (
          <CabinetOpen
            dakInfo={dakInfo}
            data={data}
            actionRef={this.cabinetRef}
            isBatchPay
            mode='appointment'
          />
        )}
      </KbPage>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
