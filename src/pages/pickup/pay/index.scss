/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-pickupPay {
  padding: $spacing-h-md;
  &__page {
    .kb-tips {
      padding: $spacing-h-md;
      color: #ff7e27;
      background-color: #fff9df;
    }
  }
  .kb-border-t {
    border-top: $border-lightest;
  }
  .kb-border-radius {
    border-radius: $border-radius-md;
  }
  .kb-size__60 {
    font-size: 60px;
  }
  .kb-size__50 {
    font-weight: bold;
    font-size: 50px;
  }
  .kb-spacing-80-tb {
    padding-top: 80px;
    padding-bottom: 80px;
  }
  .kb-number {
    color: $color-brand;
    font-weight: bold;
    font-size: 72px;
  }
  .kb-size__48 {
    font-size: 48px;
  }
}
