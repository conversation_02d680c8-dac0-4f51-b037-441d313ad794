/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Pickup from '@/components/_pages/pickup/index/index';
import CabinetCard from '@/components/_pages/store-card/cabinet/cabinet';
import KbPage from '@base/components/page';
import Taro, { Component } from '@tarojs/taro';

import './index.scss';

class Index extends Component {
  static config = {
    navigationBarTitleText: '取件码取件',
  };

  render() {
    const { cabinetData, ...rest } = this.state;
    const data = JSON.parse(this.$router.params.data || '{}');
    return (
      <KbPage {...rest}>
        <CabinetCard lite data={data} />
        <Pickup data={data} />
      </KbPage>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
