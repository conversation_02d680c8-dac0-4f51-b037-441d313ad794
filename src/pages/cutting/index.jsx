/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View, Canvas } from '@tarojs/components';
import KbPage from '@base/components/page';
import KbImagePicker from '@base/components/image-picker';
import { CuttingCanvas } from '@/components/_pages/cutting/_utils/canvas';
import './index.scss';

class Cutting extends Component {
  config =
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          navigationBarTitleText: '选择地址部分',
          navigationBarBackgroundColor: '#000000',
          allowsBounceVertical: 'NO',
          navigationBarTextStyle: 'white',
        }
      : {
          navigationBarTitleText: '选择地址部分',
          navigationBarBackgroundColor: '#000000',
          disableScroll: true,
          navigationBarTextStyle: 'white',
        };

  constructor() {
    super(...arguments);
    const { type, isEdit } = this.$router.params;
    // 寄件页或者地址编辑页进入时1个，其他可支持5个；
    // 目前逻辑:地址编辑页进入时action有值
    this.state = {
      max: type === 'send' || isEdit === '1' ? 1 : 5,
      showClean: false,
    };
    this.showCleanMin = 3;
  }

  componentDidMount() {
    const { path } = this.$router.params;
    this.canvasIns = new CuttingCanvas({
      id: 'cutting-canvas',
      src: path,
      max: this.state.max,
    });
    // ocr响应
    this.canvasIns.onOCR((boxes) => {
      this.setState({
        showClean: boxes.length >= this.showCleanMin,
      });
    });
    this.canvasIns.onClean((len) => {
      if (len === 1) {
        this.setState({
          showClean: false,
        });
      }
    });
  }

  handleTouch = (e) => this.canvasIns.handleEvents(e);
  handleClean = () => {
    this.setState({ showClean: false });
    this.canvasIns.clean();
  };
  // 图片选择
  handleChangeImage = (files) => {
    const [filePath] = files;
    if (filePath) {
      this.canvasIns.updateImage(filePath);
    }
  };
  // 图片旋转
  handleRotate = () => this.canvasIns.rotateImage();
  // 添加识别框
  handleAddSelectionBox = () => this.canvasIns.addSelectionBox();
  // 切割图片
  handleCutImage = () => {
    this.canvasIns
      .cutImage()
      .then(this.canvasIns.ocrImage)
      .then((res) => {
        if (res.length > 0) {
          Taro.navigator({
            post: {
              data: {
                text: res.map((item) => item.text).join('\n'),
                img: res,
              },
              type: 'parseImage',
            },
          });
        }
      });
  };

  render() {
    const { showClean, max, ...rest } = this.state;

    return (
      <KbPage {...rest}>
        <View className='kb-cutting'>
          <View
            className='kb-cutting__canvas'
            onTouchStart={this.handleTouch}
            onTouchMove={this.handleTouch}
            onTouchEnd={this.handleTouch}
          >
            <Canvas
              type='2d'
              canvasId='cutting-canvas'
              id='cutting-canvas'
              className='cutting-canvas'
              disableScroll
            />
          </View>
          {showClean && (
            <View className='at-row at-row__align--center at-row__justify--between kb-cutting__clean'>
              <View>一键清除多余识别框，仅保留一个。</View>
              <View
                hoverClass='kb-hover-opacity'
                className='kb-cutting__clean--bar'
                onClick={this.handleClean}
              >
                一键清除
              </View>
            </View>
          )}
          <View className='kb-cutting__button'>
            <View className='kb-cutting__button--left'>
              <View className='at-row at-row__align--center at-row__justify--center'>
                <KbImagePicker custom onChange={this.handleChangeImage}>
                  <View className='kb-spacing-md-lr'>重选</View>
                </KbImagePicker>
                <View
                  className='kb-spacing-md-lr'
                  hoverClass='kb-hover-opacity'
                  onClick={this.handleRotate}
                >
                  旋转
                </View>
              </View>
            </View>
            <View
              className='kb-cutting__button--import'
              hoverClass='kb-hover-opacity'
              onClick={this.handleCutImage}
            >
              识别
            </View>
            {max > 1 ? (
              <View
                className='kb-cutting__button--right'
                hoverClass='kb-hover-opacity'
                onClick={this.handleAddSelectionBox}
              >
                添加识别框
              </View>
            ) : (
              <View className='kb-cutting__button--right' />
            )}
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Cutting;
