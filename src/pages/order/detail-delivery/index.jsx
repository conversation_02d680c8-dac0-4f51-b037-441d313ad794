/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import Taro, { Component } from '@tarojs/taro';
import DetailDeliveryIndex from '~/components/_pages/order/detail-delivery';
import DetailDeliveryFooter from '~/components/_pages/order/detail-delivery/footer';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '订单详情',
  };

  constructor() {
    const { params } = this.$router;
    const { order_id } = params;

    this.state = {
      orderInfo: {},
    };
    this.listData = {
      api: {
        url: '/api/weixin/mini/DakMini/HomeDeliverOrder/orderDetail',
        data: { order_id },
        nonceKey: 'third_order_id,order_id,order_random',
        onThen: (_, { data }) => {
          this.setState({
            orderInfo: data,
          });
        },
      },
    };
  }

  handleRefreshOrder = () => {
    this.listIns.loader();
  };

  // 列表加载完毕
  handleReady = (ins) => {
    this.listIns = ins;
  };

  render() {
    const { orderInfo, ...rest } = this.state;

    return (
      <KbPage
        {...rest}
        renderFooter={
          <DetailDeliveryFooter
            orderInfo={orderInfo}
            handleUpdate={this.handleRefreshOrder.bind(this)}
          />
        }
      >
        <KbLongList data={this.listData} onReady={this.handleReady}>
          <DetailDeliveryIndex
            orderInfo={orderInfo}
            handleUpdate={this.handleRefreshOrder.bind(this)}
          />
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
