/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import isNumber from 'lodash/isNumber';
import isObject from 'lodash/isObject';

// 检查是否为优惠券
export const checkIsCoupon = (type) => {
  return type === 'coupon' || type === 'yj_coupon';
};
// 检查是否为购买动作
export const checkIsBuy = (action) => action === 'buy';
// 检查是否为默认动作
export const checkIsNormal = (action) => action === 'normal';
// 检查是否为失效动作
export const checkIsInvalid = (action) => action === 'invalid';
// 筛选出禁用的券和非禁用的券
export const filterCouponList = (opts) => {
  let { list = [], price, f_weight = 0, action } = opts;
  price = Number(price);
  const noFilter = !isNumber(price);
  const isNoValid = (item) => {
    if (action === 'invalid') {
      return 'isExpired';
    }
    if (item.card_type === 'discount_card') {
      return price < f_weight ? 'discountIsNoReach' : false;
    } else {
      return isObject(item.price_condition) && Number(price) < (item.price_condition.min || 0)
        ? 'isNoReach'
        : parseFloat(item.discount_fee) > price
        ? 'isCouponExceed'
        : item.disabled_text
        ? 'disabledDec'
        : false;
    }
  };

  return {
    list: noFilter ? list : list.filter((item) => !isNoValid(item)),
    disabledList: noFilter
      ? []
      : list
          .filter((item) => isNoValid(item))
          .map((item) => {
            const status = isNoValid(item);
            const disabledMap = {
              isExceed: `支付运费少于${item.price_condition ? item.price_condition.max : 0}元可用`,
              isNoReach: `支付运费满${item.price_condition ? item.price_condition.min : 0}元可用`,
              isCouponExceed: '优惠券金额超过支付运费',
              discountIsNoReach: '手填金额未超过权益次卡抵扣首重',
              isExpired: `有效期为${item.invalid_time}`,
              disabledDec: item.disabled_text,
            };
            item.disabled_status = status;
            item.disabledDec = disabledMap[status];
            return item;
          }),
  };
};
