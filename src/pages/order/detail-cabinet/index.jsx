/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import Taro, { Component } from '@tarojs/taro';
import DetailCabinetFooter from '@/components/_pages/order/detail-cabinet/footer';
import DetailCabinetIndex from '@/components/_pages/order/detail-cabinet/index';
import './index.scss';
import { formatCabinetOrderInfo } from '@/components/_pages/order/detail-cabinet/_utils/utils';

class Index extends Component {
  config = {
    navigationBarTitleText: '订单详情',
  };

  constructor() {
    const { params } = this.$router;
    const { order_id } = params;

    this.state = {
      orderInfo: {},
    };
    this.listData = {
      api: {
        url: '/api/weixin/mini/minpost/CabinetStorage/orderDetail',
        data: { order_id },
        nonceKey: 'third_order_id,order_id,order_random',
        onThen: (_, { data }) => {
          this.setState({
            orderInfo: formatCabinetOrderInfo(data),
          });
        },
      },
    };
  }

  handleRefreshOrder = () => {
    console.info('handleRefreshOrder=======>');
    this.listIns.loader();
  };

  // 列表加载完毕
  handleReady = (ins) => {
    this.listIns = ins;
  };

  render() {
    const { orderInfo, ...rest } = this.state;

    return (
      <KbPage
        {...rest}
        renderFooter={
          <DetailCabinetFooter
            orderInfo={orderInfo}
            handleUpdate={this.handleRefreshOrder.bind(this)}
          />
        }
      >
        <KbLongList data={this.listData} onReady={this.handleReady}>
          <DetailCabinetIndex
            orderInfo={orderInfo}
            handleUpdate={this.handleRefreshOrder.bind(this)}
          />
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
