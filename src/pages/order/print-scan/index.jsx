/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { Camera, View } from '@tarojs/components';
import KbPage from '@base/components/page';
import { handleScanCode, handleError } from '@/components/_pages/order/print-scan/_utils';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  };

  constructor() {
    this.scanLock = false;
  }

  onPostMessage(key, data) {
    switch (key) {
      case 'routerParamsChange':
        const { params: { orderData } = {} } = data;
        this.setState({
          orderData,
        });
        break;
    }
  }

  // componentDidMount() {
  //   setTimeout(() => {
  //     this.handleScanCode({
  //       detail: {
  //         type: 'qrcode',
  //         result: 'https://kbydy.cn/j?dakId=2225571'
  //       }
  //     })
  //   }, 3000)
  // }

  handleScanCode = handleScanCode;

  handleError = handleError;

  render() {
    const { orderData = {}, ...rest } = this.state;
    const { relationData = {} } = orderData || {};
    return (
      <KbPage {...rest}>
        <Camera
          className='kb-printScan-camera'
          mode='scanCode'
          onScanCode={this.handleScanCode.bind(this)}
          onError={this.handleError.bind(this)}
        />
        <View className='kb-printScan-tips'>
          请扫描
          {orderData.customer_id ? '企业大客户' : relationData.dakId ? '接单驿站' : '接单快递员'}
          专属下单码后继续打印面单
        </View>
      </KbPage>
    );
  }
}
export default Index;
