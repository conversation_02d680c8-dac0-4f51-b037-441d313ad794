/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import { getBrandsTCJS } from '@/actions/tcjsBrand';
import { preloadAd } from '@/components/_pages/ad-extension/sdk';
import Footer from '@/components/_pages/customer/Footer';
import KbGuide from '@/components/_pages/guide';
import KbOfficialAccount from '@/components/_pages/official-account';
import KbBrandSwitch from '@/components/_pages/order/brand-switch';
import KbList from '@/components/_pages/order/list';
import KbListSearch from '@/components/_pages/order/list-search';
import { addOrderDetailInfo } from '@/components/_pages/order/_utils/order.detail';
import { refreshControl, REFRESH_KEY_ORDER } from '@/utils/refresh-control';
import { getShareAppMessage } from '@/utils/share';
import KbCanvas from '@base/components/canvas';
import KbPage from '@base/components/page';
import { debounce, triggerRefresh } from '@base/utils/utils';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import './index.scss';

@connect(
  ({ global }) => ({
    brands: global.brands,
    brands_tcjs: global.brands_tcjs,
  }),
  {
    dispatchGet: get,
    getBrandsTCJS,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '我的订单',
  };
  constructor() {
    this.state = {
      searchData: {},
      active: true,
      showSearch: true,
      switchBrandData: null,
      customer_id: this.$router.params.customer_id || undefined, // 212030,
      type: this.$router.params.customer_id ? 'company' : 'all',
      buttonList: [
        {
          text: '自助查件码',
          cb: this.onFooterNavigator.bind(null, 'query'),
        },
        {
          text: '返回下单',
          cb: this.onFooterNavigator.bind(null, 'index'),
        },
      ],
    };
    this.triggerRefresh = debounce(() => triggerRefresh(this));
  }

  triggerRefresh = () => triggerRefresh(this);

  componentDidMount() {
    this.props.dispatchGet();
    this.props.getBrandsTCJS();
    const { customer_id } = this.state;
    Taro.setNavigationBarTitle({
      title: customer_id ? '企业订单管理' : '我的订单',
    });
  }

  componentDidShow() {
    if (refreshControl(REFRESH_KEY_ORDER, 'check')) {
      this.triggerRefresh();
    }
    preloadAd(['order.detail']);
  }

  onPostMessage(key, data) {
    switch (key) {
      case 'routerParamsChange':
        const { params: { extra_info, type, courier_id } = {} } = data;
        if (type == 'checkUnPayOrders' && extra_info) {
          setTimeout(() => {
            this.updateSearchData({ extra_info, courier_id });
          }, 1500);
        }
        break;
    }
  }

  // 搜索信息变更
  handleSearch = (val) => {
    const { searchData } = this.state;
    this.setState({
      searchData: {
        ...searchData,
        [process.env.MODE_ENV === 'wkd' ? 'search' : 'search_value']: val,
      },
    });
  };

  // 获取列表
  onGetted = (list) => {
    this.hasList = list.length > 0;
  };

  // 跳转
  onNavigator = (url) => Taro.navigator({ url });

  getShareAppImage = (info) =>
    Taro.kbDrawImage('order.detail', addOrderDetailInfo(info, { brands: Taro.brands }));

  onShareAppMessage = getShareAppMessage;

  // 点击按钮
  onClickBars = (key) => {
    let url = '';
    switch (key) {
      case 'print':
        url = 'order/select';
        break;
      case 'export':
        url = 'order/select?action=export';
        break;
      default:
        break;
    }
    Taro.navigator({
      url,
    });
  };

  // 切换tab
  handleSwitchTab = (tabItem) => {
    const { key } = tabItem;
    this.setState({
      showSearch: key !== 'tcjs',
    });
  };

  // 大客户页脚跳转
  onFooterNavigator = (type) => {
    switch (type) {
      case 'query':
        Taro.navigator({
          url: 'customer/queryCode',
        });
        break;
      case 'index':
        Taro.navigator({
          url: 'order/edit',
          target: 'tab',
        });
        break;
      default:
        break;
    }
  };

  updateSearchData = (val, cb) => {
    const { searchData } = val;
    this.setState(
      {
        searchData: {
          ...searchData,
          ...val,
        },
      },
      cb,
    );
  };

  onReady = (ins) => {
    this.listIns = ins;
  };

  render() {
    const {
      switchBrandData,
      showSearch,
      active,
      searchData,
      type,
      customer_id,
      buttonList,
      ...rest
    } = this.state;

    return (
      <KbPage
        {...rest}
        renderHeader={
          <Fragment>
            {showSearch && <KbListSearch onSearch={this.handleSearch} />}
            <KbOfficialAccount navigateId='5' full />
          </Fragment>
        }
        renderFooter={() => (type == 'company' ? <Footer button={buttonList} /> : null)}
      >
        <KbList
          active={active}
          searchData={searchData}
          onGetted={this.onGetted}
          type={type}
          onSwitchTab={this.handleSwitchTab}
          customer_id={customer_id}
          updateSearchData={this.updateSearchData}
          onReady={this.onReady}
        />
        <KbBrandSwitch data={switchBrandData} />
        <KbCanvas toastLoading />
        <KbGuide type='collection' />
      </KbPage>
    );
  }
}

export default Index;
