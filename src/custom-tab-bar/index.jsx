import { View, Image, Fragment } from '@tarojs/components';
import classNames from 'classnames';
import { useSelector } from '@tarojs/redux';
import { useGetTabBarConfig } from '~/components/_pages/custom-tab-bar/_utils';
import {
  getTabSubscribeAction,
  tabSubscribeActionKey,
  usePatch,
} from '~/components/_pages/custom-tab-bar/_utils/patch';
import KbSubscribe from '~base/components/subscribe';
import { frequencyLimit } from '~base/utils/utils';
import './index.scss';

const CustomTabBar = () => {
  const { badgeMap, showTabBar, tabBarMap } = useSelector((state) => state.global);
  const [tabs = [], selectedColor, color] = useGetTabBarConfig();
  const { current, switchTab } = usePatch(tabs);

  const handleSwitchTab = (index) => switchTab(index);

  const onSubscribe = ({ index, action, pagePath }) => {
    action && frequencyLimit('limit', tabSubscribeActionKey + pagePath);
    handleSwitchTab(index);
  };

  const rootCls = classNames('kb-custom-tab', {
    'tab-hidden': !showTabBar,
  });

  const tabItemStyles = {
    width: `calc(100vw / ${tabs.length})`,
  };

  return (
    <View className={rootCls}>
      {tabs.map((item, index) => {
        const badgeMapItem = badgeMap[index];
        const tabBarMapItemStatus = tabBarMap[index];
        const badgeCls = badgeMapItem
          ? classNames(
              'kb-custom-tab__item-badge',
              `badge-${badgeMapItem.text ? 'text' : 'dot'}`,
              `badge-${badgeMapItem.position || 'right'}`,
              `badge-animation-${badgeMapItem.animation || 'normal'}`,
            )
          : '';
        let style = { color };
        let src = item.iconPath;
        if (current === index) {
          style = { color: selectedColor };
          src = item.selectedIconPath;
        }

        const itemCls = classNames(item.className, {
          'kb-custom-tab__item--hidden': tabBarMapItemStatus === 'hide',
        });
        const action = getTabSubscribeAction(item.pagePath);

        return (
          <View key={item.pagePath} className={itemCls}>
            <KbSubscribe
              action={action}
              className='kb-subscribe__initial'
              onSubscribe={() => onSubscribe({ index, action, pagePath: item.pagePath })}
            >
              <View
                className='at-row at-row__align--center at-row__justify--center at-row__direction--column'
                style={tabItemStyles}
              >
                <Image mode='aspectFit' src={src} className='kb-custom-tab__image' />
                {item.text && item.showText ? (
                  <View style={style} className='kb-custom-tab__text'>
                    {item.text}
                  </View>
                ) : null}
                {badgeMapItem ? (
                  <Fragment>
                    {badgeMapItem.img ? (
                      <Image
                        mode='aspectFit'
                        src={badgeMapItem.img}
                        className='kb-custom-tab-badge__image'
                      />
                    ) : (
                      <View className={badgeCls}>{badgeMapItem.text}</View>
                    )}
                  </Fragment>
                ) : null}
              </View>
            </KbSubscribe>
          </View>
        );
      })}
    </View>
  );
};

CustomTabBar.options = {
  addGlobalClass: true,
};

export default CustomTabBar;
